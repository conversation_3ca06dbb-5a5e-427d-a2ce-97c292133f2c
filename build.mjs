import * as esbuild from 'esbuild';
import * as tsup from 'tsup';

async function build() {
  const file = `./@/index.ts`;
  const dist = `./dist`;

  /**
   * @note
   * Remix stable with mjs.
   * When need build into cjs, change format into "cjs" and remove outExtension
   */
  async function buildMJS() {
    console.log(`💤Building from ${file} into mjs`)
    await esbuild.build({
      entryPoints: [file],
      packages: 'external',
      bundle: true,
      sourcemap: true,
      target: 'es2022',
      outdir: dist,
      format: 'esm',
      outExtension: { '.js': '.mjs' },
      // loader: {
      //   '.png': 'dataurl',
      //   '.jpg': 'dataurl',
      //   '.jpeg': 'dataurl',
      //   '.svg': 'dataurl',
      // },
    });
    console.log(`✅Built ${dist}/index.mjs`);
  }


  /**
   * @note
   * Emitting d.ts files is super slow for whatever reason.
   */
  async function buildMTS() {
    console.log(`💤Building from ${file} into .d.mts`)
    await tsup.build({
      entry: [file],
      format: ['esm'],
      dts: { only: true },
      outDir: dist,
      silent: true,
    });
    console.log(`✅Built ${dist}/index.d.ts`);
  }

  await Promise.all([buildMJS(), buildMTS()])
}
build();
