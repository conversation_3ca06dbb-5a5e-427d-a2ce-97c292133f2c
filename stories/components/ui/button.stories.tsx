import { Button } from '@';
import { EnvelopeOpenIcon, ReloadIcon } from '@radix-ui/react-icons';
import { action } from '@storybook/addon-actions';
import type { Meta, StoryObj } from '@storybook/react';
import { ChevronRightIcon } from 'lucide-react';

const meta: Meta<typeof Button> = {
  title: 'components/ui/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      description: 'Button variant',
      options: [
        'default',
        'secondary',
        'destructive',
        'outline',
        'ghost',
        'link',
      ],
    },
    size: {
      control: 'select',
      description: 'Button sizes',
      options: ['default', 'sm', 'lg', 'icon'],
    },
    disabled: {
      control: 'boolean',
    },
    onClick: {
      action: 'clicked',
      description: 'Function called when the default button is clicked',
    },
    children: {
      control: 'text',
      description: 'Content to be displayed inside the button',
    },
    className: {
      control: 'text',
      description: 'Custom tailwind CSS classes to apply to the button',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    onClick: action('primary click'),
    children: 'Primary button',
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    onClick: action('secondary click'),
    children: 'Secondary button',
  },
};

export const Destructive: Story = {
  args: {
    variant: 'destructive',
    onClick: action('destructive click'),
    children: 'Destructive button',
  },
};

export const Outline: Story = {
  args: {
    variant: 'outline',
    onClick: action('outline click'),
    children: 'Outline button',
  },
};
export const Ghost: Story = {
  args: {
    variant: 'ghost',
    onClick: action('ghost click'),
    children: 'Ghost button',
  },
};
export const Link: Story = {
  args: {
    variant: 'link',
    onClick: action('link click'),
    children: 'Link button',
  },
};

export const Icon: Story = {
  args: {
    variant: 'outline',
    size: 'icon',
    onClick: action('icon click'),
    children: <ChevronRightIcon className="h-4 w-4" />,
  },
};

export const WithIcon: Story = {
  args: {
    onClick: action('with icon click'),
    children: (
      <>
        <EnvelopeOpenIcon className="mr-2 h-4 w-4" /> Login with Email
      </>
    ),
  },
};

export const Loading: Story = {
  args: {
    disabled: true,
    children: (
      <>
        <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
        Please wait
      </>
    ),
  },
};

export const AsChild: Story = {
  args: {
    onClick: action('As child click'),
    asChild: true,
    children: <>As child</>,
  },
};
