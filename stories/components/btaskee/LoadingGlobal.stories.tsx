import { LoadingGlobal } from '@';
import type { Meta, StoryObj } from '@storybook/react';
import {
  reactRouterParameters,
  withRouter,
} from 'storybook-addon-remix-react-router';

const meta: Meta<typeof LoadingGlobal> = {
  title: 'components/btaskee/LoadingGlobal',
  component: LoadingGlobal,
  parameters: {
    layout: 'centered',
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { userId: '42' },
      },
      routing: { path: '/users/:userId' },
    }),
  },
  decorators: [withRouter],
  tags: ['autodocs'],
  argTypes: {},
};

export default meta;
type Story = StoryObj<typeof meta>;

const Render = () => {
  return <LoadingGlobal />;
};

export const Default: Story = {
  render: () => <Render />,
};
