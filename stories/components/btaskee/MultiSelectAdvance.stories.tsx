import { MultiSelectAdvance } from '@';
import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta<typeof MultiSelectAdvance> = {
  title: 'components/btaskee/MultiSelectAdvance',
  component: ({ ...props }) => {
    return <MultiSelectAdvance {...props} />;
  },
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    defaultValue: {
      control: 'object',
      description: 'Array efault value',
    },
    onValueChange: {
      control: 'object',
      description: 'On change event',
    },
    options: {
      control: 'object',
      description: 'Options radio',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const Render = () => {
  return (
    <MultiSelectAdvance
      defaultValue={['test2', 'test3']}
      onValueChange={() => {}}
      options={[
        {
          label: 'Default value',
          value: 'test1',
        },
        {
          label: 'AAAAA',
          value: 'test2',
        },
        {
          label: 'BBBB',
          value: 'test3',
        },
        {
          label: 'CCCC',
          value: 'test4',
        },
      ]}
    />
  );
};

export const Default: Story = {
  render: () => <Render />,
};
