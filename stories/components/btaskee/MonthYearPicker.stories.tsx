import { MonthYearPicker } from '@/components/btaskee/MonthYearPicker';
import type { Meta, StoryObj } from '@storybook/react';
import { momentTz } from 'btaskee-utils';

const meta: Meta<typeof MonthYearPicker> = {
  title: 'components/btaskee/MonthYearPicker',
  component: ({ ...props }) => {
    return <MonthYearPicker {...props} />;
  },
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    initialDateFrom: {
      control: 'object',
      description: 'Initial date from',
    },
    initialDateTo: {
      control: 'object',
      description: 'Initial date to',
    },
    maxDate: {
      control: 'object',
      description: 'Max date',
    },
    minDate: {
      control: 'object',
      description: 'Min date',
    },
    onSelectMonth: {
      control: false,
    },
    onWholeYearSelect: {
      control: false,
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    initialDateFrom: momentTz().startOf('month').toDate(),
    initialDateTo: momentTz().endOf('month').toDate(),
    variant: {
      chevrons: 'outline',
    },
    maxDate: momentTz().endOf('month').toDate(),
    minDate: momentTz('2015-01-01').startOf('year').toDate(),
    onSelectMonth: (selectedMonth: Date) => {
      // Do something
      console.log('onSelectMonth', selectedMonth);
    },
    onWholeYearSelect: (selectedYear: Date) => {
      // Do something
      console.log('onWholeYearSelect', selectedYear);
    },
  },
};
