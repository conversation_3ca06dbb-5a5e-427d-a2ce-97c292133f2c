import { DropdownMenuBase } from '@';
import type { Meta, StoryObj } from '@storybook/react';
import {
  reactRouterParameters,
  withRouter,
} from 'storybook-addon-remix-react-router';

const meta: Meta<typeof DropdownMenuBase> = {
  title: 'components/btaskee/DropdownMenuBase',
  component: DropdownMenuBase,
  parameters: {
    layout: 'centered',
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { userId: '42' },
      },
      routing: { path: '/users/:userId' },
    }),
  },
  decorators: [withRouter],
  tags: ['autodocs'],
  argTypes: {
    links: {
      control: 'object',
      description: 'link',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    links: [
      {
        link: 'http://localhost:6006',
        label: 'Menu 1',
      },
      {
        link: 'http://localhost:6006',
        label: 'Menu 2',
      },
      {
        link: 'http://localhost:6006',
        label: 'Menu 3',
      },
      {
        link: 'http://localhost:6006',
        label: 'Menu 4',
      },
      {
        link: 'http://localhost:6006',
        label:
          'Menu 1ong content: xxx xxx xxx xxx xxx xxx xxx xxx xxx xxx xxx xxx',
      },
    ],
  },
};
