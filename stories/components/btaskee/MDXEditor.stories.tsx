import '@mdxeditor/editor/style.css';
import { RichTextComponent } from '@/components/btaskee/RichTextComponent';
import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta<typeof RichTextComponent> = {
  title: 'components/btaskee/RichTextComponent',
  component: RichTextComponent,
  parameters: {
    layout: 'centered',
  },
  decorators: [
    (Story) => (
      <div className="prose prose-sm md:prose-base lg:prose-lg dark:prose-invert max-w-none">
        <Story />
      </div>
    ),
  ],
  tags: ['autodocs'],
  argTypes: {
    markdown: {
      control: 'text',
      description: 'The markdown content to display/edit',
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text when content is empty',
    },
    readOnly: {
      control: 'boolean',
      description: 'Whether the editor is in read-only mode',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    markdown: `Hôm qua đi làm về rồi em ạ anh có biết đâu mà 




Anh ơ tiếng nữa là em có biết không anh không biết woori em HAGJFHDJFBBFKDJDB

Anh có đi`,
    placeholder: 'Write your content here...',
    readOnly: false,
  },
};

export const ReadOnly: Story = {
  args: {
    markdown: '',
    readOnly: true,
  },
};

export const Empty: Story = {
  args: {
    markdown: '',
    placeholder: 'Custom placeholder text...',
  },
};