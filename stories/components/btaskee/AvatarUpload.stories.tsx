import { AvatarUpload, ToasterBase } from '@';
import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta<typeof AvatarUpload> = {
  title: 'components/btaskee/AvatarUpload',
  component: ({ ...props }) => {
    return (
      <>
        <ToasterBase />
        <AvatarUpload {...props} />
      </>
    );
  },
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    onFileChange: {
      control: 'object',
      description: 'control on file change',
    },
    maxContentLength: {
      control: 'number',
      description: 'Max size',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onFileChange: () => {},
    maxContentLength: 512 * 1024 // 512 KB
  },
};
