import { DateTimePicker } from '@';
import { zodResolver } from '@hookform/resolvers/zod';
import type { Meta, StoryObj } from '@storybook/react';
import { momentTz } from 'btaskee-utils';
import { useForm, FormProvider } from 'react-hook-form';
import { z } from 'zod';

const DateTimePickerWrapper = ({ ...props }) => {
  const schema = z.object({
    dateTime: z.object({
      from: z.date(),
      to: z.date(),
    }),
  });
  const methods = useForm({
    resolver: zodResolver(schema),
    defaultValues: {
      dateTime: {
        from: momentTz().startOf('day').toDate(),
        to: momentTz().endOf('day').toDate(),
      },
    },
  });
  return (
    <FormProvider {...methods}>
      <DateTimePicker name="dateTime" form={methods} {...props} />
    </FormProvider>
  );
};

const meta: Meta<typeof DateTimePicker> = {
  title: 'components/btaskee/DateTimePicker',
  component: DateTimePickerWrapper,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    name: {
      control: 'text',
      description: 'Name of the form field',
    },
    showTime: {
      control: 'boolean',
      description: 'Whether to show time picker',
    },
    label: {
      control: 'text',
      description: 'Label for the date picker',
    },
    formatString: {
      control: 'text',
      description: 'Format string for date display',
    },
    mode: {
      control: 'select',
      options: ['default', 'dayMonth'],
      description: 'Mode of the date picker',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    name: 'dateTime',
    showTime: true,
    label: 'Select Date and Time',
    formatString: 'LLL dd, y HH:mm:ss',
    mode: 'default',
  },
};

export const DayMonth: Story = {
  args: {
    name: 'dateTime',
    showTime: false,
    label: 'Select Date',
    formatString: 'MMM dd',
    mode: 'dayMonth',
  },
};
