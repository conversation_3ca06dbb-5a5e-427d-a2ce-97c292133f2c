import { BTaskeeTableV2 } from '@/components/btaskee/BTaskeeTable';
import { DataTableColumnHeader } from '@/components/btaskee/table-data/data-table-column-header';
import { Button } from '@/components/ui/button';
import type { Meta, StoryObj } from '@storybook/react';
import type { Row } from '@tanstack/react-table';
import { createColumnHelper } from '@tanstack/react-table';
import { getRangeByPresetOptions, momentTz } from 'btaskee-utils';
import { ChevronsDownUp, ChevronsUpDown, X } from 'lucide-react';
import { useCallback } from 'react';
import {
  reactRouterParameters,
  withRouter,
} from 'storybook-addon-remix-react-router';

import { users } from '../../mock/data/Users';
import { USER_STATUS } from '../../mock/enums/user';
import type { IUser } from '../../mock/types/user';
import { usePaginatedData } from '../../utils/pagination';
import { DATE_RANGE_OPTIONS } from 'btaskee-constants';

const meta: Meta<typeof BTaskeeTableV2<Partial<IUser>, 'name' | 'age'>> = {
  title: 'components/btaskee/BTaskeeTableV2',
  component: BTaskeeTableV2,
  decorators: [withRouter],
  parameters: {
    layout: 'centered',
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { role: 'SWE' },
      },
      routing: { path: '/user-group/:role', useStoryElement: true },
    }),
  },
  tags: ['autodocs'],
  argTypes: {
    columns: {
      control: 'object',
      description: 'Table columns definition',
    },
    data: {
      control: 'object',
      description: 'Table data',
    },
    total: {
      control: 'number',
      description: 'Total number of records',
    },
    pagination: {
      control: 'object',
      description: 'Pagination state',
    },
    searchInput: {
      control: 'object',
      description: 'Search configuration',
    },
    initialFilterDate: {
      control: 'object',
      description: 'Date filter configuration',
    },
    initialFilters: {
      control: 'object',
      description: 'Filters configuration',
    },
    overrideColumnVisibility: {
      control: 'object',
      description: 'Column visibility state',
    },
    pinColumns: {
      control: 'object',
      description: 'Column pinning state',
    },
    renderExtraComponent: {
      control: 'object',
      description: 'Extra content to render',
    },
    renderExtraToolbarComponent: {
      control: 'object',
      description: 'Extra content to render in the toolbar',
    },
    onDoubleClickRow: {
      description: 'Function to handle row double-click',
    },
    renderExpandComponent: {
      description: 'Function to render sub-component for a row',
    },
    getRowCanExpand: {
      description: 'Function to determine if a row can expand',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    columns: [
      { accessorKey: 'name', header: 'Name' },
      { accessorKey: 'age', header: 'Age' },
    ],
    data: users,
    total: users.length,
    pagination: { pageIndex: 0, pageSize: 10 },
  },
  render: function DefaultComponent(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users);

    return (
      <BTaskeeTableV2
        {...args}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
      />
    );
  },
};

export const WithSort: Story = {
  args: {
    ...Default.args,
    columns: [
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={'NAME'} />
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'age',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={'AGE'} />
        ),
        enableSorting: true,
      },
    ],
  },
  render: function WithSearchComponent(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users);

    return (
      <BTaskeeTableV2
        {...args}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
      />
    );
  },
};

export const WithSearch: Story = {
  args: {
    ...Default.args,
    searchInput: {
      name: 'name',
      defaultValue: '',
      placeholder: 'Search by Name',
    },
  },
  render: function WithSearchComponent(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users, (item, searchParams) => {
        const name = searchParams.get('name') || '';
        return item.name.includes(name);
      });

    // const [someOutSideInputValue, setSomeOutSideInputValue] =
    // useState<InputHTMLAttributes<HTMLInputElement>["value"]>('');
    // console.log('someOutSideInputValue', someOutSideInputValue);

    // useEffect(() => {
    //   const timer = setInterval(() => {
    //     setSomeOutSideInputValue(prev => {
    //       if (typeof prev === 'string') {
    //         return prev + '1';
    //       }
    //       return '1';
    //     });
    //   }, 1000);

    //   return () => clearInterval(timer);
    // }, [someOutSideInputValue]);

    // const searchInput = {
    //   name: 'name',
    //   triggerMode: 'onChange',
    //   defaultValue: someOutSideInputValue,
    //   placeholder: 'Search by name',
    //   setSearchInputValue: setSomeOutSideInputValue
    // };

    return (
      <BTaskeeTableV2
        {...args}
        // searchInput={searchInput}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
      />
    );
  },
};

export const WithFilters: Story = {
  args: {
    ...Default.args,
    columns: [
      ...(Default.args?.columns || []),
      { accessorKey: 'status', header: 'Status' },
    ],
    initialFilters: [
      {
        placeholder: 'Select status',
        name: 'status',
        value: '',
        options: [
          { value: USER_STATUS.ACTIVE, label: 'Active' },
          { value: USER_STATUS.INACTIVE, label: 'Inactive' },
        ],
      },
    ],
  },
  render: function WithFiltersComponent(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users, (item, searchParams) => {
        const status = searchParams.get('status') || '';
        return status ? status.split(',').includes(item.status) : true;
      });

    return (
      <BTaskeeTableV2
        {...args}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
      />
    );
  },
};

export const WithDateFilter: Story = {
  args: {
    ...Default.args,
    columns: [
      ...(Default.args?.columns || []),
      {
        accessorKey: 'createdAt',
        header: 'Created At',
        cell: ({ row }) => (
          <span>{momentTz(row.original.createdAt).format('DD/MM/YYYY')}</span>
        ),
      },
    ],
    initialFilterDate: {
      name: 'date',
      mode: 'month-year',
      minDate: momentTz('2015-01-01').toDate(),
      maxDate: momentTz().add(10, 'years').toDate(),
      defaultValue: getRangeByPresetOptions(DATE_RANGE_OPTIONS.LAST_YEAR),
    },
  },
  render: function WithDateFilter(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users, (item, searchParams) => {
        const date = searchParams.get('date');
        if (date) {
          const { from, to } = JSON.parse(date);
          return momentTz(item.createdAt).isBetween(from, to, undefined, '[]');
        }
        return true;
      });

    return (
      <BTaskeeTableV2
        {...args}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
      />
    );
  },
};

export const WithFilterColumn: Story = {
  args: {
    ...Default.args,
    columns: [
      {
        accessorKey: 'name',
        header: 'Name',
        enableColumnFilter: true,
        meta: {
          filterVariant: 'text',
        },
      },
      {
        accessorKey: 'age',
        header: 'Age',
        enableColumnFilter: true,
        meta: {
          filterVariant: 'range',
        },
      },
      {
        accessorKey: 'status',
        header: 'Status',
        enableColumnFilter: true,
        meta: {
          filterVariant: 'select',
          filterOptions: [
            { value: USER_STATUS.ACTIVE, label: 'Active' },
            { value: USER_STATUS.INACTIVE, label: 'Inactive' },
          ],
        },
      },
      {
        accessorKey: 'createdAt',
        header: 'Created At',
        enableColumnFilter: true,
        meta: {
          filterVariant: 'date',
        },
        cell: ({ row }) => (
          <span>{momentTz(row.original.createdAt).format('DD/MM/YYYY')}</span>
        ),
      },
    ],
  },
  render: function WithFilterColumn(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users, (item, searchParams) => {
        const name = searchParams.get('name') || '';
        const ageMin = searchParams.get('age_min') || '';
        const ageMax = searchParams.get('age_max') || '';
        const status = searchParams.get('status') || '';
        const date = searchParams.get('createdAt') || '';

        return (
          item.name.includes(name) &&
          (ageMin ? item.age >= Number(ageMin) : true) &&
          (ageMax ? item.age <= Number(ageMax) : true) &&
          (status ? status.split(',').includes(item.status) : true) &&
          (!date ||
            momentTz(item.createdAt).isBetween(
              JSON.parse(date).from,
              JSON.parse(date).to,
              undefined,
              '[]',
            ))
        );
      });

    return (
      <BTaskeeTableV2
        {...args}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
      />
    );
  },
};

export const WithHeaderGroup: Story = {
  args: {
    ...Default.args,
  },
  render: function WithHeaderGroup(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users);
    const columnHelper = createColumnHelper<IUser>();

    const columns = [
      columnHelper.group({
        id: 'general-information',
        header: 'General Information',
        columns: [
          columnHelper.accessor('name', {
            header: 'Name',
          }),
          columnHelper.accessor('age', {
            header: 'Age',
          }),
        ],
      }),
      columnHelper.group({
        id: 'related-information',
        header: 'Related Information',
        columns: [
          columnHelper.accessor('status', {
            header: 'Status',
          }),
          columnHelper.accessor('createdAt', {
            header: 'Created At',
            cell: ({ row }) => (
              <span>
                {momentTz(row.original.createdAt).format('DD/MM/YYYY')}
              </span>
            ),
          }),
        ],
      }),
    ];

    return (
      <BTaskeeTableV2
        {...args}
        columns={columns}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
      />
    );
  },
};

export const WithColumnPinning: Story = {
  args: {
    ...Default.args,
    columns: [
      {
        accessorKey: 'name',
        header: 'Name',
      },
      {
        accessorKey: 'age',
        header: 'Age',
      },
      {
        accessorKey: 'status',
        header: 'Status',
      },
      {
        accessorKey: 'createdAt',
        header: 'Created At',
        cell: ({ row }) => (
          <span>{momentTz(row.original.createdAt).format('DD/MM/YYYY')}</span>
        ),
      },
    ],
  },
  render: function WithColumnPinning(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users);

    return (
      <BTaskeeTableV2
        {...args}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
        pinColumns={{ left: ['name'], right: ['age'] }}
      />
    );
  },
};

export const WithResizeColumn: Story = {
  args: {
    ...Default.args,
    isResizeColumn: true,
  },
  render: function WithResizeColumn(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users);

    return (
      <BTaskeeTableV2
        {...args}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
      />
    );
  },
};

export const WithRowExpand: Story = {
  args: {
    ...Default.args,
    columns: [
      {
        size: 0,
        enableResizing: false,
        accessorKey: 'expandAction',
        header: 'Expand',
        cell: ({ row }) => {
          return row.getCanExpand() ? (
            <Button
              variant="ghost"
              type="button"
              onClick={() => row.toggleExpanded()}
              className="py-1 px-2 bg-gray-200 hover:bg-gray-400">
              {row.getIsExpanded() ? (
                <ChevronsDownUp className="w-4 h-4" />
              ) : (
                <ChevronsUpDown className="w-4 h-4" />
              )}
            </Button>
          ) : (
            <X />
          );
        },
      },
      {
        accessorKey: 'name',
        header: 'Name',
      },
      {
        accessorKey: 'age',
        header: 'Age',
        cell: ({ row }) => <span>{row.original.age}</span>,
      },
    ],
  },
  render: function WithRowExpand(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users);

    const ExpandComponent = useCallback(
      ({ row }: { row: Row<Partial<IUser>> }) => {
        return (
          <pre style={{ fontSize: '10px' }}>
            <code>{JSON.stringify(row.original, null, 2)}</code>
          </pre>
        );
      },
      [],
    );

    return (
      <BTaskeeTableV2
        {...args}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
        getRowCanExpand={() => true}
        renderExpandComponent={ExpandComponent}
      />
    );
  },
};

export const OverrideColumnVisibility: Story = {
  args: {
    ...Default.args,
    columns: [
      {
        accessorKey: 'name',
        header: 'Name',
      },
      {
        accessorKey: 'age',
        header: 'Age',
      },
      {
        accessorKey: 'status',
        header: 'Status',
      },
    ],
  },
  render: function OverrideColumnVisibility(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users);

    return (
      <BTaskeeTableV2
        {...args}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
        overrideColumnVisibility={{
          name: false,
          status: true, // Default is true, so we can remove this line
        }}
      />
    );
  },
};
