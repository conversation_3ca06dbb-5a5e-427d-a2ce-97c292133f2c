import { TimePicker } from '@';
import type { Meta, StoryObj } from '@storybook/react';
import { momentTz } from 'btaskee-utils';
import React from 'react';

const meta: Meta<typeof TimePicker> = {
  title: 'components/btaskee/TimePicker',
  component: ({ ...props }) => {
    return <TimePicker {...props} />;
  },
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    date: {
      control: 'object',
      description: 'Date',
    },
    setDate: {
      control: 'object',
      description: 'setDate',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const RenderWithUseForm = () => {
  const [date, setDate] = React.useState<Date | undefined>(momentTz().toDate());

  return <TimePicker date={date} setDate={setDate} />;
};

export const Default: Story = {
  render: () => <RenderWithUseForm />,
};
