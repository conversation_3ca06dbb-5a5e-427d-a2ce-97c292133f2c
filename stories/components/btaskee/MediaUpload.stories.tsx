import type { Meta, StoryObj } from '@storybook/react';
import type { MediaFile } from '@/components/btaskee/MediaUpload';
import { MediaUpload, MediaUploader, MediaUploaderPreview } from '@/components/btaskee/MediaUpload';
import { useMediaState } from '../../utils/media';

interface MediaValue {
  images: MediaFile[];
  videos: MediaFile[];
}

const meta = {
  title: 'Components/Btaskee/MediaUpload',
  component: MediaUpload,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [
    (Story) => (
      <div className="w-[600px]">
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof MediaUpload>;

export default meta;
type Story = StoryObj<typeof meta>;

// Helper component for stories that need state
function MediaUploadWithState(args: MustBeAny) {
  const { media, setMedia } = useMediaState(args.value);
  return (
    <MediaUpload
      {...args}
      value={media}
      onFileChange={setMedia}
    />
  );
}

// Helper component for split view stories
function SplitViewMediaUpload(args: MustBeAny) {
  const { media, setMedia } = useMediaState(args.value);
  const props = {
    ...args,
    value: media,
    onFileChange: setMedia,
  };

  return (
    <div className="space-y-8">
      <div className="rounded-lg border p-4">
        <h3 className="mb-4 text-lg font-semibold">Upload Section</h3>
        <MediaUploader {...props} />
      </div>
      <div className="rounded-lg border p-4">
        <h3 className="mb-4 text-lg font-semibold">Preview Section</h3>
        <div className="relative">
          <MediaUploaderPreview {...props} />
        </div>
      </div>
    </div>
  );
}

const Template: Story = {
  render: (args) => <MediaUploadWithState {...args} />,
  args: {
    title: 'Upload Media',
    maxFiles: 10,
    maxImageSize: 1000,
    maxVideoSize: 10240,
  } as MustBeAny,
};

export const Default = {
  ...Template,
  args: {
    ...Template.args,
  },
};

export const SingleMedia = {
  ...Template,
  args: {
    ...Template.args,
    maxFiles: 1,
  },
};

export const LimitedSize = {
  ...Template,
  args: {
    ...Template.args,
    maxFiles: 5,
    maxImageSize: 500,
    maxVideoSize: 5120,
  },
};

export const WithPreloadedMedia = {
  ...Template,
  args: {
    ...Template.args,
    value: {
      images: [
        {
          file: new File([''], 'test1.jpg', { type: 'image/jpeg' }),
          preview: 'https://picsum.photos/400/300',
          type: 'image'
        },
        {
          file: new File([''], 'test2.jpg', { type: 'image/jpeg' }),
          preview: 'https://picsum.photos/401/300',
          type: 'image'
        }
      ],
      videos: [
        {
          file: new File([''], 'test.mp4', { type: 'video/mp4' }),
          preview: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
          type: 'video'
        }
      ]
    } as MediaValue,
  },
};

export const SplitView = {
  render: (args: MustBeAny) => <SplitViewMediaUpload {...args} />,
  args: {
    ...Template.args,
  },
};

export const SplitViewWithMedia = {
  render: (args: MustBeAny) => <SplitViewMediaUpload {...args} />,
  args: {
    ...WithPreloadedMedia.args,
  },
};
