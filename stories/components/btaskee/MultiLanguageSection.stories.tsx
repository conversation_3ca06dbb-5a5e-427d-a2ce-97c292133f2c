import { Button, Form, Input, MultiLanguageSection } from '@';
import { zodResolver } from '@hookform/resolvers/zod';
import type { Meta, StoryObj } from '@storybook/react';
import { useForm } from 'react-hook-form';
import withRHF from 'stories/decorators/withRHF';
import { z } from 'zod';

const meta: Meta<typeof MultiLanguageSection> = {
  title: 'components/btaskee/MultiLanguageSection',
  component: ({ ...props }) => {
    return <MultiLanguageSection {...props} />;
  },
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [withRHF(false)],
  argTypes: {
    form: {
      control: 'object',
      description: 'Form(born from useForm)',
    },
    children: {
      control: 'object',
      description: 'children',
    },
    childrenProps: {
      control: 'object',
      description: "children's props",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const FormSchema = z.object({
  tool_name: z.object({
    vi: z.string().min(1, 'require'),
    en: z.string().min(1, 'require'),
    ko: z.string().min(1, 'require'),
    th: z.string().min(1, 'require'),
    id: z.string().min(1, 'require'),
  }),
});

const RenderWithUseForm = () => {
  const form = useForm({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      tool_name: {
        vi: '',
        en: '',
        ko: '',
        th: '',
        id: '',
      },
    },
  });

  return (
    <Form {...form}>
      <form
        encType={'multipart/form-data'}
        className={'flex flex-col gap-8 !mt-6'}>
        <MultiLanguageSection
          childrenProps={[
            {
              name: 'tool_name',
              label: 'TOOL_NAME',
            },
          ]}
          form={form}
          // eslint-disable-next-line react/no-children-prop
          children={[<Input key={1} placeholder={'ENTER_TOOL_NAME'} />]}
        />
        <Button
          className="mt-2"
          type="button"
          onClick={form.handleSubmit(data => {
            // eslint-disable-next-line no-console
            console.log({ data });
          })}>
          {'SUBMIT'}
        </Button>
      </form>
    </Form>
  );
};

export const Default: Story = {
  render: () => <RenderWithUseForm />,
};
