import type { <PERSON>a, StoryObj } from '@storybook/react';
import { ReadMarkdownOnly } from '@/components/btaskee/rich-text-editor/read-markdown-only';

const meta: Meta<typeof ReadMarkdownOnly> = {
  title: 'components/btaskee/ReadMarkdownOnly',
  component: ReadMarkdownOnly,
  parameters: {
    layout: 'centered',
  },
  decorators: [
    (Story) => (
      <Story />
    ),
  ],
  tags: ['autodocs'],
  argTypes: {
    content: {
      control: 'text',
      description: 'The markdown content to display',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes to apply',
    },
    isDetail: {
      control: 'boolean',
      description: 'Whether to apply prose styling directly',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    content: `# h1 Heading\n\n## h2 Heading\n\n### h3 Heading\n\n### h4 Heading\n\n## Emphasis\n\n**This is bold text**\n\n**This is bold text**\n\n*This is italic text*\n\n*This is italic text*\n\n~~Strikethrough~~\n\n## Blockquotes\n\n> Blockquotes can also be nested…> …by using additional greater-than signs right next to each other…> …or with spaces between arrows.\n\n***\n\n## Lists\n\n[Unordered](https://markdown-it.github.io/)\n\n* Create a list by starting a line with\n* Sub-lists are made by indenting 2 spaces:\n  * Marker character change forces new list start:\n    * Ac tristique libero volutpat at\n  *\n    * Facilisis in pretium nisl aliquet\n  *\n    * Nulla volutpat aliquam velit\n  *\n* Very easy!\n\nOrdered\n\n1. Lorem ipsum dolor sit amet\n2. Consectetur adipiscing elit\n3. Integer molestie lorem at massa\n4. You can use sequential numbers…\n5. …or keep all the numbers as\n\nStart numbering with offset:\n\n1. foo\n2. bar\n\n* Check list 1\n* Check list 2\n* Check list 3\n\n**Upper-Lower**\n\nUpperne\n\nLowerday\n\nOk ok`,
    isDetail: true,
  },
};

export const WithoutProse: Story = {
  args: {
    content: `# h1 Heading

## h2 Heading

### h3 Heading

This example shows how the content looks without prose styling (isDetail = false).

* List item 1
* List item 2
* List item 3

> A blockquote example

[A link](https://example.com)`,
    isDetail: false,
  },
};

export const WithCustomClass: Story = {
  args: {
    content: `# Custom Styled Content

This example shows content with a custom class applied.

* The text color should be different
* The spacing might be adjusted
* Other custom styles may be visible`,
    className: 'text-blue-600 space-y-4',
    isDetail: true,
  },
};
