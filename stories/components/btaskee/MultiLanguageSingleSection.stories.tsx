import { MultiLanguageSingleSection } from '@/components/btaskee/MultiLanguageSingleSection';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { zodResolver } from '@hookform/resolvers/zod';
import type { Meta, StoryObj } from '@storybook/react';
import { useForm } from 'react-hook-form';
import withRHF from 'stories/decorators/withRHF';
import { z } from 'zod';

const meta: Meta<typeof MultiLanguageSingleSection> = {
  title: 'components/btaskee/MultiLanguageSingleSection',
  component: MultiLanguageSingleSection,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [withRHF(false)],
  argTypes: {
    form: {
      control: 'object',
      description: 'React Hook Form instance',
    },
    children: {
      control: 'object',
      description: 'Form input elements to be rendered',
    },
    childrenProps: {
      control: 'object',
      description: 'Configuration for child elements',
    },
    useNestedStructure: {
      control: 'boolean',
      description: 'Whether to use nested field naming structure',
      defaultValue: false,
    },
    parentField: {
      control: 'text',
      description: 'Parent field name for nested structures',
    },
    order: {
      control: 'number',
      description: 'Priority for error scrolling',
      defaultValue: Infinity,
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic form schema
const BasicFormSchema = z.object({
  title: z.object({
    vi: z.string().min(1, 'Title is required'),
    en: z.string().min(1, 'Title is required'),
    ko: z.string().min(1, 'Title is required'),
    th: z.string(),
    id: z.string(),
  }),
  description: z.object({
    vi: z.string().min(1, 'Description is required'),
    en: z.string().min(1, 'Description is required'),
    ko: z.string(),
    th: z.string(),
    id: z.string(),
  }),
});

// Nested form schema
const NestedFormSchema = z.object({
  product: z.object({
    vi: z.object({
      name: z.string().min(1, 'Name is required'),
      description: z.string().min(1, 'Description is required'),
    }),
    en: z.object({
      name: z.string().min(1, 'Name is required'),
      description: z.string().min(1, 'Description is required'),
    }),
    ko: z.object({
      name: z.string().min(1, 'Name is required'),
      description: z.string(),
    }),
    th: z.object({
      name: z.string(),
      description: z.string(),
    }),
    id: z.object({
      name: z.string(),
      description: z.string(),
    }),
  }),
});

// Basic form component with flat structure
const BasicForm = () => {
  const form = useForm({
    resolver: zodResolver(BasicFormSchema),
    defaultValues: {
      title: {
        vi: '',
        en: '',
        ko: '',
        th: '',
        id: '',
      },
      description: {
        vi: '',
        en: '',
        ko: '',
        th: '',
        id: '',
      },
    },
  });

  const onSubmit = (data: z.infer<typeof BasicFormSchema>) => {
    // eslint-disable-next-line no-console
    console.log('Form submitted:', data);
    alert('Check console for form data');
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6 w-[800px]">
        <MultiLanguageSingleSection
          form={form}
          childrenProps={[
            {
              name: 'title',
              label: 'Title',
              required: 'Title is required',
            },
            {
              name: 'description',
              label: 'Description',
              required: 'Description is required',
              layout: 'full',
            },
          ]}
          order={1}>
          <Input placeholder="Enter title" />
          <Textarea placeholder="Enter description" rows={4} />
        </MultiLanguageSingleSection>

        <Button type="submit" className="mt-4">
          Submit
        </Button>
      </form>
    </Form>
  );
};

// Form with nested structure
const NestedForm = () => {
  const form = useForm({
    resolver: zodResolver(NestedFormSchema),
    defaultValues: {
      product: {
        vi: { name: '', description: '' },
        en: { name: '', description: '' },
        ko: { name: '', description: '' },
        th: { name: '', description: '' },
        id: { name: '', description: '' },
      },
    },
  });

  const onSubmit = (data: z.infer<typeof NestedFormSchema>) => {
    // eslint-disable-next-line no-console
    console.log('Nested form submitted:', data);
    alert('Check console for form data');
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6 w-[800px]">
        <MultiLanguageSingleSection
          form={form}
          parentField="product"
          useNestedStructure={true}
          childrenProps={[
            {
              name: 'name',
              label: 'Product Name',
              required: 'Product name is required',
            },
            {
              name: 'description',
              label: 'Product Description',
              required: 'Product description is required for some languages',
              layout: 'full',
            },
          ]}
          order={1}>
          <Input placeholder="Enter product name" />
          <Textarea placeholder="Enter product description" rows={4} />
        </MultiLanguageSingleSection>

        <Button type="submit" className="mt-4">
          Submit
        </Button>
      </form>
    </Form>
  );
};

// Read-only display
const ReadOnlyExample = () => {
  const data = {
    title: {
      vi: 'Tiêu đề tiếng Việt',
      en: 'English Title',
      ko: '한국어 제목',
      th: 'ชื่อภาษาไทย',
      id: 'Judul Bahasa Indonesia',
    },
    description: {
      vi: 'Mô tả chi tiết bằng tiếng Việt cho sản phẩm này',
      en: 'Detailed description in English for this product',
      ko: '이 제품에 대한 한국어 상세 설명',
      th: 'คำอธิบายโดยละเอียดเป็นภาษาไทยสำหรับผลิตภัณฑ์นี้',
      id: 'Deskripsi detail dalam Bahasa Indonesia untuk produk ini',
    },
  };

  return (
    <div className="w-[800px]">
      <MultiLanguageSingleSection
        data={data}
        childrenProps={[
          {
            name: 'title',
            label: 'Title',
          },
          {
            name: 'description',
            label: 'Description',
            layout: 'full',
            type: 'richtext',
          },
        ]}>
        <Input />
        <Textarea />
      </MultiLanguageSingleSection>
    </div>
  );
};

export const Default: Story = {
  render: () => <BasicForm />,
};

export const WithNestedStructure: Story = {
  render: () => <NestedForm />,
};

export const ReadOnly: Story = {
  render: () => <ReadOnlyExample />,
};
