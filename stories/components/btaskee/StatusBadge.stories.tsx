import { StatusBadge } from '@';
import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta<typeof StatusBadge> = {
  title: 'components/btaskee/StatusBadge',
  component: StatusBadge,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    status: {
      control: 'select',
      description: 'Badge status',
      options: ['DEFAULT', 'ACTIVE', 'INACTIVE'],
    },
    isTesting: {
      control: 'boolean',
      description: 'Badge status with testing data',
    },
    statusClasses: {
      control: 'object',
      description: 'Defined style of status key',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    status: 'DEFAULT',
  },
};

export const Active: Story = {
  args: {
    status: 'ACTIVE',
  },
};

export const Inactive: Story = {
  args: {
    status: 'INACTIVE',
  },
};

export const WithIsTesting: Story = {
  args: {
    status: 'ACTIVE',
    isTesting: true,
  },
};

export const StatusClassed: Story = {
  args: {
    status: 'SOMETHING',
    statusClasses: {
      SOMETHING: 'bg-red-50 text-red-500 rounded-md text-center',
    },
  },
};
