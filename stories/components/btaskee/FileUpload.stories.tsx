import { ImageUpload, ToasterBase } from '@';
import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta<typeof ImageUpload> = {
  title: 'components/btaskee/ImageUpload',
  component: ({ ...props }) => {
    return (
      <>
        <ToasterBase />
        <ImageUpload {...props} />
      </>
    );
  },
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    onFileChange: {
      control: 'object',
      description: 'control on file change',
    },
    ratio: {
      control: 'object',
      description: 'ratio image',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onFileChange: () => { },
    ratio: 1
  },
};
