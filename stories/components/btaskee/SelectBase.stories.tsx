import { SelectBase } from '@/components/btaskee/SelectBase';
import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta<typeof SelectBase> = {
  title: 'components/btaskee/SelectBase',
  component: ({ ...props }) => {
    return <SelectBase {...props} />;
  },
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    defaultValue: {
      control: 'text',
      description: 'Default value',
    },
    onValueChange: {
      control: 'object',
      description: 'On change event',
    },
    options: {
      control: 'object',
      description: 'Options for the select',
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text',
    },
    allowClear: {
      control: 'boolean',
      description: 'Allow clearing the selection',
    },
    isAddItem: {
      control: 'boolean',
      description: 'Allow adding new items',
    },
    newItemPlaceholder: {
      control: 'text',
      description: 'Placeholder for new item input',
    },
    addItem: {
      control: 'object',
      description: 'Add new item event',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    defaultValue: '',
    onValueChange: () => { },
    addItem: () => { },
    options:
      [
        { value: 'option1', label: 'Option 1' },
        { value: 'option2', label: 'Option 2' },
        { value: 'option3', label: 'Option 3' },
      ],
    placeholder: 'Select an option',
  },
};
