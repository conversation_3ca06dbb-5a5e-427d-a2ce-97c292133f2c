import { BreadcrumbsLink } from '@';
import type { Meta, StoryObj } from '@storybook/react';
import {
  reactRouterParameters,
  withRouter,
} from 'storybook-addon-remix-react-router';

const meta: Meta<typeof BreadcrumbsLink> = {
  title: 'components/btaskee/BreadcrumbsLink',
  component: BreadcrumbsLink,
  parameters: {
    layout: 'centered',
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { userId: '42' },
      },
      routing: { path: '/users/:userId' },
    }),
  },
  decorators: [withRouter],
  tags: ['autodocs'],
  argTypes: {
    label: {
      control: 'text',
      description: 'Title',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    label: 'Test link',
  },
};
