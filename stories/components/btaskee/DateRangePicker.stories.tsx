import { DateRangePicker } from '@';
import type { Meta, StoryObj } from '@storybook/react';
import { momentTz } from 'btaskee-utils';

const meta: Meta<typeof DateRangePicker> = {
  title: 'components/btaskee/DateRangePicker',
  component: ({ ...props }) => {
    return <DateRangePicker {...props} />;
  },
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    initialRangeDate: {
      control: 'object',
      description: 'Initial range data',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    initialRangeDate: {
      from: momentTz().startOf('day').toDate(),
      to: momentTz().endOf('day').toDate(),
    },
  },
};
