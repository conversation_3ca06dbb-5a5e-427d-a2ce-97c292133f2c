import { BTaskeeTable } from '@/components/btaskee/TableBase';
import { DataTableColumnHeader } from '@/components/btaskee/table-data/data-table-column-header';
import type { Meta, StoryObj } from '@storybook/react';
import { momentTz } from 'btaskee-utils';
import {
  reactRouterParameters,
  withRouter,
} from 'storybook-addon-remix-react-router';

import { users } from '../../mock/data/Users';
import { USER_STATUS } from '../../mock/enums/user';
import type { IUser } from '../../mock/types/user';
import { usePaginatedData } from '../../utils/pagination';

const meta: Meta<typeof BTaskeeTable<Partial<IUser>, 'name' | 'age'>> = {
  title: 'components/btaskee/BTaskeeTable',
  component: BTaskeeTable,
  decorators: [withRouter],
  parameters: {
    layout: 'centered',
    reactRouter: reactRouterParameters({
      location: {
        pathParams: { role: 'SWE' },
      },
      routing: { path: '/user-group/:role', useStoryElement: true },
    }),
  },
  tags: ['autodocs'],
  argTypes: {
    columns: {
      control: 'object',
      description: 'Table columns definition',
    },
    data: {
      control: 'object',
      description: 'Table data',
    },
    total: {
      control: 'number',
      description: 'Total number of records',
    },
    pagination: {
      control: 'object',
      description: 'Pagination state',
    },
    search: {
      control: 'object',
      description: 'Search configuration',
    },
    filters: {
      control: 'object',
      description: 'Filters configuration',
    },
    filterDate: {
      control: 'object',
      description: 'Date filter configuration',
    },
    columnVisibilityFromOutSide: {
      control: 'object',
      description: 'Column visibility state',
    },
    columnPinningFromOutSide: {
      control: 'object',
      description: 'Column pinning state',
    },
    extraContent: {
      control: 'object',
      description: 'Extra content to render',
    },
    onDoubleClickRow: {
      description: 'Function to handle row double-click',
    },
    renderSubComponent: {
      description: 'Function to render sub-component for a row',
    },
    getRowCanExpand: {
      description: 'Function to determine if a row can expand',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    columns: [
      { accessorKey: 'name', header: 'Name' },
      { accessorKey: 'age', header: 'Age' },
    ],
    data: users,
    total: users.length,
    pagination: { pageIndex: 0, pageSize: 10 },
    isShowClearButton: true,
  },
  render: function DefaultComponent(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users);

    return (
      <BTaskeeTable
        {...args}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
      />
    );
  },
};

export const WithSort: Story = {
  args: {
    ...Default.args,
    columns: [
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={'NAME'} />
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'age',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={'AGE'} />
        ),
        enableSorting: true,
      },
    ],
  },
  render: function WithSearchComponent(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users);

    return (
      <BTaskeeTable
        {...args}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
      />
    );
  },
};

export const WithSearch: Story = {
  args: {
    ...Default.args,
    search: {
      name: 'name',
      placeholder: 'Search by name',
      defaultValue: '',
    },
  },
  render: function WithSearchComponent(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users, (item, searchParams) => {
        const name = searchParams.get('name') || '';
        return item.name.includes(name);
      });

    return (
      <BTaskeeTable
        {...args}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
      />
    );
  },
};

export const WithFilters: Story = {
  args: {
    ...Default.args,
    columns: [
      ...(Default.args?.columns || []),
      { accessorKey: 'status', header: 'Status' },
    ],
    filters: [
      {
        placeholder: 'Select status',
        name: 'status',
        value: '',
        options: [
          { value: USER_STATUS.ACTIVE, label: 'Active' },
          { value: USER_STATUS.INACTIVE, label: 'Inactive' },
        ],
      },
    ],
  },
  render: function WithFiltersComponent(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users, (item, searchParams) => {
        const status = searchParams.get('status') || '';
        return status ? status.split(',').includes(item.status) : true;
      });

    return (
      <BTaskeeTable
        {...args}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
      />
    );
  },
};

export const WithDateFilter: Story = {
  args: {
    ...Default.args,
    columns: [
      ...(Default.args?.columns || []),
      {
        accessorKey: 'createdAt',
        header: 'Created At',
        cell: ({ row }) => (
          <span>{momentTz(row.original.createdAt).format('DD/MM/YYYY')}</span>
        ),
      },
    ],
    filterDate: {
      name: 'date',
      defaultRangeDateOptions: 'NONE',
    },
  },
  render: function WithDateFilter(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users, (item, searchParams) => {
        const date = searchParams.get('date');
        if (date) {
          const { from, to } = JSON.parse(date);
          return momentTz(item.createdAt).isBetween(from, to, undefined, '[]');
        }
        return true;
      });

    return (
      <BTaskeeTable
        {...args}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
      />
    );
  },
};

export const WithFilterColumn: Story = {
  args: {
    ...Default.args,
    columns: [
      {
        accessorKey: 'name',
        header: 'Name',
        enableColumnFilter: true,
        meta: {
          filterVariant: 'text',
        },
      },
      {
        accessorKey: 'age',
        header: 'Age',
        enableColumnFilter: true,
        meta: {
          filterVariant: 'range',
        },
      },
      {
        accessorKey: 'status',
        header: 'Status',
        enableColumnFilter: true,
        meta: {
          filterVariant: 'select',
          filterOptions: [
            { value: USER_STATUS.ACTIVE, label: 'Active' },
            { value: USER_STATUS.INACTIVE, label: 'Inactive' },
          ],
        },
      },
      {
        accessorKey: 'createdAt',
        header: 'Created At',
        enableColumnFilter: true,
        meta: {
          filterVariant: 'date',
        },
        cell: ({ row }) => (
          <span>{momentTz(row.original.createdAt).format('DD/MM/YYYY')}</span>
        ),
      },
    ],
  },
  render: function WithFilterColumn(args) {
    const { paginatedData, totalDataAfterFiltered, pagination } =
      usePaginatedData(users, (item, searchParams) => {
        const name = searchParams.get('name') || '';
        const ageMin = searchParams.get('age_min') || '';
        const ageMax = searchParams.get('age_max') || '';
        const status = searchParams.get('status') || '';
        const date = searchParams.get('createdAt') || '';

        return (
          item.name.includes(name) &&
          (ageMin ? item.age >= Number(ageMin) : true) &&
          (ageMax ? item.age <= Number(ageMax) : true) &&
          (status ? status.split(',').includes(item.status) : true) &&
          (!date ||
            momentTz(item.createdAt).isBetween(
              JSON.parse(date).from,
              JSON.parse(date).to,
              undefined,
              '[]',
            ))
        );
      });

    return (
      <BTaskeeTable
        {...args}
        data={paginatedData}
        total={totalDataAfterFiltered}
        pagination={pagination}
      />
    );
  },
};
