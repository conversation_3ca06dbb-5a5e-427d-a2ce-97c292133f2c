import { RadioGroupsBase } from '@';
import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta<typeof RadioGroupsBase> = {
  title: 'components/btaskee/RadioGroupsBase',
  component: ({ ...props }) => {
    return <RadioGroupsBase {...props} />;
  },
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    defaultValue: {
      control: 'text',
      description: 'Default value',
    },
    onValueChange: {
      control: 'object',
      description: 'On change event',
    },
    options: {
      control: 'object',
      description: 'Options radio',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const Render = () => {
  return (
    <RadioGroupsBase
      defaultValue="test1"
      onValueChange={() => {}}
      options={[
        {
          label: 'Default value',
          value: 'test1',
        },
        {
          label: 'AAAAA',
          value: 'test2',
        },
        {
          label: 'BBBB',
          value: 'test3',
        },
        {
          label: 'CCCC',
          value: 'test4',
        },
      ]}
    />
  );
};

export const Default: Story = {
  render: () => <Render />,
};
