import { useSearchParams } from '@remix-run/react';
import { getPageSizeAndPageIndex } from 'btaskee-utils';
import { useEffect, useState } from 'react';

import type { IUser } from '../mock/types/user';

interface IndexedUser extends IUser {
  [key: string]: MustBeAny;
}

export function usePaginatedData<T extends IndexedUser>(
  data: T[],
  filterFn: (item: T, searchParams: URLSearchParams) => boolean = () => true,
) {
  const [paginatedData, setPaginatedData] = useState(data);
  const [totalDataAfterFiltered, setTotalDataAfterFiltered] = useState(
    data.length,
  );
  const [searchParams] = useSearchParams();

  useEffect(() => {
    const pageSize = Number(searchParams.get('pageSize') || 10);
    const pageIndex = Number(searchParams.get('pageIndex') || 0);
    const start = pageIndex * pageSize;
    const sort = searchParams.get('sort') || '';
    const [sortKey, sortOrder] = sort.split(':');

    const filteredData = data.filter(item => filterFn(item, searchParams));
    
    // Find the most recent pinned post
    const pinnedPosts = filteredData.filter(item => item.isPinned);
    let mostRecentPinnedPost = null;
    
    if (pinnedPosts.length > 0) {
      mostRecentPinnedPost = pinnedPosts.reduce((newest, current) => {
        return new Date(current.createdAt).getTime() > new Date(newest.createdAt).getTime() 
          ? current 
          : newest;
      }, pinnedPosts[0]);
    }
    
    // Sort data with only the most recent pinned post at the top
    const sortedData = filteredData.sort((a, b) => {
      // Check if this is the most recent pinned post
      const isAPinnedAndMostRecent = mostRecentPinnedPost && a.id === mostRecentPinnedPost.id;
      const isBPinnedAndMostRecent = mostRecentPinnedPost && b.id === mostRecentPinnedPost.id;
      
      // Only the most recent pinned post should have priority
      if (isAPinnedAndMostRecent && !isBPinnedAndMostRecent) return -1;
      if (!isAPinnedAndMostRecent && isBPinnedAndMostRecent) return 1;
      // Then sort by the specified sort criteria
      if (sortKey && sortOrder) {
        if (sortOrder === 'asc') {
          return Number(a[sortKey]) - Number(b[sortKey]);
        }
        return Number(b[sortKey]) - Number(a[sortKey]);
      }

      // Default sort by most recent first if no sort criteria specified
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    setPaginatedData(sortedData.slice(start, start + pageSize));
    setTotalDataAfterFiltered(sortedData.length);
  }, [searchParams, data]);

  return {
    paginatedData,
    totalDataAfterFiltered,
    pagination: getPageSizeAndPageIndex({
      total: totalDataAfterFiltered,
      pageSize: Number(searchParams.get('pageSize') || 10),
      pageIndex: Number(searchParams.get('pageIndex') || 0),
    }),
  };
}
