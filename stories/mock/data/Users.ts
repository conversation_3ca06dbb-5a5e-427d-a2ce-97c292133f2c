import { USER_STATUS } from '../enums/user';
import type { IUser } from '../types/user';

export const users: IUser[] = [
  {
    _id: 'user-1',
    name: '<PERSON> 1',
    status: USER_STATUS.ACTIVE,
    age: 20,
    createdAt: '2024-07-01T09:23:32.000Z',
  },
  {
    _id: 'user-2',
    name: '<PERSON> 1',
    status: USER_STATUS.INACTIVE,
    age: 21,
    createdAt: '2024-07-09T09:23:32.000Z',
  },
  {
    _id: 'user-3',
    name: '<PERSON> 2',
    status: USER_STATUS.ACTIVE,
    age: 21,
    createdAt: '2024-07-09T09:23:32.000Z',
  },
  {
    _id: 'user-2',
    name: '<PERSON> 2',
    status: USER_STATUS.INACTIVE,
    age: 22,
    createdAt: '2024-07-09T09:23:32.000Z',
  },
  {
    _id: 'user-4',
    name: '<PERSON> 3',
    status: USER_STATUS.ACTIVE,
    age: 23,
    createdAt: '2024-08-02T09:23:32.000Z',
  },
  {
    _id: 'user-5',
    name: '<PERSON> 3',
    status: USER_STATUS.ACTIVE,
    age: 24,
    createdAt: '2024-08-02T09:23:32.000Z',
  },
  {
    _id: 'user-6',
    name: 'John Doe 4',
    status: USER_STATUS.ACTIVE,
    age: 25,
    createdAt: '2024-08-02T09:23:32.000Z',
  },
  {
    _id: 'user-7',
    name: 'Jane Smith 4',
    status: USER_STATUS.INACTIVE,
    age: 26,
    createdAt: '2024-06-01T09:23:32.000Z',
  },
  {
    _id: 'user-8',
    name: 'John Doe 5',
    status: USER_STATUS.ACTIVE,
    age: 27,
    createdAt: '2024-06-01T09:23:32.000Z',
  },
  {
    _id: 'user-9',
    name: 'Jane Smith 5',
    status: USER_STATUS.ACTIVE,
    age: 28,
    createdAt: '2024-06-03T09:23:32.000Z',
  },
  {
    _id: 'user-10',
    name: 'John Doe 6',
    status: USER_STATUS.INACTIVE,
    age: 29,
    createdAt: '2024-05-03T09:23:32.000Z',
  },
  {
    _id: 'user-11',
    name: 'Jane Smith 6',
    status: USER_STATUS.ACTIVE,
    age: 30,
    createdAt: '2024-05-03T09:23:32.000Z',
  },
  {
    _id: 'user-12',
    name: 'John Doe 7',
    status: USER_STATUS.ACTIVE,
    age: 31,
    createdAt: '2024-05-03T09:23:32.000Z',
  },
  {
    _id: 'user-13',
    name: 'Jane Smith 7',
    status: USER_STATUS.INACTIVE,
    age: 32,
    createdAt: '2024-05-01T09:23:32.000Z',
  },
  {
    _id: 'user-14',
    name: 'John Doe 8',
    status: USER_STATUS.ACTIVE,
    age: 33,
    createdAt: '2024-08-01T09:23:32.000Z',
  },
  {
    _id: 'user-15',
    name: 'Jane Smith 8',
    status: USER_STATUS.ACTIVE,
    age: 34,
    createdAt: '2024-04-01T09:23:32.000Z',
  },
  {
    _id: 'user-16',
    name: 'John Doe 9',
    status: USER_STATUS.INACTIVE,
    age: 35,
    createdAt: '2024-04-01T09:23:32.000Z',
  },
  {
    _id: 'user-17',
    name: 'Jane Smith 9',
    status: USER_STATUS.ACTIVE,
    age: 36,
    createdAt: '2024-04-02T09:23:32.000Z',
  },
  {
    _id: 'user-18',
    name: 'John Doe 10',
    status: USER_STATUS.ACTIVE,
    age: 37,
    createdAt: '2024-05-02T09:23:32.000Z',
  },
  {
    _id: 'user-19',
    name: 'Jane Smith 10',
    status: USER_STATUS.ACTIVE,
    age: 38,
    createdAt: '2024-05-02T09:23:32.000Z',
  },
  {
    _id: 'user-20',
    name: 'John Doe 11',
    status: USER_STATUS.INACTIVE,
    age: 39,
    createdAt: '2024-05-02T09:23:32.000Z',
  },
  {
    _id: 'user-21',
    name: 'Jane Smith 11',
    status: USER_STATUS.ACTIVE,
    age: 40,
    createdAt: '2024-05-01T09:23:32.000Z',
  },
];
