{"version": 3, "sources": ["../@/components/btaskee/drop-zone/ReactCrop.css"], "sourcesContent": ["@keyframes marching-ants{0%{background-position:0 0,0 100%,0 0,100% 0}to{background-position:20px 0,-20px 100%,0 -20px,100% 20px}}:root{--rc-drag-handle-size: 12px;--rc-drag-handle-mobile-size: 24px;--rc-drag-handle-bg-colour: rgba(0, 0, 0, .2);--rc-drag-bar-size: 6px;--rc-border-color: rgba(255, 255, 255, .7);--rc-focus-color: #0088ff}.ReactCrop{position:relative;display:inline-block;cursor:crosshair;max-width:100%}.ReactCrop *,.ReactCrop *:before,.ReactCrop *:after{box-sizing:border-box}.ReactCrop--disabled,.ReactCrop--locked{cursor:inherit}.ReactCrop__child-wrapper{overflow:hidden;max-height:inherit}.ReactCrop__child-wrapper>img,.ReactCrop__child-wrapper>video{display:block;max-width:100%;max-height:inherit}.ReactCrop:not(.ReactCrop--disabled) .ReactCrop__child-wrapper>img,.ReactCrop:not(.ReactCrop--disabled) .ReactCrop__child-wrapper>video{touch-action:none}.ReactCrop:not(.ReactCrop--disabled) .ReactCrop__crop-selection{touch-action:none}.ReactCrop__crop-mask{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none;width:calc(100% + .5px);height:calc(100% + .5px)}.ReactCrop__crop-selection{position:absolute;top:0;left:0;transform:translateZ(0);cursor:move}.ReactCrop--disabled .ReactCrop__crop-selection{cursor:inherit}.ReactCrop--circular-crop .ReactCrop__crop-selection{border-radius:50%}.ReactCrop--circular-crop .ReactCrop__crop-selection:after{pointer-events:none;content:\"\";position:absolute;top:-1px;right:-1px;bottom:-1px;left:-1px;border:1px solid var(--rc-border-color);opacity:.3}.ReactCrop--no-animate .ReactCrop__crop-selection{outline:1px dashed white}.ReactCrop__crop-selection:not(.ReactCrop--no-animate .ReactCrop__crop-selection){animation:marching-ants 1s;background-image:linear-gradient(to right,#fff 50%,#444 50%),linear-gradient(to right,#fff 50%,#444 50%),linear-gradient(to bottom,#fff 50%,#444 50%),linear-gradient(to bottom,#fff 50%,#444 50%);background-size:10px 1px,10px 1px,1px 10px,1px 10px;background-position:0 0,0 100%,0 0,100% 0;background-repeat:repeat-x,repeat-x,repeat-y,repeat-y;color:#fff;animation-play-state:running;animation-timing-function:linear;animation-iteration-count:infinite}.ReactCrop__crop-selection:focus{outline:2px solid var(--rc-focus-color);outline-offset:-1px}.ReactCrop--invisible-crop .ReactCrop__crop-mask,.ReactCrop--invisible-crop .ReactCrop__crop-selection{display:none}.ReactCrop__rule-of-thirds-vt:before,.ReactCrop__rule-of-thirds-vt:after,.ReactCrop__rule-of-thirds-hz:before,.ReactCrop__rule-of-thirds-hz:after{content:\"\";display:block;position:absolute;background-color:#fff6}.ReactCrop__rule-of-thirds-vt:before,.ReactCrop__rule-of-thirds-vt:after{width:1px;height:100%}.ReactCrop__rule-of-thirds-vt:before{left:33.3333333333%}.ReactCrop__rule-of-thirds-vt:after{left:66.6666666667%}.ReactCrop__rule-of-thirds-hz:before,.ReactCrop__rule-of-thirds-hz:after{width:100%;height:1px}.ReactCrop__rule-of-thirds-hz:before{top:33.3333333333%}.ReactCrop__rule-of-thirds-hz:after{top:66.6666666667%}.ReactCrop__drag-handle{position:absolute;width:var(--rc-drag-handle-size);height:var(--rc-drag-handle-size);background-color:var(--rc-drag-handle-bg-colour);border:1px solid var(--rc-border-color)}.ReactCrop__drag-handle:focus{background:var(--rc-focus-color)}.ReactCrop .ord-nw{top:0;left:0;transform:translate(-50%,-50%);cursor:nw-resize}.ReactCrop .ord-n{top:0;left:50%;transform:translate(-50%,-50%);cursor:n-resize}.ReactCrop .ord-ne{top:0;right:0;transform:translate(50%,-50%);cursor:ne-resize}.ReactCrop .ord-e{top:50%;right:0;transform:translate(50%,-50%);cursor:e-resize}.ReactCrop .ord-se{bottom:0;right:0;transform:translate(50%,50%);cursor:se-resize}.ReactCrop .ord-s{bottom:0;left:50%;transform:translate(-50%,50%);cursor:s-resize}.ReactCrop .ord-sw{bottom:0;left:0;transform:translate(-50%,50%);cursor:sw-resize}.ReactCrop .ord-w{top:50%;left:0;transform:translate(-50%,-50%);cursor:w-resize}.ReactCrop__disabled .ReactCrop__drag-handle{cursor:inherit}.ReactCrop__drag-bar{position:absolute}.ReactCrop__drag-bar.ord-n{top:0;left:0;width:100%;height:var(--rc-drag-bar-size);transform:translateY(-50%)}.ReactCrop__drag-bar.ord-e{right:0;top:0;width:var(--rc-drag-bar-size);height:100%;transform:translate(50%)}.ReactCrop__drag-bar.ord-s{bottom:0;left:0;width:100%;height:var(--rc-drag-bar-size);transform:translateY(50%)}.ReactCrop__drag-bar.ord-w{top:0;left:0;width:var(--rc-drag-bar-size);height:100%;transform:translate(-50%)}.ReactCrop--new-crop .ReactCrop__drag-bar,.ReactCrop--new-crop .ReactCrop__drag-handle,.ReactCrop--fixed-aspect .ReactCrop__drag-bar,.ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-n,.ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-e,.ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-s,.ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-w{display:none}@media (pointer: coarse){.ReactCrop .ord-n,.ReactCrop .ord-e,.ReactCrop .ord-s,.ReactCrop .ord-w{display:none}.ReactCrop__drag-handle{width:var(--rc-drag-handle-mobile-size);height:var(--rc-drag-handle-mobile-size)}}\n"], "mappings": ";AAAA,WAAW;AAAc;AAAG;AAAA,MAAoB,EAAE,CAAC;AAAA,MAAC,EAAE,IAAI;AAAA,MAAC,EAAE,CAAC;AAAA,MAAC,KAAK;AAAC;AAAC;AAAG;AAAA,MAAoB,KAAK,CAAC;AAAA,MAAC,MAAM,IAAI;AAAA,MAAC,EAAE,KAAK;AAAA,MAAC,KAAK;AAAI;AAAC;AAAC;AAAM,yBAAuB;AAAK,gCAA8B;AAAK,8BAA4B,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAAI,sBAAoB;AAAI,qBAAmB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAAI,oBAAkB;AAAO;AAAC,CAAC;AAAU,YAAS;AAAS,WAAQ;AAAa,UAAO;AAAU,aAAU;AAAI;AAAC,CAAjF,UAA4F;AAAE,CAA9F,UAAyG,CAAC;AAAQ,CAAlH,UAA6H,CAAC;AAAO,cAAW;AAAU;AAAC,CAAC;AAAoB,CAAC;AAAkB,UAAO;AAAO;AAAC,CAAC;AAAyB,YAAS;AAAO,cAAW;AAAO;AAAC,CAA5D,yBAAqF,EAAC;AAAI,CAA1F,yBAAmH,EAAC;AAAM,WAAQ;AAAM,aAAU;AAAK,cAAW;AAAO;AAAC,CAA7X,SAAuY,KAAK,CAAhP,qBAAsQ,CAA/M,yBAAwO,EAAC;AAAI,CAAhc,SAA0c,KAAK,CAAnT,qBAAyU,CAAlR,yBAA2S,EAAC;AAAM,gBAAa;AAAI;AAAC,CAAvhB,SAAiiB,KAAK,CAA1Y,qBAAga,CAAC;AAA0B,gBAAa;AAAI;AAAC,CAAC;AAAqB,YAAS;AAAS,OAAI;AAAE,SAAM;AAAE,UAAO;AAAE,QAAK;AAAE,kBAAe;AAAK,SAAM,KAAK,KAAK,EAAE;AAAM,UAAO,KAAK,KAAK,EAAE;AAAK;AAAC,CAAvL;AAAkN,YAAS;AAAS,OAAI;AAAE,QAAK;AAAE,aAAU,WAAW;AAAG,UAAO;AAAI;AAAC,CAAtrB,oBAA2sB,CAA1S;AAAqU,UAAO;AAAO;AAAC,CAAC,yBAAyB,CAA9W;AAAyY,iBAAc;AAAG;AAAC,CAAtE,yBAAgG,CAArb,yBAA+c;AAAO,kBAAe;AAAK,WAAQ;AAAG,YAAS;AAAS,OAAI;AAAK,SAAM;AAAK,UAAO;AAAK,QAAK;AAAK,UAAO,IAAI,MAAM,IAAI;AAAmB,WAAQ;AAAE;AAAC,CAAC,sBAAsB,CAA3nB;AAAspB,WAAQ,IAAI,OAAO;AAAK;AAAC,CAA/qB,yBAAysB,KAAK,CAAzG,sBAAgI,CAAruB;AAAiwB,aAAU,cAAc;AAAG;AAAA,IAAiB;AAAA,MAAgB,GAAG,KAAnB;AAAA,MAAyB,KAAK,GAA9B;AAAA,MAAkC,KAAK,IAAI;AAAA,IAAC;AAAA,MAAgB,GAAG,KAAnB;AAAA,MAAyB,KAAK,GAA9B;AAAA,MAAkC,KAAK,IAAI;AAAA,IAAC;AAAA,MAAgB,GAAG,MAAnB;AAAA,MAA0B,KAAK,GAA/B;AAAA,MAAmC,KAAK,IAAI;AAAA,IAAC;AAAA,MAAgB,GAAG,MAAnB;AAAA,MAA0B,KAAK,GAA/B;AAAA,MAAmC,KAAK;AAAK;AAAA,IAAgB,KAAK,GAAG;AAAA,IAAC,KAAK,GAAG;AAAA,IAAC,IAAI,IAAI;AAAA,IAAC,IAAI;AAAK;AAAA,IAAoB,EAAE,CAAC;AAAA,IAAC,EAAE,IAAI;AAAA,IAAC,EAAE,CAAC;AAAA,IAAC,KAAK;AAAE;AAAA,IAAkB,QAAQ;AAAA,IAAC,QAAQ;AAAA,IAAC,QAAQ;AAAA,IAAC;AAAS,SAAM;AAAK,wBAAqB;AAAQ,6BAA0B;AAAO,6BAA0B;AAAQ;AAAC,CAA/tC,yBAAyvC;AAAO,WAAQ,IAAI,MAAM,IAAI;AAAkB,kBAAe;AAAI;AAAC,CAAC,0BAA0B,CAA1yC;AAAg0C,CAAhD,0BAA2E,CAAx4C;AAAm6C,WAAQ;AAAI;AAAC,CAAC,4BAA4B;AAAQ,CAApC,4BAAiE;AAAO,CAAC,4BAA4B;AAAQ,CAApC,4BAAiE;AAAO,WAAQ;AAAG,WAAQ;AAAM,YAAS;AAAS,oBAAiB;AAAK;AAAC,CAAnN,4BAAgP;AAAQ,CAAxP,4BAAqR;AAAO,SAAM;AAAI,UAAO;AAAI;AAAC,CAAlT,4BAA+U;AAAQ,QAAK;AAAc;AAAC,CAA3W,4BAAwY;AAAO,QAAK;AAAc;AAAC,CAA1V,4BAAuX;AAAQ,CAA/X,4BAA4Z;AAAO,SAAM;AAAK,UAAO;AAAG;AAAC,CAAzb,4BAAsd;AAAQ,OAAI;AAAc;AAAC,CAAjf,4BAA8gB;AAAO,OAAI;AAAc;AAAC,CAAC;AAAuB,YAAS;AAAS,SAAM,IAAI;AAAuB,UAAO,IAAI;AAAuB,oBAAiB,IAAI;AAA4B,UAAO,IAAI,MAAM,IAAI;AAAkB;AAAC,CAArM,sBAA4N;AAAO,cAAW,IAAI;AAAiB;AAAC,CAAp2F,UAA+2F,CAAC;AAAO,OAAI;AAAE,QAAK;AAAE,aAAU,UAAU,IAAI,EAAC;AAAM,UAAO;AAAS;AAAC,CAAp7F,UAA+7F,CAAC;AAAM,OAAI;AAAE,QAAK;AAAI,aAAU,UAAU,IAAI,EAAC;AAAM,UAAO;AAAQ;AAAC,CAApgG,UAA+gG,CAAC;AAAO,OAAI;AAAE,SAAM;AAAE,aAAU,UAAU,GAAG,EAAC;AAAM,UAAO;AAAS;AAAC,CAAplG,UAA+lG,CAAC;AAAM,OAAI;AAAI,SAAM;AAAE,aAAU,UAAU,GAAG,EAAC;AAAM,UAAO;AAAQ;AAAC,CAApqG,UAA+qG,CAAC;AAAO,UAAO;AAAE,SAAM;AAAE,aAAU,UAAU,GAAG,EAAC;AAAK,UAAO;AAAS;AAAC,CAAtvG,UAAiwG,CAAC;AAAM,UAAO;AAAE,QAAK;AAAI,aAAU,UAAU,IAAI,EAAC;AAAK,UAAO;AAAQ;AAAC,CAAx0G,UAAm1G,CAAC;AAAO,UAAO;AAAE,QAAK;AAAE,aAAU,UAAU,IAAI,EAAC;AAAK,UAAO;AAAS;AAAC,CAA15G,UAAq6G,CAAC;AAAM,OAAI;AAAI,QAAK;AAAE,aAAU,UAAU,IAAI,EAAC;AAAM,UAAO;AAAQ;AAAC,CAAC,oBAAoB,CAA/5B;AAAu7B,UAAO;AAAO;AAAC,CAAC;AAAoB,YAAS;AAAQ;AAAC,CAAtC,mBAA0D,CAAjqB;AAAwqB,OAAI;AAAE,QAAK;AAAE,SAAM;AAAK,UAAO,IAAI;AAAoB,aAAU,WAAW;AAAK;AAAC,CAAnJ,mBAAuK,CAA9mB;AAAqnB,SAAM;AAAE,OAAI;AAAE,SAAM,IAAI;AAAoB,UAAO;AAAK,aAAU,UAAU;AAAI;AAAC,CAA/P,mBAAmR,CAAxjB;AAA+jB,UAAO;AAAE,QAAK;AAAE,SAAM;AAAK,UAAO,IAAI;AAAoB,aAAU,WAAW;AAAI;AAAC,CAA9W,mBAAkY,CAAngB;AAA0gB,OAAI;AAAE,QAAK;AAAE,SAAM,IAAI;AAAoB,UAAO;AAAK,aAAU,UAAU;AAAK;AAAC,CAAC,oBAAoB,CAA/e;AAAogB,CAAzC,oBAA8D,CAAh+C;AAAw/C,CAAC,wBAAwB,CAA1kB;AAA+lB,CAA7C,wBAAsE,CAA/jD,sBAAslD,CAAtvC;AAA6vC,CAApG,wBAA6H,CAAtnD,sBAA6oD,CAA7oC;AAAopC,CAA3J,wBAAoL,CAA7qD,sBAAosD,CAAliC;AAAyiC,CAAlN,wBAA2O,CAApuD,sBAA2vD,CAAr7B;AAA47B,WAAQ;AAAI;AAAC,OAAO,CAAC,OAAO,EAAE;AAAQ,GAAx4I,UAAm5I,CAAn9C;AAAA,EAA09C,CAA15I,UAAq6I,CAAr0C;AAAA,EAA40C,CAA56I,UAAu7I,CAArrC;AAAA,EAA4rC,CAA97I,UAAy8I,CAAniC;AAA0iC,aAAQ;AAAI;AAAC,GAA73D;AAAq5D,WAAM,IAAI;AAA8B,YAAO,IAAI;AAA6B;AAAC;", "names": []}