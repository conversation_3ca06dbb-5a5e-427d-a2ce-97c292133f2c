import * as RechartsPrimitive from 'recharts';
import { Area, AreaProps, Bar, BarProps, CartesianGrid, CartesianGridProps, CellProps, Legend, LegendProps, Line, LineProps, Pie, PieProps, RectangleProps, ReferenceLine, ReferenceLineProps, ResponsiveContainerProps, Tooltip as Tooltip$1, XAxis, XAxisProps, YAxis, YAxisProps } from 'recharts';
import * as React$1 from 'react';
import React__default, { HTMLAttributes, FC, InputHTMLAttributes, ReactElement, Dispatch, SetStateAction, ComponentType, ReactNode, SVGProps, ComponentProps, DetailedHTMLProps, ImgHTMLAttributes } from 'react';
import * as recharts_types_util_types from 'recharts/types/util/types';
import { DataKey } from 'recharts/types/util/types';
import * as recharts_types_chart_types from 'recharts/types/chart/types';
import * as lodash from 'lodash';
import * as recharts_types_chart_generateCategoricalChart from 'recharts/types/chart/generateCategoricalChart';
import { CategoricalChartProps } from 'recharts/types/chart/generateCategoricalChart';
import * as recharts_types_chart_AccessibilityManager from 'recharts/types/chart/AccessibilityManager';
import * as react_jsx_runtime from 'react/jsx-runtime';
import * as recharts_types_util_payload_getUniqPayload from 'recharts/types/util/payload/getUniqPayload';
import * as recharts_types_component_Tooltip from 'recharts/types/component/Tooltip';
import * as recharts_types_component_DefaultTooltipContent from 'recharts/types/component/DefaultTooltipContent';
import * as AccordionPrimitive from '@radix-ui/react-accordion';
import { AccordionSingleProps } from '@radix-ui/react-accordion';
import * as AlertDialogPrimitive from '@radix-ui/react-alert-dialog';
import * as class_variance_authority_types from 'class-variance-authority/types';
import { VariantProps } from 'class-variance-authority';
import * as AspectRatioPrimitive from '@radix-ui/react-aspect-ratio';
import * as AvatarPrimitive from '@radix-ui/react-avatar';
import { LinkProps, NavLinkProps, ErrorResponse } from '@remix-run/react';
import { DayPicker } from 'react-day-picker';
import useEmblaCarousel, { UseEmblaCarouselType } from 'embla-carousel-react';
import * as CheckboxPrimitive from '@radix-ui/react-checkbox';
import * as CollapsiblePrimitive from '@radix-ui/react-collapsible';
import * as DialogPrimitive from '@radix-ui/react-dialog';
import { DialogProps } from '@radix-ui/react-dialog';
import * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu';
import { DropdownMenuItemProps } from '@radix-ui/react-dropdown-menu';
import * as LabelPrimitive from '@radix-ui/react-label';
import * as ProgressPrimitive from '@radix-ui/react-progress';
import * as RadioGroupPrimitive from '@radix-ui/react-radio-group';
import { RadioGroupProps } from '@radix-ui/react-radio-group';
import * as SliderPrimitive from '@radix-ui/react-slider';
import * as SwitchPrimitives from '@radix-ui/react-switch';
import * as vaul from 'vaul';
import { Drawer as Drawer$1 } from 'vaul';
import * as TabsPrimitive from '@radix-ui/react-tabs';
export { TabsListProps, TabsProps } from '@radix-ui/react-tabs';
import * as TooltipPrimitive from '@radix-ui/react-tooltip';
import * as ToastPrimitives from '@radix-ui/react-toast';
import * as TogglePrimitive from '@radix-ui/react-toggle';
import * as ToggleGroupPrimitive from '@radix-ui/react-toggle-group';
import * as ContextMenuPrimitive from '@radix-ui/react-context-menu';
import * as _radix_ui_react_slot from '@radix-ui/react-slot';
import * as react_hook_form from 'react-hook-form';
import { FieldValues, FieldPath, ControllerProps, UseFormReturn, RegisterOptions, FieldErrors, RefCallBack, FormState, ControllerRenderProps, Path, UseFormProps } from 'react-hook-form';
import * as HoverCardPrimitive from '@radix-ui/react-hover-card';
import * as input_otp from 'input-otp';
import * as MenubarPrimitive from '@radix-ui/react-menubar';
import { Menu } from '@radix-ui/react-menubar';
import { SetURLSearchParams, URLSearchParamsInit } from 'react-router-dom';
import * as NavigationMenuPrimitive from '@radix-ui/react-navigation-menu';
import * as PopoverPrimitive from '@radix-ui/react-popover';
import * as ResizablePrimitive from 'react-resizable-panels';
import * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area';
import * as SelectPrimitive from '@radix-ui/react-select';
import * as SeparatorPrimitive from '@radix-ui/react-separator';
import { Props as Props$2 } from '@hookform/error-message';
import { ColumnDef, PaginationState, VisibilityState, ColumnPinningState, Row, Column, Table as Table$1, SortingState } from '@tanstack/react-table';
import { RowData } from '@tanstack/table-core';
import { FlatNamespace, TFunction } from 'i18next';
import { ClassValue } from 'clsx';
import { useTranslation } from 'react-i18next';
import { SerializeFrom } from '@remix-run/node';
import { z, ZodRawShape } from 'zod';
import { getFileType, usePostSchema } from 'btaskee-utils';
import { DropzoneOptions } from 'react-dropzone';
import { USER_RANKS } from 'btaskee-constants';

type AreaBaseProps = Omit<AreaProps, 'ref'>;
declare const AreaBase: typeof Area;

type AreaChartBaseProps = CategoricalChartProps;
declare const AreaChartBase: React$1.ForwardRefExoticComponent<CategoricalChartProps & React$1.RefAttributes<{
    readonly eventEmitterSymbol: Symbol;
    clipPathId: string;
    accessibilityManager: recharts_types_chart_AccessibilityManager.AccessibilityManager;
    throttleTriggeredAfterMouseMove: lodash.DebouncedFunc<(e: recharts_types_chart_generateCategoricalChart.MousePointer) => any>;
    container?: HTMLElement;
    componentDidMount(): void;
    displayDefaultTooltip(): void;
    getSnapshotBeforeUpdate(prevProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, prevState: Readonly<recharts_types_chart_types.CategoricalChartState>): null;
    componentDidUpdate(prevProps: recharts_types_chart_generateCategoricalChart.CategoricalChartProps): void;
    componentWillUnmount(): void;
    getTooltipEventType(): recharts_types_util_types.TooltipEventType;
    getMouseInfo(event: recharts_types_chart_generateCategoricalChart.MousePointer): {
        xValue: any;
        yValue: any;
        chartX: number;
        chartY: number;
    } | {
        activeTooltipIndex: number;
        activeLabel: any;
        activePayload: any[];
        activeCoordinate: recharts_types_util_types.ChartCoordinate;
        chartX: number;
        chartY: number;
    };
    inRange(x: number, y: number, scale?: number): any;
    parseEventsOfWrapper(): any;
    addListener(): void;
    removeListener(): void;
    handleLegendBBoxUpdate: (box: DOMRect) => void;
    handleReceiveSyncEvent: (cId: string | number, data: recharts_types_chart_types.CategoricalChartState, emitter: Symbol) => void;
    handleBrushChange: ({ startIndex, endIndex }: {
        startIndex: number;
        endIndex: number;
    }) => void;
    handleMouseEnter: (e: React$1.MouseEvent<Element, MouseEvent>) => void;
    triggeredAfterMouseMove: (e: recharts_types_chart_generateCategoricalChart.MousePointer) => any;
    handleItemMouseEnter: (el: any) => void;
    handleItemMouseLeave: () => void;
    handleMouseMove: (e: recharts_types_chart_generateCategoricalChart.MousePointer & Partial<Omit<React$1.MouseEvent<Element, MouseEvent>, keyof recharts_types_chart_generateCategoricalChart.MousePointer>>) => void;
    handleMouseLeave: (e: any) => void;
    handleOuterEvent: (e: React$1.MouseEvent<Element, MouseEvent> | React$1.TouchEvent<Element>) => void;
    handleClick: (e: React$1.MouseEvent<Element, MouseEvent>) => void;
    handleMouseDown: (e: React$1.MouseEvent<Element, MouseEvent> | React$1.Touch) => void;
    handleMouseUp: (e: React$1.MouseEvent<Element, MouseEvent> | React$1.Touch) => void;
    handleTouchMove: (e: React$1.TouchEvent<Element>) => void;
    handleTouchStart: (e: React$1.TouchEvent<Element>) => void;
    handleTouchEnd: (e: React$1.TouchEvent<Element>) => void;
    handleDoubleClick: (e: React$1.MouseEvent<Element, MouseEvent>) => void;
    handleContextMenu: (e: React$1.MouseEvent<Element, MouseEvent>) => void;
    triggerSyncEvent: (data: recharts_types_chart_types.CategoricalChartState) => void;
    applySyncEvent: (data: recharts_types_chart_types.CategoricalChartState) => void;
    filterFormatItem(item: any, displayName: any, childIndex: any): any;
    renderCursor: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.JSX.Element;
    renderPolarAxis: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderPolarGrid: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderLegend: () => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderTooltip: () => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderBrush: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderReferenceElement: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderActivePoints: ({ item, activePoint, basePoint, childIndex, isRange }: any) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>[];
    renderGraphicChild: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
    renderCustomized: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderClipPath(): React$1.JSX.Element;
    getXScales(): {
        [x: string]: Function | recharts_types_util_types.ScaleType;
    };
    getYScales(): {
        [x: string]: Function | recharts_types_util_types.ScaleType;
    };
    getXScaleByAxisId(axisId: string): Function | recharts_types_util_types.ScaleType;
    getYScaleByAxisId(axisId: string): Function | recharts_types_util_types.ScaleType;
    getItemByXY(chartXY: {
        x: number;
        y: number;
    }): {
        graphicalItem: any;
        payload: any;
    };
    renderMap: {
        CartesianGrid: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
            once: boolean;
        };
        ReferenceArea: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        ReferenceLine: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        ReferenceDot: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        XAxis: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        YAxis: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        Brush: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
            once: boolean;
        };
        Bar: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Line: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Area: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Radar: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        RadialBar: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Scatter: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Pie: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Funnel: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Tooltip: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.JSX.Element;
            once: boolean;
        };
        PolarGrid: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
            once: boolean;
        };
        PolarAngleAxis: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        PolarRadiusAxis: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        Customized: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
    };
    render(): React$1.JSX.Element;
    context: unknown;
    setState<K extends keyof recharts_types_chart_types.CategoricalChartState>(state: recharts_types_chart_types.CategoricalChartState | ((prevState: Readonly<recharts_types_chart_types.CategoricalChartState>, props: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>) => recharts_types_chart_types.CategoricalChartState | Pick<recharts_types_chart_types.CategoricalChartState, K>) | Pick<recharts_types_chart_types.CategoricalChartState, K>, callback?: () => void): void;
    forceUpdate(callback?: () => void): void;
    readonly props: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>;
    state: Readonly<recharts_types_chart_types.CategoricalChartState>;
    refs: {
        [key: string]: React$1.ReactInstance;
    };
    shouldComponentUpdate?(nextProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, nextState: Readonly<recharts_types_chart_types.CategoricalChartState>, nextContext: any): boolean;
    componentDidCatch?(error: Error, errorInfo: React$1.ErrorInfo): void;
    componentWillMount?(): void;
    UNSAFE_componentWillMount?(): void;
    componentWillReceiveProps?(nextProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, nextContext: any): void;
    UNSAFE_componentWillReceiveProps?(nextProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, nextContext: any): void;
    componentWillUpdate?(nextProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, nextState: Readonly<recharts_types_chart_types.CategoricalChartState>, nextContext: any): void;
    UNSAFE_componentWillUpdate?(nextProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, nextState: Readonly<recharts_types_chart_types.CategoricalChartState>, nextContext: any): void;
}>>;

type BarBaseProps = Omit<BarProps, 'ref'>;
declare const BarBase: typeof Bar;

type BarChartProps = CategoricalChartProps;
declare const BarChartBase: React$1.ForwardRefExoticComponent<CategoricalChartProps & React$1.RefAttributes<{
    readonly eventEmitterSymbol: Symbol;
    clipPathId: string;
    accessibilityManager: recharts_types_chart_AccessibilityManager.AccessibilityManager;
    throttleTriggeredAfterMouseMove: lodash.DebouncedFunc<(e: recharts_types_chart_generateCategoricalChart.MousePointer) => any>;
    container?: HTMLElement;
    componentDidMount(): void;
    displayDefaultTooltip(): void;
    getSnapshotBeforeUpdate(prevProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, prevState: Readonly<recharts_types_chart_types.CategoricalChartState>): null;
    componentDidUpdate(prevProps: recharts_types_chart_generateCategoricalChart.CategoricalChartProps): void;
    componentWillUnmount(): void;
    getTooltipEventType(): recharts_types_util_types.TooltipEventType;
    getMouseInfo(event: recharts_types_chart_generateCategoricalChart.MousePointer): {
        xValue: any;
        yValue: any;
        chartX: number;
        chartY: number;
    } | {
        activeTooltipIndex: number;
        activeLabel: any;
        activePayload: any[];
        activeCoordinate: recharts_types_util_types.ChartCoordinate;
        chartX: number;
        chartY: number;
    };
    inRange(x: number, y: number, scale?: number): any;
    parseEventsOfWrapper(): any;
    addListener(): void;
    removeListener(): void;
    handleLegendBBoxUpdate: (box: DOMRect) => void;
    handleReceiveSyncEvent: (cId: string | number, data: recharts_types_chart_types.CategoricalChartState, emitter: Symbol) => void;
    handleBrushChange: ({ startIndex, endIndex }: {
        startIndex: number;
        endIndex: number;
    }) => void;
    handleMouseEnter: (e: React$1.MouseEvent<Element, MouseEvent>) => void;
    triggeredAfterMouseMove: (e: recharts_types_chart_generateCategoricalChart.MousePointer) => any;
    handleItemMouseEnter: (el: any) => void;
    handleItemMouseLeave: () => void;
    handleMouseMove: (e: recharts_types_chart_generateCategoricalChart.MousePointer & Partial<Omit<React$1.MouseEvent<Element, MouseEvent>, keyof recharts_types_chart_generateCategoricalChart.MousePointer>>) => void;
    handleMouseLeave: (e: any) => void;
    handleOuterEvent: (e: React$1.MouseEvent<Element, MouseEvent> | React$1.TouchEvent<Element>) => void;
    handleClick: (e: React$1.MouseEvent<Element, MouseEvent>) => void;
    handleMouseDown: (e: React$1.MouseEvent<Element, MouseEvent> | React$1.Touch) => void;
    handleMouseUp: (e: React$1.MouseEvent<Element, MouseEvent> | React$1.Touch) => void;
    handleTouchMove: (e: React$1.TouchEvent<Element>) => void;
    handleTouchStart: (e: React$1.TouchEvent<Element>) => void;
    handleTouchEnd: (e: React$1.TouchEvent<Element>) => void;
    handleDoubleClick: (e: React$1.MouseEvent<Element, MouseEvent>) => void;
    handleContextMenu: (e: React$1.MouseEvent<Element, MouseEvent>) => void;
    triggerSyncEvent: (data: recharts_types_chart_types.CategoricalChartState) => void;
    applySyncEvent: (data: recharts_types_chart_types.CategoricalChartState) => void;
    filterFormatItem(item: any, displayName: any, childIndex: any): any;
    renderCursor: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.JSX.Element;
    renderPolarAxis: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderPolarGrid: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderLegend: () => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderTooltip: () => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderBrush: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderReferenceElement: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderActivePoints: ({ item, activePoint, basePoint, childIndex, isRange }: any) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>[];
    renderGraphicChild: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
    renderCustomized: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderClipPath(): React$1.JSX.Element;
    getXScales(): {
        [x: string]: Function | recharts_types_util_types.ScaleType;
    };
    getYScales(): {
        [x: string]: Function | recharts_types_util_types.ScaleType;
    };
    getXScaleByAxisId(axisId: string): Function | recharts_types_util_types.ScaleType;
    getYScaleByAxisId(axisId: string): Function | recharts_types_util_types.ScaleType;
    getItemByXY(chartXY: {
        x: number;
        y: number;
    }): {
        graphicalItem: any;
        payload: any;
    };
    renderMap: {
        CartesianGrid: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
            once: boolean;
        };
        ReferenceArea: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        ReferenceLine: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        ReferenceDot: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        XAxis: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        YAxis: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        Brush: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
            once: boolean;
        };
        Bar: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Line: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Area: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Radar: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        RadialBar: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Scatter: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Pie: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Funnel: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Tooltip: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.JSX.Element;
            once: boolean;
        };
        PolarGrid: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
            once: boolean;
        };
        PolarAngleAxis: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        PolarRadiusAxis: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        Customized: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
    };
    render(): React$1.JSX.Element;
    context: unknown;
    setState<K extends keyof recharts_types_chart_types.CategoricalChartState>(state: recharts_types_chart_types.CategoricalChartState | ((prevState: Readonly<recharts_types_chart_types.CategoricalChartState>, props: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>) => recharts_types_chart_types.CategoricalChartState | Pick<recharts_types_chart_types.CategoricalChartState, K>) | Pick<recharts_types_chart_types.CategoricalChartState, K>, callback?: () => void): void;
    forceUpdate(callback?: () => void): void;
    readonly props: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>;
    state: Readonly<recharts_types_chart_types.CategoricalChartState>;
    refs: {
        [key: string]: React$1.ReactInstance;
    };
    shouldComponentUpdate?(nextProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, nextState: Readonly<recharts_types_chart_types.CategoricalChartState>, nextContext: any): boolean;
    componentDidCatch?(error: Error, errorInfo: React$1.ErrorInfo): void;
    componentWillMount?(): void;
    UNSAFE_componentWillMount?(): void;
    componentWillReceiveProps?(nextProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, nextContext: any): void;
    UNSAFE_componentWillReceiveProps?(nextProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, nextContext: any): void;
    componentWillUpdate?(nextProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, nextState: Readonly<recharts_types_chart_types.CategoricalChartState>, nextContext: any): void;
    UNSAFE_componentWillUpdate?(nextProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, nextState: Readonly<recharts_types_chart_types.CategoricalChartState>, nextContext: any): void;
}>>;

type CartesianGridBaseProps = CartesianGridProps;
declare const CartesianGridBase: typeof CartesianGrid;

type CellBaseProps = CellProps;
declare const CellBase: React$1.FunctionComponent<CellProps>;

type LegendBaseProps = LegendProps;
declare const LegendBase: typeof Legend;

type LineBaseProps = Omit<LineProps, 'ref'>;
declare const LineBase: typeof Line;

type LineChartBaseProps = CategoricalChartProps;
declare const LineChartBase: ({ children, ...props }: LineChartBaseProps) => react_jsx_runtime.JSX.Element;

type PieBaseProps = Omit<PieProps, 'ref'>;
declare const PieBase: typeof Pie;

type PieChartBaseProps = CategoricalChartProps;
declare const PieChartBase: React$1.ForwardRefExoticComponent<CategoricalChartProps & React$1.RefAttributes<{
    readonly eventEmitterSymbol: Symbol;
    clipPathId: string;
    accessibilityManager: recharts_types_chart_AccessibilityManager.AccessibilityManager;
    throttleTriggeredAfterMouseMove: lodash.DebouncedFunc<(e: recharts_types_chart_generateCategoricalChart.MousePointer) => any>;
    container?: HTMLElement;
    componentDidMount(): void;
    displayDefaultTooltip(): void;
    getSnapshotBeforeUpdate(prevProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, prevState: Readonly<recharts_types_chart_types.CategoricalChartState>): null;
    componentDidUpdate(prevProps: recharts_types_chart_generateCategoricalChart.CategoricalChartProps): void;
    componentWillUnmount(): void;
    getTooltipEventType(): recharts_types_util_types.TooltipEventType;
    getMouseInfo(event: recharts_types_chart_generateCategoricalChart.MousePointer): {
        xValue: any;
        yValue: any;
        chartX: number;
        chartY: number;
    } | {
        activeTooltipIndex: number;
        activeLabel: any;
        activePayload: any[];
        activeCoordinate: recharts_types_util_types.ChartCoordinate;
        chartX: number;
        chartY: number;
    };
    inRange(x: number, y: number, scale?: number): any;
    parseEventsOfWrapper(): any;
    addListener(): void;
    removeListener(): void;
    handleLegendBBoxUpdate: (box: DOMRect) => void;
    handleReceiveSyncEvent: (cId: string | number, data: recharts_types_chart_types.CategoricalChartState, emitter: Symbol) => void;
    handleBrushChange: ({ startIndex, endIndex }: {
        startIndex: number;
        endIndex: number;
    }) => void;
    handleMouseEnter: (e: React$1.MouseEvent<Element, MouseEvent>) => void;
    triggeredAfterMouseMove: (e: recharts_types_chart_generateCategoricalChart.MousePointer) => any;
    handleItemMouseEnter: (el: any) => void;
    handleItemMouseLeave: () => void;
    handleMouseMove: (e: recharts_types_chart_generateCategoricalChart.MousePointer & Partial<Omit<React$1.MouseEvent<Element, MouseEvent>, keyof recharts_types_chart_generateCategoricalChart.MousePointer>>) => void;
    handleMouseLeave: (e: any) => void;
    handleOuterEvent: (e: React$1.MouseEvent<Element, MouseEvent> | React$1.TouchEvent<Element>) => void;
    handleClick: (e: React$1.MouseEvent<Element, MouseEvent>) => void;
    handleMouseDown: (e: React$1.MouseEvent<Element, MouseEvent> | React$1.Touch) => void;
    handleMouseUp: (e: React$1.MouseEvent<Element, MouseEvent> | React$1.Touch) => void;
    handleTouchMove: (e: React$1.TouchEvent<Element>) => void;
    handleTouchStart: (e: React$1.TouchEvent<Element>) => void;
    handleTouchEnd: (e: React$1.TouchEvent<Element>) => void;
    handleDoubleClick: (e: React$1.MouseEvent<Element, MouseEvent>) => void;
    handleContextMenu: (e: React$1.MouseEvent<Element, MouseEvent>) => void;
    triggerSyncEvent: (data: recharts_types_chart_types.CategoricalChartState) => void;
    applySyncEvent: (data: recharts_types_chart_types.CategoricalChartState) => void;
    filterFormatItem(item: any, displayName: any, childIndex: any): any;
    renderCursor: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.JSX.Element;
    renderPolarAxis: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderPolarGrid: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderLegend: () => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderTooltip: () => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderBrush: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderReferenceElement: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderActivePoints: ({ item, activePoint, basePoint, childIndex, isRange }: any) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>[];
    renderGraphicChild: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
    renderCustomized: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
    renderClipPath(): React$1.JSX.Element;
    getXScales(): {
        [x: string]: Function | recharts_types_util_types.ScaleType;
    };
    getYScales(): {
        [x: string]: Function | recharts_types_util_types.ScaleType;
    };
    getXScaleByAxisId(axisId: string): Function | recharts_types_util_types.ScaleType;
    getYScaleByAxisId(axisId: string): Function | recharts_types_util_types.ScaleType;
    getItemByXY(chartXY: {
        x: number;
        y: number;
    }): {
        graphicalItem: any;
        payload: any;
    };
    renderMap: {
        CartesianGrid: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
            once: boolean;
        };
        ReferenceArea: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        ReferenceLine: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        ReferenceDot: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        XAxis: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        YAxis: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        Brush: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
            once: boolean;
        };
        Bar: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Line: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Area: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Radar: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        RadialBar: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Scatter: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Pie: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Funnel: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => any[];
        };
        Tooltip: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.JSX.Element;
            once: boolean;
        };
        PolarGrid: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
            once: boolean;
        };
        PolarAngleAxis: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        PolarRadiusAxis: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
        Customized: {
            handler: (element: React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>, displayName: string, index: number) => React$1.ReactElement<any, string | React$1.JSXElementConstructor<any>>;
        };
    };
    render(): React$1.JSX.Element;
    context: unknown;
    setState<K extends keyof recharts_types_chart_types.CategoricalChartState>(state: recharts_types_chart_types.CategoricalChartState | ((prevState: Readonly<recharts_types_chart_types.CategoricalChartState>, props: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>) => recharts_types_chart_types.CategoricalChartState | Pick<recharts_types_chart_types.CategoricalChartState, K>) | Pick<recharts_types_chart_types.CategoricalChartState, K>, callback?: () => void): void;
    forceUpdate(callback?: () => void): void;
    readonly props: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>;
    state: Readonly<recharts_types_chart_types.CategoricalChartState>;
    refs: {
        [key: string]: React$1.ReactInstance;
    };
    shouldComponentUpdate?(nextProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, nextState: Readonly<recharts_types_chart_types.CategoricalChartState>, nextContext: any): boolean;
    componentDidCatch?(error: Error, errorInfo: React$1.ErrorInfo): void;
    componentWillMount?(): void;
    UNSAFE_componentWillMount?(): void;
    componentWillReceiveProps?(nextProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, nextContext: any): void;
    UNSAFE_componentWillReceiveProps?(nextProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, nextContext: any): void;
    componentWillUpdate?(nextProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, nextState: Readonly<recharts_types_chart_types.CategoricalChartState>, nextContext: any): void;
    UNSAFE_componentWillUpdate?(nextProps: Readonly<recharts_types_chart_generateCategoricalChart.CategoricalChartProps>, nextState: Readonly<recharts_types_chart_types.CategoricalChartState>, nextContext: any): void;
}>>;

type RectangleBaseProps = RectangleProps;
declare const RectangleBase: React$1.FC<RectangleProps>;

type ReferenceLineBaseProps = ReferenceLineProps;
declare const ReferenceLineBase: typeof ReferenceLine;

type ResponsiveContainerBaseProps = Omit<ResponsiveContainerProps, 'children'>;
declare const ResponsiveContainerBase: React$1.ForwardRefExoticComponent<ResponsiveContainerProps & React$1.RefAttributes<HTMLDivElement | {
    current: HTMLDivElement;
}>>;

declare const TooltipBase: typeof Tooltip$1;

type XAxisBaseProps = XAxisProps;
declare const XAxisBase: typeof XAxis;

type YAxisBaseProps = YAxisProps;
declare const YAxisBase: typeof YAxis;

interface SimpleAreaChartProps {
    chartProps: AreaChartBaseProps;
    areas: Array<AreaBaseProps>;
    cartesianProps?: CartesianGridBaseProps;
    xAxisKey: string;
}
declare const SimpleAreaChart: ({ chartProps, xAxisKey, cartesianProps, areas, ...props }: SimpleAreaChartProps) => react_jsx_runtime.JSX.Element;

type AutoCompleteProps<T extends string> = {
    selectedValue: T;
    onSelectedValueChange: (value: T) => void;
    searchValue: string;
    onSearchValueChange: (value: string) => void;
    items: {
        value: T;
        label: string;
    }[];
    isLoading?: boolean;
    emptyMessage?: string;
    placeholder?: string;
};
declare function AutoComplete<T extends string>({ selectedValue, onSelectedValueChange, searchValue, onSearchValueChange, items, isLoading, emptyMessage, placeholder, }: AutoCompleteProps<T>): react_jsx_runtime.JSX.Element;

declare const THEMES: {
    readonly light: "";
    readonly dark: ".dark";
};
type ChartConfig = {
    [k in string]: {
        label?: React$1.ReactNode;
        icon?: React$1.ComponentType;
    } & ({
        color?: string;
        theme?: never;
    } | {
        color?: never;
        theme: Record<keyof typeof THEMES, string>;
    });
};
declare const ChartContainer: React$1.ForwardRefExoticComponent<Omit<React$1.ClassAttributes<HTMLDivElement> & React$1.HTMLAttributes<HTMLDivElement> & {
    config: ChartConfig;
    children: React$1.ComponentProps<typeof RechartsPrimitive.ResponsiveContainer>["children"];
}, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const ChartStyle: ({ id, config }: {
    id: string;
    config: ChartConfig;
}) => react_jsx_runtime.JSX.Element | null;
declare const ChartTooltip: typeof RechartsPrimitive.Tooltip;
declare const ChartTooltipContent: React$1.ForwardRefExoticComponent<Omit<RechartsPrimitive.DefaultTooltipContentProps<recharts_types_component_DefaultTooltipContent.ValueType, recharts_types_component_DefaultTooltipContent.NameType> & {
    accessibilityLayer?: boolean;
    active?: boolean | undefined;
    includeHidden?: boolean | undefined;
    allowEscapeViewBox?: recharts_types_util_types.AllowInDimension;
    animationDuration?: recharts_types_util_types.AnimationDuration;
    animationEasing?: recharts_types_util_types.AnimationTiming;
    content?: recharts_types_component_Tooltip.ContentType<recharts_types_component_DefaultTooltipContent.ValueType, recharts_types_component_DefaultTooltipContent.NameType> | undefined;
    coordinate?: Partial<recharts_types_util_types.Coordinate>;
    cursor?: boolean | React$1.ReactElement | React$1.SVGProps<SVGElement>;
    filterNull?: boolean;
    defaultIndex?: number;
    isAnimationActive?: boolean;
    offset?: number;
    payloadUniqBy?: recharts_types_util_payload_getUniqPayload.UniqueOption<recharts_types_component_DefaultTooltipContent.Payload<recharts_types_component_DefaultTooltipContent.ValueType, recharts_types_component_DefaultTooltipContent.NameType>> | undefined;
    position?: Partial<recharts_types_util_types.Coordinate>;
    reverseDirection?: recharts_types_util_types.AllowInDimension;
    shared?: boolean;
    trigger?: "hover" | "click";
    useTranslate3d?: boolean;
    viewBox?: recharts_types_util_types.CartesianViewBox;
    wrapperStyle?: React$1.CSSProperties;
} & React$1.ClassAttributes<HTMLDivElement> & React$1.HTMLAttributes<HTMLDivElement> & {
    hideLabel?: boolean;
    hideIndicator?: boolean;
    indicator?: "line" | "dot" | "dashed";
    nameKey?: string;
    labelKey?: string;
}, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const ChartLegend: typeof RechartsPrimitive.Legend;
declare const ChartLegendContent: React$1.ForwardRefExoticComponent<Omit<React$1.ClassAttributes<HTMLDivElement> & React$1.HTMLAttributes<HTMLDivElement> & Pick<RechartsPrimitive.LegendProps, "verticalAlign" | "payload"> & {
    hideIcon?: boolean;
    nameKey?: string;
}, "ref"> & React$1.RefAttributes<HTMLDivElement>>;

interface BarChartLabelProps {
    chartConfig: ChartConfig;
    chartData: MustBeAny[];
    dataKey: DataKey<MustBeAny>;
    dataValue: DataKey<MustBeAny>;
    colorBar: string;
}
declare const BarChartLabel: ({ chartConfig, chartData, dataKey, dataValue, colorBar, }: BarChartLabelProps) => react_jsx_runtime.JSX.Element;

interface SimpleBarChartProps {
    chartConfig: ChartConfig;
    chartData: any[];
    barProps: BarBaseProps[];
    xAxis: XAxis;
    yAxis?: YAxis;
    legend?: Omit<LegendBaseProps, 'ref'>;
}
declare const SimpleBarChart: ({ chartConfig, chartData, barProps, xAxis, yAxis, legend, ...props }: SimpleBarChartProps & React.ComponentProps<"div">) => react_jsx_runtime.JSX.Element;

interface SimplePieChartProps {
    chartProps: Omit<PieBaseProps, 'data' | 'dataKey' | 'nameKey'> & {
        data: {
            label: string;
            value: number;
        }[];
    };
    legend?: boolean | Omit<LegendBaseProps, 'ref'>;
}
declare const SimplePieChart: ({ chartProps, legend, ...props }: SimplePieChartProps & React.ComponentProps<"div">) => react_jsx_runtime.JSX.Element;

interface StackedBarChartProps {
    barChart: BarChartProps;
    chartConfig: ChartConfig;
    xAxis: XAxis;
    yAxis?: YAxis;
    isLegend?: boolean;
    barProps: BarBaseProps[];
}
declare const StackedBarChart: ({ barChart, chartConfig, barProps, xAxis, yAxis, isLegend, ...props }: StackedBarChartProps & React.ComponentProps<"div">) => react_jsx_runtime.JSX.Element;

interface HourRangePickerProps {
    initialValue?: {
        from: Date;
        to: Date;
    };
    onValueChange: (timeRange: {
        from: Date;
        to: Date;
    }) => void;
    startTimePlaceholder?: string;
    endTimePlaceholder?: string;
}
declare const HourRangePicker: React.FC<HourRangePickerProps>;

declare const Accordion: React$1.ForwardRefExoticComponent<(AccordionPrimitive.AccordionSingleProps | AccordionPrimitive.AccordionMultipleProps) & React$1.RefAttributes<HTMLDivElement>>;
declare const AccordionItem: React$1.ForwardRefExoticComponent<Omit<AccordionPrimitive.AccordionItemProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const AccordionTrigger: React$1.ForwardRefExoticComponent<Omit<AccordionPrimitive.AccordionTriggerProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & React$1.RefAttributes<HTMLButtonElement>>;
declare const AccordionContent: React$1.ForwardRefExoticComponent<Omit<AccordionPrimitive.AccordionContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;

declare const AlertDialog: React$1.FC<AlertDialogPrimitive.AlertDialogProps>;
declare const AlertDialogTrigger: React$1.ForwardRefExoticComponent<AlertDialogPrimitive.AlertDialogTriggerProps & React$1.RefAttributes<HTMLButtonElement>>;
declare const AlertDialogPortal: React$1.FC<AlertDialogPrimitive.AlertDialogPortalProps>;
declare const AlertDialogOverlay: React$1.ForwardRefExoticComponent<Omit<AlertDialogPrimitive.AlertDialogOverlayProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const AlertDialogContent: React$1.ForwardRefExoticComponent<Omit<AlertDialogPrimitive.AlertDialogContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const AlertDialogHeader: {
    ({ className, ...props }: React$1.HTMLAttributes<HTMLDivElement>): react_jsx_runtime.JSX.Element;
    displayName: string;
};
declare const AlertDialogFooter: {
    ({ className, ...props }: React$1.HTMLAttributes<HTMLDivElement>): react_jsx_runtime.JSX.Element;
    displayName: string;
};
declare const AlertDialogTitle: React$1.ForwardRefExoticComponent<Omit<AlertDialogPrimitive.AlertDialogTitleProps & React$1.RefAttributes<HTMLHeadingElement>, "ref"> & React$1.RefAttributes<HTMLHeadingElement>>;
declare const AlertDialogDescription: React$1.ForwardRefExoticComponent<Omit<AlertDialogPrimitive.AlertDialogDescriptionProps & React$1.RefAttributes<HTMLParagraphElement>, "ref"> & React$1.RefAttributes<HTMLParagraphElement>>;
declare const AlertDialogAction: React$1.ForwardRefExoticComponent<Omit<AlertDialogPrimitive.AlertDialogActionProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & React$1.RefAttributes<HTMLButtonElement>>;
declare const AlertDialogCancel: React$1.ForwardRefExoticComponent<Omit<AlertDialogPrimitive.AlertDialogCancelProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & React$1.RefAttributes<HTMLButtonElement>>;

declare const Alert: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLDivElement> & VariantProps<(props?: ({
    variant?: "default" | "destructive" | null | undefined;
} & class_variance_authority_types.ClassProp) | undefined) => string> & React$1.RefAttributes<HTMLDivElement>>;
declare const AlertTitle: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLHeadingElement> & React$1.RefAttributes<HTMLParagraphElement>>;
declare const AlertDescription: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLParagraphElement> & React$1.RefAttributes<HTMLParagraphElement>>;

declare const AspectRatio: React$1.ForwardRefExoticComponent<AspectRatioPrimitive.AspectRatioProps & React$1.RefAttributes<HTMLDivElement>>;

declare const Avatar: React$1.ForwardRefExoticComponent<Omit<AvatarPrimitive.AvatarProps & React$1.RefAttributes<HTMLSpanElement>, "ref"> & React$1.RefAttributes<HTMLSpanElement>>;
declare const AvatarImage: React$1.ForwardRefExoticComponent<Omit<AvatarPrimitive.AvatarImageProps & React$1.RefAttributes<HTMLImageElement>, "ref"> & React$1.RefAttributes<HTMLImageElement>>;
declare const AvatarFallback: React$1.ForwardRefExoticComponent<Omit<AvatarPrimitive.AvatarFallbackProps & React$1.RefAttributes<HTMLSpanElement>, "ref"> & React$1.RefAttributes<HTMLSpanElement>>;

declare const badgeVariants: (props?: ({
    variant?: "default" | "destructive" | "outline" | "secondary" | null | undefined;
} & class_variance_authority_types.ClassProp) | undefined) => string;
interface BadgeProps extends React$1.HTMLAttributes<HTMLDivElement>, VariantProps<typeof badgeVariants> {
}
declare function Badge({ className, variant, ...props }: BadgeProps): react_jsx_runtime.JSX.Element;

declare const Breadcrumb: React$1.ForwardRefExoticComponent<Omit<React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLElement>, HTMLElement>, "ref"> & {
    separator?: React$1.ReactNode;
} & React$1.RefAttributes<HTMLElement>>;
interface BreadcrumbListProps extends React$1.ComponentPropsWithoutRef<'ol'> {
    className?: string;
}
declare const BreadcrumbList: React$1.ForwardRefExoticComponent<BreadcrumbListProps & React$1.RefAttributes<HTMLOListElement>>;
interface BreadcrumbItemProps extends React$1.ComponentPropsWithoutRef<'li'> {
    className?: string;
}
declare const BreadcrumbItem: React$1.ForwardRefExoticComponent<BreadcrumbItemProps & React$1.RefAttributes<HTMLLIElement>>;
declare const BreadcrumbLink: {
    ({ className, children, ...props }: LinkProps): react_jsx_runtime.JSX.Element;
    displayName: string;
};
interface BreadcrumbPageProps extends React$1.ComponentPropsWithoutRef<'span'> {
    className?: string;
}
declare const BreadcrumbPage: React$1.ForwardRefExoticComponent<BreadcrumbPageProps & React$1.RefAttributes<HTMLSpanElement>>;
interface BreadcrumbSeparatorProps extends React$1.ComponentProps<'li'> {
    className?: string;
    children?: React$1.ReactNode;
}
declare const BreadcrumbSeparator: {
    ({ children, className, ...props }: BreadcrumbSeparatorProps): react_jsx_runtime.JSX.Element;
    displayName: string;
};
interface BreadcrumbEllipsisProps extends React$1.ComponentProps<'span'> {
    className?: string;
}
declare const BreadcrumbEllipsis: {
    ({ className, ...props }: BreadcrumbEllipsisProps): react_jsx_runtime.JSX.Element;
    displayName: string;
};

declare const buttonVariants: (props?: ({
    variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | null | undefined;
    size?: "default" | "sm" | "lg" | "icon" | null | undefined;
} & class_variance_authority_types.ClassProp) | undefined) => string;
interface ButtonProps extends React$1.ButtonHTMLAttributes<HTMLButtonElement>, VariantProps<typeof buttonVariants> {
    asChild?: boolean;
}
declare const Button: React$1.ForwardRefExoticComponent<ButtonProps & React$1.RefAttributes<HTMLButtonElement>>;

type CalendarProps = React$1.ComponentProps<typeof DayPicker>;
declare function Calendar({ className, classNames, showOutsideDays, ...props }: CalendarProps): react_jsx_runtime.JSX.Element;
declare namespace Calendar {
    var displayName: string;
}

declare const Card: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLDivElement> & React$1.RefAttributes<HTMLDivElement>>;
declare const CardHeader: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLDivElement> & React$1.RefAttributes<HTMLDivElement>>;
declare const CardTitle: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLHeadingElement> & React$1.RefAttributes<HTMLParagraphElement>>;
declare const CardDescription: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLParagraphElement> & React$1.RefAttributes<HTMLParagraphElement>>;
declare const CardContent: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLDivElement> & React$1.RefAttributes<HTMLDivElement>>;
declare const CardFooter: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLDivElement> & React$1.RefAttributes<HTMLDivElement>>;

type CarouselApi = UseEmblaCarouselType[1];
type UseCarouselParameters = Parameters<typeof useEmblaCarousel>;
type CarouselOptions = UseCarouselParameters[0];
type CarouselPlugin = UseCarouselParameters[1];
type CarouselProps = {
    opts?: CarouselOptions;
    plugins?: CarouselPlugin;
    orientation?: 'horizontal' | 'vertical';
    setApi?: (api: CarouselApi) => void;
};
declare const Carousel: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLDivElement> & CarouselProps & React$1.RefAttributes<HTMLDivElement>>;
declare const CarouselContent: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLDivElement> & React$1.RefAttributes<HTMLDivElement>>;
declare const CarouselItem: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLDivElement> & React$1.RefAttributes<HTMLDivElement>>;
declare const CarouselPrevious: React$1.ForwardRefExoticComponent<Omit<ButtonProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & React$1.RefAttributes<HTMLButtonElement>>;
declare const CarouselNext: React$1.ForwardRefExoticComponent<Omit<ButtonProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & React$1.RefAttributes<HTMLButtonElement>>;

declare const Checkbox: React$1.ForwardRefExoticComponent<Omit<CheckboxPrimitive.CheckboxProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & React$1.RefAttributes<HTMLButtonElement>>;

declare const Collapsible: React$1.ForwardRefExoticComponent<CollapsiblePrimitive.CollapsibleProps & React$1.RefAttributes<HTMLDivElement>>;
declare const CollapsibleTrigger: React$1.ForwardRefExoticComponent<CollapsiblePrimitive.CollapsibleTriggerProps & React$1.RefAttributes<HTMLButtonElement>>;
declare const CollapsibleContent: React$1.ForwardRefExoticComponent<CollapsiblePrimitive.CollapsibleContentProps & React$1.RefAttributes<HTMLDivElement>>;

declare const Command: React$1.ForwardRefExoticComponent<Omit<{
    children?: React$1.ReactNode;
} & React$1.HTMLAttributes<HTMLDivElement> & {
    label?: string;
    shouldFilter?: boolean;
    filter?: (value: string, search: string) => number;
    defaultValue?: string;
    value?: string;
    onValueChange?: (value: string) => void;
    loop?: boolean;
    vimBindings?: boolean;
} & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
interface CommandDialogProps extends DialogProps {
}
declare const CommandDialog: ({ children, ...props }: CommandDialogProps) => react_jsx_runtime.JSX.Element;
declare const CommandInput: React$1.ForwardRefExoticComponent<Omit<Omit<React$1.InputHTMLAttributes<HTMLInputElement>, "type" | "value" | "onChange"> & {
    value?: string;
    onValueChange?: (search: string) => void;
} & React$1.RefAttributes<HTMLInputElement>, "ref"> & React$1.RefAttributes<HTMLInputElement>>;
declare const CommandList: React$1.ForwardRefExoticComponent<Omit<{
    children?: React$1.ReactNode;
} & React$1.HTMLAttributes<HTMLDivElement> & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const CommandEmpty: React$1.ForwardRefExoticComponent<Omit<{
    children?: React$1.ReactNode;
} & React$1.HTMLAttributes<HTMLDivElement> & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const CommandGroup: React$1.ForwardRefExoticComponent<Omit<{
    children?: React$1.ReactNode;
} & Omit<React$1.HTMLAttributes<HTMLDivElement>, "value" | "heading"> & {
    heading?: React$1.ReactNode;
    value?: string;
    forceMount?: boolean;
} & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const CommandSeparator: React$1.ForwardRefExoticComponent<Omit<React$1.HTMLAttributes<HTMLDivElement> & {
    alwaysRender?: boolean;
} & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const CommandItem: React$1.ForwardRefExoticComponent<Omit<{
    children?: React$1.ReactNode;
} & Omit<React$1.HTMLAttributes<HTMLDivElement>, "disabled" | "value" | "onSelect"> & {
    disabled?: boolean;
    onSelect?: (value: string) => void;
    value?: string;
    forceMount?: boolean;
} & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const CommandShortcut: {
    ({ className, ...props }: React$1.HTMLAttributes<HTMLSpanElement>): react_jsx_runtime.JSX.Element;
    displayName: string;
};

declare const containerVariants: (props?: ({
    variant?: "fullMobileConstrainedPadded" | "constrainedPadded" | "fullMobileBreakpointPadded" | "breakpointPadded" | "narrowConstrainedPadded" | null | undefined;
} & class_variance_authority_types.ClassProp) | undefined) => string;
interface ContainerProps extends React$1.HTMLAttributes<HTMLDivElement>, VariantProps<typeof containerVariants> {
    asChild?: boolean;
}
declare const Container: React$1.FC<ContainerProps>;

declare const Dialog: React$1.FC<DialogPrimitive.DialogProps>;
declare const DialogTrigger: React$1.ForwardRefExoticComponent<DialogPrimitive.DialogTriggerProps & React$1.RefAttributes<HTMLButtonElement>>;
declare const DialogPortal: React$1.FC<DialogPrimitive.DialogPortalProps>;
declare const DialogClose: React$1.ForwardRefExoticComponent<DialogPrimitive.DialogCloseProps & React$1.RefAttributes<HTMLButtonElement>>;
declare const DialogOverlay: React$1.ForwardRefExoticComponent<Omit<DialogPrimitive.DialogOverlayProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const DialogContent: React$1.ForwardRefExoticComponent<Omit<DialogPrimitive.DialogContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const DialogHeader: {
    ({ className, ...props }: React$1.HTMLAttributes<HTMLDivElement>): react_jsx_runtime.JSX.Element;
    displayName: string;
};
declare const DialogFooter: {
    ({ className, ...props }: React$1.HTMLAttributes<HTMLDivElement>): react_jsx_runtime.JSX.Element;
    displayName: string;
};
declare const DialogTitle: React$1.ForwardRefExoticComponent<Omit<DialogPrimitive.DialogTitleProps & React$1.RefAttributes<HTMLHeadingElement>, "ref"> & React$1.RefAttributes<HTMLHeadingElement>>;
declare const DialogDescription: React$1.ForwardRefExoticComponent<Omit<DialogPrimitive.DialogDescriptionProps & React$1.RefAttributes<HTMLParagraphElement>, "ref"> & React$1.RefAttributes<HTMLParagraphElement>>;

declare const DropdownMenu: React$1.FC<DropdownMenuPrimitive.DropdownMenuProps>;
declare const DropdownMenuTrigger: React$1.ForwardRefExoticComponent<DropdownMenuPrimitive.DropdownMenuTriggerProps & React$1.RefAttributes<HTMLButtonElement>>;
declare const DropdownMenuGroup: React$1.ForwardRefExoticComponent<DropdownMenuPrimitive.DropdownMenuGroupProps & React$1.RefAttributes<HTMLDivElement>>;
declare const DropdownMenuPortal: React$1.FC<DropdownMenuPrimitive.DropdownMenuPortalProps>;
declare const DropdownMenuSub: React$1.FC<DropdownMenuPrimitive.DropdownMenuSubProps>;
declare const DropdownMenuRadioGroup: React$1.ForwardRefExoticComponent<DropdownMenuPrimitive.DropdownMenuRadioGroupProps & React$1.RefAttributes<HTMLDivElement>>;
declare const DropdownMenuSubTrigger: React$1.ForwardRefExoticComponent<Omit<DropdownMenuPrimitive.DropdownMenuSubTriggerProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & {
    inset?: boolean;
} & React$1.RefAttributes<HTMLDivElement>>;
declare const DropdownMenuSubContent: React$1.ForwardRefExoticComponent<Omit<DropdownMenuPrimitive.DropdownMenuSubContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const DropdownMenuContent: React$1.ForwardRefExoticComponent<Omit<DropdownMenuPrimitive.DropdownMenuContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const DropdownMenuItem: React$1.ForwardRefExoticComponent<Omit<DropdownMenuPrimitive.DropdownMenuItemProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & {
    inset?: boolean;
} & React$1.RefAttributes<HTMLDivElement>>;
declare const DropdownMenuCheckboxItem: React$1.ForwardRefExoticComponent<Omit<DropdownMenuPrimitive.DropdownMenuCheckboxItemProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const DropdownMenuRadioItem: React$1.ForwardRefExoticComponent<Omit<DropdownMenuPrimitive.DropdownMenuRadioItemProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const DropdownMenuLabel: React$1.ForwardRefExoticComponent<Omit<DropdownMenuPrimitive.DropdownMenuLabelProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & {
    inset?: boolean;
} & React$1.RefAttributes<HTMLDivElement>>;
declare const DropdownMenuSeparator: React$1.ForwardRefExoticComponent<Omit<DropdownMenuPrimitive.DropdownMenuSeparatorProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const DropdownMenuShortcut: {
    ({ className, ...props }: React$1.HTMLAttributes<HTMLSpanElement>): react_jsx_runtime.JSX.Element;
    displayName: string;
};

interface InputProps$1 extends React$1.InputHTMLAttributes<HTMLInputElement> {
    startAdornment?: JSX.Element;
    endAdornment?: JSX.Element;
    backgroundColor?: string;
}
declare const Input$1: React$1.ForwardRefExoticComponent<InputProps$1 & React$1.RefAttributes<HTMLInputElement>>;

declare const Label: React$1.ForwardRefExoticComponent<Omit<LabelPrimitive.LabelProps & React$1.RefAttributes<HTMLLabelElement>, "ref"> & VariantProps<(props?: class_variance_authority_types.ClassProp | undefined) => string> & React$1.RefAttributes<HTMLLabelElement>>;

declare const Progress: React$1.ForwardRefExoticComponent<Omit<ProgressPrimitive.ProgressProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;

declare const RadioGroup: React$1.ForwardRefExoticComponent<Omit<RadioGroupPrimitive.RadioGroupProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const RadioGroupItem: React$1.ForwardRefExoticComponent<Omit<RadioGroupPrimitive.RadioGroupItemProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & React$1.RefAttributes<HTMLButtonElement>>;

declare function Skeleton({ className, ...props }: React.HTMLAttributes<HTMLDivElement>): react_jsx_runtime.JSX.Element;

declare const Slider: React$1.ForwardRefExoticComponent<Omit<SliderPrimitive.SliderProps & React$1.RefAttributes<HTMLSpanElement>, "ref"> & React$1.RefAttributes<HTMLSpanElement>>;

declare const Switch: React$1.ForwardRefExoticComponent<Omit<SwitchPrimitives.SwitchProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & React$1.RefAttributes<HTMLButtonElement>>;

declare const Drawer: {
    ({ shouldScaleBackground, ...props }: React$1.ComponentProps<typeof Drawer$1.Root>): react_jsx_runtime.JSX.Element;
    displayName: string;
};
declare const DrawerTrigger: React$1.ForwardRefExoticComponent<DialogPrimitive.DialogTriggerProps & React$1.RefAttributes<HTMLButtonElement>>;
declare const DrawerPortal: typeof vaul.Portal;
declare const DrawerClose: React$1.ForwardRefExoticComponent<DialogPrimitive.DialogCloseProps & React$1.RefAttributes<HTMLButtonElement>>;
declare const DrawerOverlay: React$1.ForwardRefExoticComponent<Omit<Omit<DialogPrimitive.DialogOverlayProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const DrawerContent: React$1.ForwardRefExoticComponent<Omit<Omit<DialogPrimitive.DialogContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const DrawerHeader: {
    ({ className, ...props }: React$1.HTMLAttributes<HTMLDivElement>): react_jsx_runtime.JSX.Element;
    displayName: string;
};
declare const DrawerFooter: {
    ({ className, ...props }: React$1.HTMLAttributes<HTMLDivElement>): react_jsx_runtime.JSX.Element;
    displayName: string;
};
declare const DrawerTitle: React$1.ForwardRefExoticComponent<Omit<DialogPrimitive.DialogTitleProps & React$1.RefAttributes<HTMLHeadingElement>, "ref"> & React$1.RefAttributes<HTMLHeadingElement>>;
declare const DrawerDescription: React$1.ForwardRefExoticComponent<Omit<DialogPrimitive.DialogDescriptionProps & React$1.RefAttributes<HTMLParagraphElement>, "ref"> & React$1.RefAttributes<HTMLParagraphElement>>;

declare const Tabs: React$1.ForwardRefExoticComponent<TabsPrimitive.TabsProps & React$1.RefAttributes<HTMLDivElement>>;
declare const TabsList: React$1.ForwardRefExoticComponent<Omit<TabsPrimitive.TabsListProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const TabsTrigger: React$1.ForwardRefExoticComponent<Omit<TabsPrimitive.TabsTriggerProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & React$1.RefAttributes<HTMLButtonElement>>;
declare const TabsContent: React$1.ForwardRefExoticComponent<Omit<TabsPrimitive.TabsContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;

interface TextareaProps extends React$1.TextareaHTMLAttributes<HTMLTextAreaElement> {
}
declare const Textarea: React$1.ForwardRefExoticComponent<TextareaProps & React$1.RefAttributes<HTMLTextAreaElement>>;

declare const TooltipProvider: React$1.FC<TooltipPrimitive.TooltipProviderProps>;
declare const Tooltip: React$1.FC<TooltipPrimitive.TooltipProps>;
declare const TooltipTrigger: React$1.ForwardRefExoticComponent<TooltipPrimitive.TooltipTriggerProps & React$1.RefAttributes<HTMLButtonElement>>;
declare const TooltipContent: React$1.ForwardRefExoticComponent<Omit<TooltipPrimitive.TooltipContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;

declare const ToastProvider: React$1.FC<ToastPrimitives.ToastProviderProps>;
declare const ToastViewport: React$1.ForwardRefExoticComponent<Omit<ToastPrimitives.ToastViewportProps & React$1.RefAttributes<HTMLOListElement>, "ref"> & React$1.RefAttributes<HTMLOListElement>>;
declare const Toast$1: React$1.ForwardRefExoticComponent<Omit<ToastPrimitives.ToastProps & React$1.RefAttributes<HTMLLIElement>, "ref"> & VariantProps<(props?: ({
    variant?: "default" | "destructive" | "error" | "success" | "information" | "warning" | null | undefined;
} & class_variance_authority_types.ClassProp) | undefined) => string> & React$1.RefAttributes<HTMLLIElement>>;
declare const ToastAction: React$1.ForwardRefExoticComponent<Omit<ToastPrimitives.ToastActionProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & React$1.RefAttributes<HTMLButtonElement>>;
declare const ToastClose: React$1.ForwardRefExoticComponent<Omit<ToastPrimitives.ToastCloseProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & React$1.RefAttributes<HTMLButtonElement>>;
declare const ToastTitle: React$1.ForwardRefExoticComponent<Omit<ToastPrimitives.ToastTitleProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const ToastDescription: React$1.ForwardRefExoticComponent<Omit<ToastPrimitives.ToastDescriptionProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
type ToastProps = React$1.ComponentPropsWithoutRef<typeof Toast$1>;
type ToastActionElement = React$1.ReactElement<typeof ToastAction>;

type ToasterToast = ToastProps & {
    id: string;
    title?: React$1.ReactNode;
    description?: React$1.ReactNode;
    action?: ToastActionElement;
};
type Toast = Omit<ToasterToast, 'id'>;
declare function toast({ ...props }: Toast): {
    id: string;
    dismiss: () => void;
    update: (props: ToasterToast) => void;
};
declare function useToast(): {
    toast: typeof toast;
    dismiss: (toastId?: string) => void;
    toasts: ToasterToast[];
};

declare const toggleVariants: (props?: ({
    variant?: "default" | "outline" | null | undefined;
    size?: "default" | "sm" | "lg" | null | undefined;
} & class_variance_authority_types.ClassProp) | undefined) => string;
declare const Toggle: React$1.ForwardRefExoticComponent<Omit<TogglePrimitive.ToggleProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & VariantProps<(props?: ({
    variant?: "default" | "outline" | null | undefined;
    size?: "default" | "sm" | "lg" | null | undefined;
} & class_variance_authority_types.ClassProp) | undefined) => string> & React$1.RefAttributes<HTMLButtonElement>>;

declare const ToggleGroup: React$1.ForwardRefExoticComponent<((Omit<ToggleGroupPrimitive.ToggleGroupSingleProps & React$1.RefAttributes<HTMLDivElement>, "ref"> | Omit<ToggleGroupPrimitive.ToggleGroupMultipleProps & React$1.RefAttributes<HTMLDivElement>, "ref">) & VariantProps<(props?: ({
    variant?: "default" | "outline" | null | undefined;
    size?: "default" | "sm" | "lg" | null | undefined;
} & class_variance_authority_types.ClassProp) | undefined) => string>) & React$1.RefAttributes<HTMLDivElement>>;
declare const ToggleGroupItem: React$1.ForwardRefExoticComponent<Omit<ToggleGroupPrimitive.ToggleGroupItemProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & VariantProps<(props?: ({
    variant?: "default" | "outline" | null | undefined;
    size?: "default" | "sm" | "lg" | null | undefined;
} & class_variance_authority_types.ClassProp) | undefined) => string> & React$1.RefAttributes<HTMLButtonElement>>;

declare const ContextMenu: React$1.FC<ContextMenuPrimitive.ContextMenuProps>;
declare const ContextMenuTrigger: React$1.ForwardRefExoticComponent<ContextMenuPrimitive.ContextMenuTriggerProps & React$1.RefAttributes<HTMLSpanElement>>;
declare const ContextMenuGroup: React$1.ForwardRefExoticComponent<ContextMenuPrimitive.ContextMenuGroupProps & React$1.RefAttributes<HTMLDivElement>>;
declare const ContextMenuPortal: React$1.FC<ContextMenuPrimitive.ContextMenuPortalProps>;
declare const ContextMenuSub: React$1.FC<ContextMenuPrimitive.ContextMenuSubProps>;
declare const ContextMenuRadioGroup: React$1.ForwardRefExoticComponent<ContextMenuPrimitive.ContextMenuRadioGroupProps & React$1.RefAttributes<HTMLDivElement>>;
declare const ContextMenuSubTrigger: React$1.ForwardRefExoticComponent<Omit<ContextMenuPrimitive.ContextMenuSubTriggerProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & {
    inset?: boolean;
} & React$1.RefAttributes<HTMLDivElement>>;
declare const ContextMenuSubContent: React$1.ForwardRefExoticComponent<Omit<ContextMenuPrimitive.ContextMenuSubContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const ContextMenuContent: React$1.ForwardRefExoticComponent<Omit<ContextMenuPrimitive.ContextMenuContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const ContextMenuItem: React$1.ForwardRefExoticComponent<Omit<ContextMenuPrimitive.ContextMenuItemProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & {
    inset?: boolean;
} & React$1.RefAttributes<HTMLDivElement>>;
declare const ContextMenuCheckboxItem: React$1.ForwardRefExoticComponent<Omit<ContextMenuPrimitive.ContextMenuCheckboxItemProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const ContextMenuRadioItem: React$1.ForwardRefExoticComponent<Omit<ContextMenuPrimitive.ContextMenuRadioItemProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const ContextMenuLabel: React$1.ForwardRefExoticComponent<Omit<ContextMenuPrimitive.ContextMenuLabelProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & {
    inset?: boolean;
} & React$1.RefAttributes<HTMLDivElement>>;
declare const ContextMenuSeparator: React$1.ForwardRefExoticComponent<Omit<ContextMenuPrimitive.ContextMenuSeparatorProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const ContextMenuShortcut: {
    ({ className, ...props }: React$1.HTMLAttributes<HTMLSpanElement>): react_jsx_runtime.JSX.Element;
    displayName: string;
};

declare const Form: <TFieldValues extends FieldValues, TContext = any, TTransformedValues extends FieldValues | undefined = undefined>(props: react_hook_form.FormProviderProps<TFieldValues, TContext, TTransformedValues>) => React$1.JSX.Element;
declare const FormField: <TFieldValues extends FieldValues = FieldValues, TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({ ...props }: ControllerProps<TFieldValues, TName>) => react_jsx_runtime.JSX.Element;
declare const useFormField: () => {
    invalid: boolean;
    isDirty: boolean;
    isTouched: boolean;
    isValidating: boolean;
    error?: react_hook_form.FieldError;
    id: string;
    name: string;
    formItemId: string;
    formDescriptionId: string;
    formMessageId: string;
};
declare const FormItem: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLDivElement> & React$1.RefAttributes<HTMLDivElement>>;
declare const FormLabel: React$1.ForwardRefExoticComponent<Omit<LabelPrimitive.LabelProps & React$1.RefAttributes<HTMLLabelElement>, "ref"> & React$1.RefAttributes<HTMLLabelElement>>;
declare const FormControl: React$1.ForwardRefExoticComponent<Omit<_radix_ui_react_slot.SlotProps & React$1.RefAttributes<HTMLElement>, "ref"> & React$1.RefAttributes<HTMLElement>>;
declare const FormDescription: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLParagraphElement> & React$1.RefAttributes<HTMLParagraphElement>>;
declare const FormMessage: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLParagraphElement> & React$1.RefAttributes<HTMLParagraphElement>>;

declare const HoverCard: React$1.FC<HoverCardPrimitive.HoverCardProps>;
declare const HoverCardTrigger: React$1.ForwardRefExoticComponent<HoverCardPrimitive.HoverCardTriggerProps & React$1.RefAttributes<HTMLAnchorElement>>;
declare const HoverCardContent: React$1.ForwardRefExoticComponent<Omit<HoverCardPrimitive.HoverCardContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;

declare const InputOTP: React$1.ForwardRefExoticComponent<(Omit<Omit<React$1.InputHTMLAttributes<HTMLInputElement>, "value" | "onChange" | "maxLength" | "textAlign" | "onComplete" | "pushPasswordManagerStrategy" | "pasteTransformer" | "containerClassName" | "noScriptCSSFallback"> & {
    value?: string;
    onChange?: (newValue: string) => unknown;
    maxLength: number;
    textAlign?: "left" | "center" | "right";
    onComplete?: (...args: any[]) => unknown;
    pushPasswordManagerStrategy?: "increase-width" | "none";
    pasteTransformer?: (pasted: string) => string;
    containerClassName?: string;
    noScriptCSSFallback?: string | null;
} & {
    render: (props: input_otp.RenderProps) => React$1.ReactNode;
    children?: never;
} & React$1.RefAttributes<HTMLInputElement>, "ref"> | Omit<Omit<React$1.InputHTMLAttributes<HTMLInputElement>, "value" | "onChange" | "maxLength" | "textAlign" | "onComplete" | "pushPasswordManagerStrategy" | "pasteTransformer" | "containerClassName" | "noScriptCSSFallback"> & {
    value?: string;
    onChange?: (newValue: string) => unknown;
    maxLength: number;
    textAlign?: "left" | "center" | "right";
    onComplete?: (...args: any[]) => unknown;
    pushPasswordManagerStrategy?: "increase-width" | "none";
    pasteTransformer?: (pasted: string) => string;
    containerClassName?: string;
    noScriptCSSFallback?: string | null;
} & {
    render?: never;
    children: React$1.ReactNode;
} & React$1.RefAttributes<HTMLInputElement>, "ref">) & React$1.RefAttributes<HTMLInputElement>>;
declare const InputOTPGroup: React$1.ForwardRefExoticComponent<Omit<React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const InputOTPSlot: React$1.ForwardRefExoticComponent<Omit<React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "ref"> & {
    index: number;
} & React$1.RefAttributes<HTMLDivElement>>;
declare const InputOTPSeparator: React$1.ForwardRefExoticComponent<Omit<React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLDivElement>, HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;

declare const MenubarMenu: typeof Menu;
declare const MenubarGroup: React$1.ForwardRefExoticComponent<MenubarPrimitive.MenubarGroupProps & React$1.RefAttributes<HTMLDivElement>>;
declare const MenubarPortal: React$1.FC<MenubarPrimitive.MenubarPortalProps>;
declare const MenubarSub: React$1.FC<MenubarPrimitive.MenubarSubProps>;
declare const MenubarRadioGroup: React$1.ForwardRefExoticComponent<MenubarPrimitive.MenubarRadioGroupProps & React$1.RefAttributes<HTMLDivElement>>;
declare const Menubar: React$1.ForwardRefExoticComponent<Omit<MenubarPrimitive.MenubarProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const MenubarTrigger: React$1.ForwardRefExoticComponent<Omit<MenubarPrimitive.MenubarTriggerProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & React$1.RefAttributes<HTMLButtonElement>>;
declare const MenubarSubTrigger: React$1.ForwardRefExoticComponent<Omit<MenubarPrimitive.MenubarSubTriggerProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & {
    inset?: boolean;
} & React$1.RefAttributes<HTMLDivElement>>;
declare const MenubarSubContent: React$1.ForwardRefExoticComponent<Omit<MenubarPrimitive.MenubarSubContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const MenubarContent: React$1.ForwardRefExoticComponent<Omit<MenubarPrimitive.MenubarContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const MenubarItem: React$1.ForwardRefExoticComponent<Omit<MenubarPrimitive.MenubarItemProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & {
    inset?: boolean;
} & React$1.RefAttributes<HTMLDivElement>>;
declare const MenubarCheckboxItem: React$1.ForwardRefExoticComponent<Omit<MenubarPrimitive.MenubarCheckboxItemProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const MenubarRadioItem: React$1.ForwardRefExoticComponent<Omit<MenubarPrimitive.MenubarRadioItemProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const MenubarLabel: React$1.ForwardRefExoticComponent<Omit<MenubarPrimitive.MenubarLabelProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & {
    inset?: boolean;
} & React$1.RefAttributes<HTMLDivElement>>;
declare const MenubarSeparator: React$1.ForwardRefExoticComponent<Omit<MenubarPrimitive.MenubarSeparatorProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const MenubarShortcut: {
    ({ className, ...props }: React$1.HTMLAttributes<HTMLSpanElement>): react_jsx_runtime.JSX.Element;
    displayname: string;
};

interface MultiSelectAsyncProps<T> {
    options: OptionType[];
    className?: string;
    isDisplayAllOptions?: boolean;
    selected?: OptionType[];
    setSelected?: T;
    isLoading?: boolean;
    defaultSearchValue?: string;
    searchRemix: {
        setSearchParams: SetURLSearchParams;
        searchKey: string;
    };
}
declare function MultiSelectAsync<T extends CommonFunction>({ options, className, selected, setSelected, isLoading, defaultSearchValue, searchRemix, }: MultiSelectAsyncProps<T>): react_jsx_runtime.JSX.Element;
interface MultiSelectProps$1<T> {
    options: OptionType[];
    className?: string;
    isDisplayAllOptions?: boolean;
    selected?: OptionType[];
    setSelected?: T;
    placeholder?: string;
}
declare function MultiSelect<T extends CommonFunction>({ options, className, isDisplayAllOptions, selected, setSelected, placeholder, }: MultiSelectProps$1<T>): react_jsx_runtime.JSX.Element;

declare const NavigationMenu: React$1.ForwardRefExoticComponent<Omit<NavigationMenuPrimitive.NavigationMenuProps & React$1.RefAttributes<HTMLElement>, "ref"> & React$1.RefAttributes<HTMLElement>>;
declare const NavigationMenuList: React$1.ForwardRefExoticComponent<Omit<NavigationMenuPrimitive.NavigationMenuListProps & React$1.RefAttributes<HTMLUListElement>, "ref"> & React$1.RefAttributes<HTMLUListElement>>;
declare const NavigationMenuItem: React$1.ForwardRefExoticComponent<Omit<NavigationMenuPrimitive.NavigationMenuItemProps & React$1.RefAttributes<HTMLLIElement>, "ref"> & React$1.RefAttributes<HTMLLIElement>>;
declare const navigationMenuTriggerStyle: (props?: class_variance_authority_types.ClassProp | undefined) => string;
declare const navigationMenuItemTriggerStyle: (props?: class_variance_authority_types.ClassProp | undefined) => string;
declare const dropdownMenuTriggerStyle: (props?: class_variance_authority_types.ClassProp | undefined) => string;
declare const NavigationMenuTrigger: React$1.ForwardRefExoticComponent<Omit<NavigationMenuPrimitive.NavigationMenuTriggerProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & React$1.RefAttributes<HTMLButtonElement>>;
declare const NavigationMenuContent: React$1.ForwardRefExoticComponent<Omit<NavigationMenuPrimitive.NavigationMenuContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const NavigationMenuLink: React$1.ForwardRefExoticComponent<NavigationMenuPrimitive.NavigationMenuLinkProps & React$1.RefAttributes<HTMLAnchorElement>>;
declare const NavigationMenuViewport: React$1.ForwardRefExoticComponent<Omit<NavigationMenuPrimitive.NavigationMenuViewportProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const NavigationMenuIndicator: React$1.ForwardRefExoticComponent<Omit<NavigationMenuPrimitive.NavigationMenuIndicatorProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;

declare const Pagination: {
    ({ className, ...props }: React$1.ComponentProps<"nav">): react_jsx_runtime.JSX.Element;
    displayName: string;
};
declare const PaginationContent: React$1.ForwardRefExoticComponent<Omit<React$1.DetailedHTMLProps<React$1.HTMLAttributes<HTMLUListElement>, HTMLUListElement>, "ref"> & React$1.RefAttributes<HTMLUListElement>>;
declare const PaginationItem: React$1.ForwardRefExoticComponent<Omit<React$1.DetailedHTMLProps<React$1.LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>, "ref"> & React$1.RefAttributes<HTMLLIElement>>;
type PaginationLinkProps = {
    isActive?: boolean;
} & Pick<ButtonProps, 'size'> & React$1.ComponentProps<'a'>;
declare const PaginationLink: {
    ({ className, isActive, size, ...props }: PaginationLinkProps): react_jsx_runtime.JSX.Element;
    displayName: string;
};
declare const PaginationPrevious: {
    ({ className, ...props }: React$1.ComponentProps<typeof PaginationLink>): react_jsx_runtime.JSX.Element;
    displayName: string;
};
declare const PaginationNext: {
    ({ className, ...props }: React$1.ComponentProps<typeof PaginationLink>): react_jsx_runtime.JSX.Element;
    displayName: string;
};
declare const PaginationEllipsis: {
    ({ className, ...props }: React$1.ComponentProps<"span">): react_jsx_runtime.JSX.Element;
    displayName: string;
};

declare const Popover: React$1.FC<PopoverPrimitive.PopoverProps>;
declare const PopoverTrigger: React$1.ForwardRefExoticComponent<PopoverPrimitive.PopoverTriggerProps & React$1.RefAttributes<HTMLButtonElement>>;
declare const PopoverAnchor: React$1.ForwardRefExoticComponent<PopoverPrimitive.PopoverAnchorProps & React$1.RefAttributes<HTMLDivElement>>;
declare const PopoverContent: React$1.ForwardRefExoticComponent<Omit<PopoverPrimitive.PopoverContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;

declare const ResizablePanelGroup: ({ className, ...props }: React.ComponentProps<typeof ResizablePrimitive.PanelGroup>) => react_jsx_runtime.JSX.Element;
declare const ResizablePanel: React$1.ForwardRefExoticComponent<Omit<React$1.HTMLAttributes<HTMLButtonElement | HTMLElement | HTMLDivElement | HTMLParagraphElement | HTMLHeadingElement | HTMLObjectElement | HTMLLinkElement | HTMLFormElement | HTMLSlotElement | HTMLStyleElement | HTMLTitleElement | HTMLMapElement | HTMLDialogElement | HTMLImageElement | HTMLOptionElement | HTMLTableElement | HTMLAnchorElement | HTMLInputElement | HTMLLabelElement | HTMLLIElement | HTMLOListElement | HTMLSpanElement | HTMLUListElement | HTMLAreaElement | HTMLAudioElement | HTMLBaseElement | HTMLQuoteElement | HTMLBodyElement | HTMLBRElement | HTMLCanvasElement | HTMLTableColElement | HTMLDataElement | HTMLDataListElement | HTMLModElement | HTMLDetailsElement | HTMLDListElement | HTMLEmbedElement | HTMLFieldSetElement | HTMLHeadElement | HTMLHRElement | HTMLHtmlElement | HTMLIFrameElement | HTMLLegendElement | HTMLMetaElement | HTMLMeterElement | HTMLOptGroupElement | HTMLOutputElement | HTMLPreElement | HTMLProgressElement | HTMLScriptElement | HTMLSelectElement | HTMLSourceElement | HTMLTemplateElement | HTMLTableSectionElement | HTMLTableCellElement | HTMLTextAreaElement | HTMLTimeElement | HTMLTableRowElement | HTMLTrackElement | HTMLVideoElement | HTMLTableCaptionElement | HTMLMenuElement | HTMLPictureElement>, "id" | "onResize"> & {
    className?: string | undefined;
    collapsedSize?: number | undefined;
    collapsible?: boolean | undefined;
    defaultSize?: number | undefined;
    id?: string | undefined;
    maxSize?: number | undefined;
    minSize?: number | undefined;
    onCollapse?: ResizablePrimitive.PanelOnCollapse | undefined;
    onExpand?: ResizablePrimitive.PanelOnExpand | undefined;
    onResize?: ResizablePrimitive.PanelOnResize | undefined;
    order?: number | undefined;
    style?: object | undefined;
    tagName?: keyof HTMLElementTagNameMap | undefined;
} & {
    children?: React$1.ReactNode;
} & React$1.RefAttributes<ResizablePrimitive.ImperativePanelHandle>>;
declare const ResizableHandle: ({ withHandle, className, ...props }: React.ComponentProps<typeof ResizablePrimitive.PanelResizeHandle> & {
    withHandle?: boolean;
}) => react_jsx_runtime.JSX.Element;

declare const ScrollArea: React$1.ForwardRefExoticComponent<Omit<ScrollAreaPrimitive.ScrollAreaProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const ScrollBar: React$1.ForwardRefExoticComponent<Omit<ScrollAreaPrimitive.ScrollAreaScrollbarProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;

declare const Select: React$1.FC<SelectPrimitive.SelectProps>;
declare const SelectGroup: React$1.ForwardRefExoticComponent<SelectPrimitive.SelectGroupProps & React$1.RefAttributes<HTMLDivElement>>;
declare const SelectValue: React$1.ForwardRefExoticComponent<SelectPrimitive.SelectValueProps & React$1.RefAttributes<HTMLSpanElement>>;
declare const SelectTrigger: React$1.ForwardRefExoticComponent<Omit<SelectPrimitive.SelectTriggerProps & React$1.RefAttributes<HTMLButtonElement>, "ref"> & React$1.RefAttributes<HTMLButtonElement>>;
declare const SelectScrollUpButton: React$1.ForwardRefExoticComponent<Omit<SelectPrimitive.SelectScrollUpButtonProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const SelectScrollDownButton: React$1.ForwardRefExoticComponent<Omit<SelectPrimitive.SelectScrollDownButtonProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const SelectContent: React$1.ForwardRefExoticComponent<Omit<SelectPrimitive.SelectContentProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const SelectLabel: React$1.ForwardRefExoticComponent<Omit<SelectPrimitive.SelectLabelProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const SelectItem: React$1.ForwardRefExoticComponent<Omit<SelectPrimitive.SelectItemProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const SelectSeparator: React$1.ForwardRefExoticComponent<Omit<SelectPrimitive.SelectSeparatorProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;

declare const Separator: React$1.ForwardRefExoticComponent<Omit<SeparatorPrimitive.SeparatorProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;

declare const Sheet: React$1.FC<DialogPrimitive.DialogProps>;
declare const SheetTrigger: React$1.ForwardRefExoticComponent<DialogPrimitive.DialogTriggerProps & React$1.RefAttributes<HTMLButtonElement>>;
declare const SheetClose: React$1.ForwardRefExoticComponent<DialogPrimitive.DialogCloseProps & React$1.RefAttributes<HTMLButtonElement>>;
declare const SheetPortal: React$1.FC<DialogPrimitive.DialogPortalProps>;
declare const SheetOverlay: React$1.ForwardRefExoticComponent<Omit<DialogPrimitive.DialogOverlayProps & React$1.RefAttributes<HTMLDivElement>, "ref"> & React$1.RefAttributes<HTMLDivElement>>;
declare const sheetVariants: (props?: ({
    side?: "top" | "right" | "bottom" | "left" | null | undefined;
} & class_variance_authority_types.ClassProp) | undefined) => string;
interface SheetContentProps extends React$1.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>, VariantProps<typeof sheetVariants> {
}
declare const SheetContent: React$1.ForwardRefExoticComponent<SheetContentProps & React$1.RefAttributes<HTMLDivElement>>;
declare const SheetHeader: {
    ({ className, ...props }: React$1.HTMLAttributes<HTMLDivElement>): react_jsx_runtime.JSX.Element;
    displayName: string;
};
declare const SheetFooter: {
    ({ className, ...props }: React$1.HTMLAttributes<HTMLDivElement>): react_jsx_runtime.JSX.Element;
    displayName: string;
};
declare const SheetTitle: React$1.ForwardRefExoticComponent<Omit<DialogPrimitive.DialogTitleProps & React$1.RefAttributes<HTMLHeadingElement>, "ref"> & React$1.RefAttributes<HTMLHeadingElement>>;
declare const SheetDescription: React$1.ForwardRefExoticComponent<Omit<DialogPrimitive.DialogDescriptionProps & React$1.RefAttributes<HTMLParagraphElement>, "ref"> & React$1.RefAttributes<HTMLParagraphElement>>;

declare function Toaster(): react_jsx_runtime.JSX.Element;

declare const Table: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLTableElement> & React$1.RefAttributes<HTMLTableElement>>;
/**
 * A table component that serves as the root element for tabular data without wrapping in a div.
 */
declare const TableRoot: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLTableElement> & React$1.RefAttributes<HTMLTableElement>>;
declare const TableHeader: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLTableSectionElement> & React$1.RefAttributes<HTMLTableSectionElement>>;
declare const TableBody: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLTableSectionElement> & React$1.RefAttributes<HTMLTableSectionElement>>;
declare const TableFooter: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLTableSectionElement> & React$1.RefAttributes<HTMLTableSectionElement>>;
declare const TableRow: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLTableRowElement> & React$1.RefAttributes<HTMLTableRowElement>>;
declare const TableHead: React$1.ForwardRefExoticComponent<React$1.ThHTMLAttributes<HTMLTableCellElement> & React$1.RefAttributes<HTMLTableCellElement>>;
declare const TableCell: React$1.ForwardRefExoticComponent<React$1.TdHTMLAttributes<HTMLTableCellElement> & React$1.RefAttributes<HTMLTableCellElement>>;
declare const TableCaption: React$1.ForwardRefExoticComponent<React$1.HTMLAttributes<HTMLTableCaptionElement> & React$1.RefAttributes<HTMLTableCaptionElement>>;

interface ImageUploadProps$1 {
    onFileChange: (args: {
        file: File;
    }) => void;
    description?: string;
    avatarUrl?: string;
    maxContentLength: number;
}
declare const AvatarUpload: ({ onFileChange, description, avatarUrl, maxContentLength, }: ImageUploadProps$1) => react_jsx_runtime.JSX.Element;

type BreadcrumbsItemProps = HTMLAttributes<HTMLElement> & NavLinkProps & {
    label: string;
    disabled?: boolean;
};
declare const BreadcrumbsLink: ({ children, label, disabled, ...props }: BreadcrumbsItemProps) => react_jsx_runtime.JSX.Element;
declare const Breadcrumbs: ({ className, ...props }: HTMLAttributes<HTMLElement>) => react_jsx_runtime.JSX.Element;

declare const AccountantLogo: ({ className, ...props }: HTMLAttributes<SVGSVGElement>) => react_jsx_runtime.JSX.Element;
declare const AccountantLoginLogo: ({ className, ...props }: HTMLAttributes<SVGSVGElement>) => react_jsx_runtime.JSX.Element;
declare const MarketingLogo: ({ className, ...props }: HTMLAttributes<SVGSVGElement>) => react_jsx_runtime.JSX.Element;
declare const MarketingLoginLogo: ({ className, ...props }: HTMLAttributes<SVGSVGElement>) => react_jsx_runtime.JSX.Element;
declare const CustomerSupportLogo: ({ className, ...props }: HTMLAttributes<SVGSVGElement>) => react_jsx_runtime.JSX.Element;
declare const CustomerSupportLoginLogo: ({ className, ...props }: HTMLAttributes<SVGSVGElement>) => react_jsx_runtime.JSX.Element;
declare const TaskerOperationLogo: ({ className, ...props }: HTMLAttributes<SVGSVGElement>) => react_jsx_runtime.JSX.Element;
declare const TaskerOperationLoginLogo: ({ className, ...props }: HTMLAttributes<SVGSVGElement>) => react_jsx_runtime.JSX.Element;
declare const AdminLogo: ({ className, ...props }: HTMLAttributes<SVGSVGElement>) => react_jsx_runtime.JSX.Element;
declare const AdminLoginLogo: ({ className, ...props }: HTMLAttributes<SVGSVGElement>) => react_jsx_runtime.JSX.Element;

/**
 * DatePickerProps interface for the DateTimePicker component.
 * @typedef {Object} DatePickerProps
 * @property {UseFormReturn<any>} form - The form object from react-hook-form.
 * @property {string} name - The name of the form field.
 * @property {boolean} [showTime] - Whether to show time picker or not.
 * @property {RegisterOptions} [rules] - Validation rules for the form field.
 * @property {string} [label] - Label for the date picker.
 * @property {string} [formatString] - Format string for date display.
 * @property {'default' | 'dayMonth'} [mode] - Mode of the date picker.
 * @property {boolean} [disabled] - Whether the date picker is disabled or not.
 */
interface DatePickerProps$1 {
    form: UseFormReturn<any>;
    name: string;
    showTime?: boolean;
    rules?: RegisterOptions;
    label?: string;
    formatString?: string;
    mode?: 'default' | 'dayMonth';
    disabled?: boolean;
}
/**
 * DateTimePicker component for selecting date and time range.
 *
 * @example
 * <DateTimePicker
 *   form={form}
 *   name="dateRange"
 *   showTime={true}
 *   label="Select Date Range"
 *   mode="default"
 *   disabled={false}
 * />
 *
 * @param {DatePickerProps} props - The props for the DateTimePicker component.
 * @returns {JSX.Element} The DateTimePicker component.
 */
declare const DateTimePicker: React__default.ForwardRefExoticComponent<DatePickerProps$1 & React__default.RefAttributes<HTMLInputElement>>;

interface DropdownMenuBaseProps extends DropdownMenuPrimitive.DropdownMenuContentProps {
    links: {
        link: string;
        label: string;
    }[];
    children?: React__default.ReactNode;
    btnClassName?: string;
}
declare const DropdownMenuBase: ({ links, children, btnClassName, ...props }: DropdownMenuBaseProps) => react_jsx_runtime.JSX.Element;

interface ImageUploadProps {
    onFileChange: (file: File | null, previewUrl: string | null, fileName: string) => void;
    title?: string;
    maxSize?: number;
    ratio?: number;
    imageUrl?: string;
    disableActionUpload?: Boolean;
}
/**
 * This component just handle the image upload and preview
 */
declare const ImageUpload: React__default.FC<ImageUploadProps>;

declare const Grid: ({ className, ...props }: HTMLAttributes<HTMLElement>) => react_jsx_runtime.JSX.Element;

declare const GridItem: ({ className, ...props }: HTMLAttributes<HTMLElement>) => react_jsx_runtime.JSX.Element;

declare const LoadingGlobal: ({ backgroundColor }: {
    backgroundColor?: string | undefined;
}) => react_jsx_runtime.JSX.Element | null;

interface ISVGProps extends React.SVGProps<SVGSVGElement> {
    size?: number;
    className?: string;
}
declare const LoadingSpinner: ({ size, className, ...props }: ISVGProps) => react_jsx_runtime.JSX.Element;

type IErrorMessage<TFieldErrors extends FieldErrors, T> = Props$2<TFieldErrors, any>;
declare function ErrorMessageBase<TFieldErrors extends FieldErrors, T = any>(props: IErrorMessage<TFieldErrors, T>): react_jsx_runtime.JSX.Element;

declare const NavigationLink: ({ className, ...props }: NavLinkProps) => react_jsx_runtime.JSX.Element;

type TextInputProps = {
    form: UseFormReturn<any>;
    name: string;
    label: string;
    placeholder: string;
    currency: string;
    rules?: RegisterOptions;
    disabled?: boolean;
    backgroundColor?: string;
};
declare function MoneyInput(props: TextInputProps): react_jsx_runtime.JSX.Element;

type InputProps = React$1.InputHTMLAttributes<HTMLInputElement>;
declare const Input: React$1.ForwardRefExoticComponent<InputProps & React$1.RefAttributes<HTMLInputElement>>;

interface SelectBaseProps extends Omit<SelectPrimitive.SelectProps, 'onValueChange'> {
    onValueChange: (value: string) => void;
    defaultValue?: string;
    options: Array<OptionType>;
    placeholder?: string;
    isAddItem?: boolean;
    addItem?: (item: OptionType) => void;
    newItemPlaceholder?: string;
    backgroundColor?: string;
    allowClear?: boolean;
    selectTriggerRef?: RefCallBack;
}
declare const SelectBase: ({ onValueChange, defaultValue, options, placeholder, isAddItem, addItem, newItemPlaceholder, allowClear, backgroundColor: selectTriggerBtnClassName, selectTriggerRef, ...props }: SelectBaseProps) => react_jsx_runtime.JSX.Element;

interface SelectSearchAsyncProps<T> {
    options: OptionType[];
    className?: string;
    selected?: OptionType[];
    setSelected?: T;
    isLoading?: boolean;
    defaultSearchValue?: string;
    placeholder?: string;
    searchRemix: {
        setSearchParams: SetURLSearchParams;
        searchKey: string;
    };
}
declare function SelectSearchAsync<T extends CommonFunction>({ options, className, selected, setSelected, isLoading, defaultSearchValue, searchRemix, placeholder, }: SelectSearchAsyncProps<T>): react_jsx_runtime.JSX.Element;

interface StatusBadgeProps$1 {
    status: string;
    isTesting?: boolean;
    statusClasses?: {
        [key: string]: string;
    };
    translationKey?: string;
}
declare const StatusBadge: React__default.FC<StatusBadgeProps$1>;

declare enum DATE_RANGE_PICKER_OPTIONS {
    NONE = "NONE",
    TODAY = "TODAY",
    YESTERDAY = "YESTERDAY",
    LAST_7_DAYS = "LAST_7_DAYS",
    LAST_14_DAYS = "LAST_14_DAYS",
    LAST_30_DAYS = "LAST_30_DAYS",
    THIS_MONTH = "THIS_MONTH",
    LAST_MONTH = "LAST_MONTH",
    THIS_YEAR = "THIS_YEAR",
    LAST_YEAR = "LAST_YEAR"
}
type DateRangePickerOptions = keyof typeof DATE_RANGE_PICKER_OPTIONS;
type DateRangeProps$1 = {
    from: Date;
    to: Date;
} | undefined;
interface DateRangePickerProps$1 {
    onUpdate?: (value: DateRangeProps$1) => void;
    initialRangeDate?: DateRangeProps$1;
    align?: 'start' | 'center' | 'end';
    className?: string;
    defaultRangeDateOptions?: DateRangePickerOptions;
    formatDateTriggerButtonText?: string;
}
declare const DateRangePicker: FC<DateRangePickerProps$1>;

interface DataTableProps$1<TData, TValue> {
    columns: ColumnDef<TData, TValue>[];
    data: TData[];
    onDoubleClickRow?: (record: TData) => void | Promise<void>;
    onClickRow?: (record: TData) => void | Promise<void>;
    total: number;
    pagination?: PaginationState;
    defaultSearchParams?: URLSearchParamsInit;
    filterDate?: {
        name: string;
        mode?: 'month' | 'range-month' | 'range-date' | 'month-year';
        variant?: {
            chevrons?: 'outline' | 'default';
        };
        selectMode?: 'month' | 'year';
        minDate?: Date;
        maxDate?: Date;
        defaultValue?: {
            from: Date;
            to: Date;
        };
        defaultRangeDateOptions?: DateRangePickerOptions;
    };
    columnVisibilityFromOutSide?: VisibilityState;
    search?: {
        name: string;
        defaultValue: string;
        placeholder?: string;
        setValue?: (newValue: string) => void;
        value?: string;
        searchByEnter?: boolean;
        className?: InputHTMLAttributes<HTMLInputElement>['className'];
    };
    filters?: Array<{
        placeholder: string;
        options: OptionType[];
        name: string;
        value: string;
        className?: InputHTMLAttributes<HTMLInputElement>['className'];
    }>;
    columnPinningFromOutSide?: ColumnPinningState;
    extraContent?: ReactElement;
    renderSubComponent?: (props: {
        row: Row<TData>;
    }) => ReactElement;
    getRowCanExpand?: (row: Row<TData>) => boolean;
    localeAddress?: string;
    componentAfterFilter?: React.ReactNode;
    isShowClearButton?: boolean;
    toolbarAction?: ReactElement;
    disableViewOptions?: boolean;
    isShowRecords?: boolean;
}
declare module '@tanstack/react-table' {
    interface ColumnMeta<TData extends RowData, TValue> {
        filterVariant?: 'text' | 'range' | 'select' | 'date';
        filterOptions?: OptionType[];
    }
}
declare const BTaskeeTable: <TData, TValue>({ data, total, pagination, columns, search, filters, filterDate, columnVisibilityFromOutSide, componentAfterFilter, columnPinningFromOutSide, extraContent, defaultSearchParams, onClickRow, onDoubleClickRow, renderSubComponent, getRowCanExpand, localeAddress, isShowClearButton, toolbarAction, disableViewOptions, isShowRecords, }: DataTableProps$1<TData, TValue>) => react_jsx_runtime.JSX.Element;

interface AlertBaseProps {
    icon: React__default.ReactNode;
    title: string;
    description: string;
    variant?: 'default' | 'destructive';
}
declare function AlertBase({ icon, title, description, variant, }: AlertBaseProps): react_jsx_runtime.JSX.Element;

type THideElements = 'view-options' | 'search' | 'pagination' | 'toolbar-filter';
type TSearchInputTriggerMode = 'onEnter' | 'onChange' | 'onChangeDebounce' | 'onChangeThrottle' | 'custom' | undefined;
interface DataTablePropsV2<TData, TValue> {
    columns: ColumnDef<TData, TValue>[];
    data: TData[];
    total: number;
    pagination: PaginationState;
    overrideColumnVisibility?: VisibilityState;
    pinColumns?: ColumnPinningState;
    isResizeColumn?: boolean;
    getRowCanExpand?: (row: Row<TData>) => boolean;
    renderExpandComponent?: (props: {
        row: Row<TData>;
    }) => ReactElement;
    renderExtraComponent?: Array<{
        /** Implement this is Array cause we might have to put multi extra things in the middle of component */
        position: 'top' | 'bottom';
        component: ReactElement;
    }>;
    renderExtraToolbarComponent?: Array<{
        position: 'left' | 'right';
        component: ReactElement;
    }>;
    alertInformation?: AlertBaseProps;
    emptyDataComponent?: ReactElement;
    onDoubleClickRow?: (record: TData) => void | Promise<void>;
    onClickRow?: (record: TData) => void | Promise<void>;
    translationKey?: FlatNamespace;
    searchInput?: {
        name: InputHTMLAttributes<HTMLInputElement>['name'];
        defaultValue?: InputHTMLAttributes<HTMLInputElement>['defaultValue'];
        placeholder?: InputHTMLAttributes<HTMLInputElement>['placeholder'];
        setSearchInputValue?: (newValue: InputHTMLAttributes<HTMLInputElement>['value']) => void;
        triggerMode?: TSearchInputTriggerMode;
        delayTime?: number;
        customTrigger?: (value: InputHTMLAttributes<HTMLInputElement>['value']) => void;
        inputClassName?: InputHTMLAttributes<HTMLInputElement>['className'];
        onChange?: InputHTMLAttributes<HTMLInputElement>['onChange'];
    };
    initialFilterDate?: {
        name: string;
        mode?: 'range-date' | 'month-year' | 'month';
        defaultValue?: {
            from: Date;
            to: Date;
        };
        minDate?: Date;
        maxDate?: Date;
    };
    initialFilters?: Array<{
        placeholder: string;
        options: OptionType[];
        name: string;
        value: string;
        className?: InputHTMLAttributes<HTMLInputElement>['className'];
    }>;
    initialSearchParams?: URLSearchParamsInit;
    hideElements?: THideElements[];
}
declare module '@tanstack/react-table' {
    interface ColumnMeta<TData extends RowData, TValue> {
        filterVariant?: 'text' | 'range' | 'select' | 'date';
        filterOptions?: OptionType[];
        headerCellClassName?: HTMLAttributes<HTMLTableCellElement>['className'];
        dataCellClassName?: HTMLAttributes<HTMLTableCellElement>['className'];
        dateFilterOptions?: {
            defaultDateRange?: DateRangeProps$1;
            minDate?: Date;
            maxDate?: Date;
            formatTriggerText?: string;
        };
    }
}
/**
 * Columns definition for the table.
 * @type {ColumnDef<TData, TValue>[]}
 * @property {ColumnDef<TData, TValue>} action - The action column.
 * This column will be aligned to the center if its key or id is 'action'.
 */
/**
 * BTaskeeTableV2 is a generic table component that supports various features such as sorting, filtering, pagination, and column pinning.
 *
 * @template TData - The type of data for each row in the table.
 * @template TValue - The type of value for each column in the table.
 *
 * @param {DataTablePropsV2<TData, TValue>} props - The properties for configuring the table.
 * @param {ColumnDef<TData, TValue>[]} props.columns - The column definitions for the table.
 * @param {TData[]} props.data - The data to be displayed in the table.
 * @param {number} props.total - The total number of data entries.
 * @param {PaginationState} props.pagination - The pagination state for the table.
 * @param {VisibilityState} [props.overrideColumnVisibility] - Optional override for column visibility.
 * @param {ColumnPinningState} [props.pinColumns] - Optional column pinning configuration. Default column def is 150px, using pinning with modified size(column implementation) for better UI
 * @param {boolean} [props.isResizeColumn=true] - Whether columns can be resized.
 * @param {(row: Row<TData>) => boolean} [props.getRowCanExpand] - Function to determine if a row can be expanded.
 * @param {(props: { row: Row<TData> }) => ReactElement} [props.renderExpandComponent] - Component to render when a row is expanded.
 * @param {Array<{ position: 'top' | 'bottom'; component: ReactElement }>} [props.renderExtraComponent] - Extra components to render at the top or bottom of the table.
 * @param {Array<{ position: 'left' | 'right'; component: ReactElement }>} [props.renderExtraToolbarComponent] - Extra components to render in the toolbar.
 * @param {(record: TData) => void | Promise<void>} [props.onDoubleClickRow] - Callback for double-clicking a row.
 * @param {(record: TData) => void | Promise<void>} [props.onClickRow] - Callback for clicking a row.
 * @param {FlatNamespace} [props.translationKey='common'] - Translation key for i18n.
 * @param {object} [props.searchInput] - Configuration for the search input.
 * @param {object} [props.initialFilterDate] - Initial date filter configuration.
 * @param {Array<{ placeholder: string; options: OptionType[]; name: string; value: string; className?: string }>} [props.initialFilters] - Initial filters for the table.
 * @param {URLSearchParamsInit} [props.initialSearchParams] - Initial search parameters for the table.
 * @param {THideElements[]} [props.hideElements] - Elements to hide in the table UI.
 *
 * @returns {ReactElement} The rendered table component.
 */
declare const BTaskeeTableV2: <TData, TValue>({ data, total, pagination, columns, searchInput, initialFilterDate, initialFilters, overrideColumnVisibility, pinColumns, renderExtraComponent, alertInformation, onClickRow, onDoubleClickRow, renderExpandComponent, translationKey, initialSearchParams, isResizeColumn, getRowCanExpand, hideElements, renderExtraToolbarComponent, emptyDataComponent, }: DataTablePropsV2<TData, TValue>) => react_jsx_runtime.JSX.Element;

interface TimePickerDemoProps {
    date: Date | undefined;
    setDate: React$1.Dispatch<React$1.SetStateAction<Date | undefined>>;
}
declare function TimePicker({ date, setDate }: TimePickerDemoProps): react_jsx_runtime.JSX.Element;

declare function cn(...inputs: ClassValue[]): string;
type TimePickerType = 'minutes' | 'seconds' | 'hours';

interface TimePickerInputProps extends React__default.InputHTMLAttributes<HTMLInputElement> {
    picker: TimePickerType;
    date: Date | undefined;
    setDate: (date: Date | undefined) => void;
    onRightFocus?: () => void;
    onLeftFocus?: () => void;
}
declare const TimePickerInput: React__default.ForwardRefExoticComponent<TimePickerInputProps & React__default.RefAttributes<HTMLInputElement>>;

type Month$2 = {
    number: number;
    name: string;
    yearOffset: number;
};
type SingleMonthCalProps = {
    selectedMonth?: Date;
    onMonthSelect?: (date: Date) => void;
    callbacks?: {
        yearLabel?: (year: number) => string;
        monthLabel?: (month: Month$2) => string;
    };
    variant?: {
        calendar?: {
            main?: ButtonVariant$3;
            selected?: ButtonVariant$3;
        };
        chevrons?: ButtonVariant$3;
    };
    minDate?: Date;
    maxDate?: Date;
};
type ButtonVariant$3 = 'default' | 'outline' | 'ghost' | 'link' | 'destructive' | 'secondary' | null | undefined;
declare function SingleMonthPicker({ onMonthSelect, callbacks, variant, minDate, maxDate, defaultMonth, className, ...props }: React$1.HTMLAttributes<HTMLDivElement> & Omit<SingleMonthCalProps, 'selectedMonth'> & {
    defaultMonth?: Date;
}): react_jsx_runtime.JSX.Element;
declare namespace SingleMonthPicker {
    var displayName: string;
}

type ButtonVariant$2 = 'default' | 'outline' | 'ghost' | 'link' | 'destructive' | 'secondary' | null | undefined;
type SingleYearCalProps = {
    selectedYear?: Date;
    onYearSelect?: (date: Date) => void;
    variant?: {
        calendar?: {
            main?: ButtonVariant$2;
            selected?: ButtonVariant$2;
        };
        chevrons?: ButtonVariant$2;
    };
    minDate?: Date;
    maxDate?: Date;
};
declare function SingleYearPicker({ onYearSelect, variant, minDate, maxDate, defaultYear, className, ...props }: React$1.HTMLAttributes<HTMLDivElement> & Omit<SingleYearCalProps, 'selectedYear'> & {
    defaultYear?: Date;
}): react_jsx_runtime.JSX.Element;
declare namespace SingleYearPicker {
    var displayName: string;
}

declare const typographyVariants: (props?: ({
    variant?: "h3" | "p" | "h2" | "h1" | "h4" | null | undefined;
    affects?: "default" | "small" | "muted" | "lead" | "large" | "removePMargin" | null | undefined;
} & class_variance_authority_types.ClassProp) | undefined) => string;
interface TypographyProps extends HTMLAttributes<HTMLHeadingElement>, VariantProps<typeof typographyVariants> {
}
declare const Typography: React$1.ForwardRefExoticComponent<TypographyProps & React$1.RefAttributes<HTMLHeadingElement>>;

declare function ToasterBase(): react_jsx_runtime.JSX.Element;

interface DataTableColumnHeaderProps<TData, TValue> extends React.HTMLAttributes<HTMLDivElement> {
    column: Column<TData, TValue>;
    title: string;
}
declare function DataTableColumnHeader<TData, TValue>({ column, title, className, }: DataTableColumnHeaderProps<TData, TValue>): react_jsx_runtime.JSX.Element;

interface DataTableViewOptionsProps<TData> {
    table: Table$1<TData>;
    localeAddress?: DataTableProps$1<TData, any>['localeAddress'];
}
declare function DataTableViewOptions<TData>({ table, localeAddress, }: DataTableViewOptionsProps<TData>): react_jsx_runtime.JSX.Element;

interface DataTableToolbarProps<TData, TValue> {
    table: Table$1<TData>;
    search?: DataTableProps$1<TData, TValue>['search'];
    total: number;
    filterDate: DataTableProps$1<TData, TValue>['filterDate'];
    filters?: DataTableProps$1<TData, TValue>['filters'];
    setSorting: Dispatch<SetStateAction<SortingState>>;
    localeAddress?: DataTableProps$1<TData, TValue>['localeAddress'];
    isShowClearButton?: boolean;
    defaultSearchParams?: DataTableProps$1<TData, TValue>['defaultSearchParams'];
    toolbarAction?: ReactElement;
    disableViewOptions?: boolean;
    componentAfterFilter?: DataTableProps$1<TData, TValue>['componentAfterFilter'];
    isShowRecords?: boolean;
}
declare function DataTableToolbar<TData, TValue>({ table, search, total, filterDate, filters, setSorting, componentAfterFilter, localeAddress, defaultSearchParams, isShowClearButton, toolbarAction, disableViewOptions, isShowRecords, }: DataTableToolbarProps<TData, TValue>): react_jsx_runtime.JSX.Element;

interface DataTableRowActionsProps {
    items: Array<{
        item: string | React__default.ReactNode;
    } & DropdownMenuItemProps>;
}
declare function DataTableRowActions({ items }: DataTableRowActionsProps): react_jsx_runtime.JSX.Element;

interface DataTablePaginationProps<TData> {
    table: Table$1<TData>;
}
declare function DataTablePagination<TData>({ table, }: DataTablePaginationProps<TData>): react_jsx_runtime.JSX.Element;

interface DataTableFacetedFilterProps<TData, TValue> {
    title?: string;
    options: {
        label: string;
        value: string;
        icon?: ComponentType<{
            className?: string;
        }>;
    }[];
    keyFilter: string;
    defaultValues?: string;
}
declare function DataTableFacetedFilter<TData, TValue>({ title, options, keyFilter, defaultValues, }: DataTableFacetedFilterProps<TData, TValue>): react_jsx_runtime.JSX.Element;

declare function AlertDialogProvider({ children, }: {
    children: React__default.ReactNode;
}): react_jsx_runtime.JSX.Element;

type TLanguage = 'vi' | 'en' | 'ko' | 'th' | 'id' | 'ms';
type FlatData = {
    [key: string]: {
        [K in TLanguage]?: string;
    };
};
type NestedData = {
    [K in TLanguage]?: {
        [key: string]: string;
    };
};
type ParentNestedData = {
    [parent: string]: {
        [K in TLanguage]?: {
            [key: string]: string;
        };
    };
};
declare const ITEMS_LANGUAGE: Array<{
    label: string;
    value: TLanguage;
    icon: ReactNode;
}>;
interface MultiLanguageSectionProps$1 {
    data: FlatData | NestedData | ParentNestedData;
    fields: {
        [key: string]: string;
    };
    parentField?: string;
    useNestedStructure?: boolean;
}
declare const MultiLanguageSectionView: ({ data, fields, parentField, useNestedStructure, }: MultiLanguageSectionProps$1) => react_jsx_runtime.JSX.Element;

type MultiLanguageSectionProps = {
    form: UseFormReturn<any>;
    children: React__default.ReactElement[];
    childrenProps: {
        name: string;
        label: string;
        required?: string;
    }[];
    parentField?: string;
    useNestedStructure?: boolean;
};
declare function MultiLanguageSection({ children, form, childrenProps, parentField, useNestedStructure, }: MultiLanguageSectionProps): react_jsx_runtime.JSX.Element;

interface RadioGroupsProps extends Omit<RadioGroupProps, 'onValueChange'> {
    onValueChange: RadioGroupProps['onValueChange'];
    defaultValue: string;
    options: OptionType[];
    className?: string;
    name?: string;
}
declare const RadioGroupsBase: ({ onValueChange, defaultValue, options, className, name, ...props }: RadioGroupsProps) => react_jsx_runtime.JSX.Element;

interface TooltipBaseProps {
    children: React__default.ReactNode;
    content: string;
}
declare const TooltipUIBase: React__default.FC<TooltipBaseProps>;

interface DatePickerProps {
    /** The form object from react-hook-form */
    form: UseFormReturn<any>;
    /** The name of the form field */
    name: string;
    /** The label for the date picker */
    label?: string;
    /** Additional rules for form validation */
    rules?: RegisterOptions;
    /** Default time to set when a date is selected (format: "HH:mm:ss") */
    defaultTime?: string;
    /** If true, sets the time to the start of the day */
    isStartDate?: boolean;
    /** If true, sets the time to the end of the day */
    isEndDate?: boolean;
}
/**
 * A single date picker component with customizable time settings.
 *
 * This component provides a form field with a date picker, integrated with react-hook-form.
 * It allows users to select a date from a calendar interface and optionally set a specific time.
 *
 * @param props - The component props
 * @param props.form - The form object from react-hook-form (UseFormReturn<any>)
 * @param props.name - The name of the form field (string)
 * @param props.label - The label for the date picker (string, optional)
 * @param props.rules - Additional rules for form validation (RegisterOptions, optional)
 * @param props.defaultTime - Default time to set when a date is selected (string, format: "HH:mm:ss", optional)
 * @param props.isStartDate - If true, sets the time to the start of the day (boolean, optional)
 * @param props.isEndDate - If true, sets the time to the end of the day (boolean, optional)
 *
 * @returns A form field component with a date picker
 *
 * @example
 * <SingleDatePicker
 *   form={form}
 *   name="eventDate"
 *   label="Event Date"
 *   defaultTime="09:00:00"
 * />
 */
declare const SingleDatePicker: React__default.ForwardRefExoticComponent<DatePickerProps & React__default.RefAttributes<HTMLInputElement>>;

type CustomColumnDef<TData, TValue> = ColumnDef<TData, TValue> & {
    enableColumnFilter?: boolean;
    dateFilterOptions?: {
        defaultDateRange?: DateRangeProps$1;
        minDate?: Date;
        maxDate?: Date;
        formatTriggerText?: string;
    };
};
interface DataTableProps<TData, TValue> {
    columns: CustomColumnDef<TData, TValue>[];
    data: TData[];
    manualPagination?: boolean;
    isManualCountSelectedRow?: boolean;
    onClickRow?: (record: TData) => void | Promise<void>;
    extraContent?: React$1.ReactElement;
    pinColumns?: ColumnPinningState;
    initialSearchParams?: MustBeAny;
    renderExtraToolbarComponent?: Array<{
        position: 'left' | 'right';
        component: React$1.ReactElement;
        initialSearchParams?: MustBeAny;
    }>;
    translationKey?: FlatNamespace;
    searchInput?: {
        name: React$1.InputHTMLAttributes<HTMLInputElement>['name'];
        defaultValue?: React$1.InputHTMLAttributes<HTMLInputElement>['defaultValue'];
        placeholder?: React$1.InputHTMLAttributes<HTMLInputElement>['placeholder'];
        setSearchInputValue?: (newValue: React$1.InputHTMLAttributes<HTMLInputElement>['value']) => void;
        triggerMode?: MustBeAny;
        delayTime?: number;
        customTrigger?: (value: React$1.InputHTMLAttributes<HTMLInputElement>['value']) => void;
        inputClassName?: React$1.InputHTMLAttributes<HTMLInputElement>['className'];
    };
    initialFilters?: Array<{
        placeholder: string;
        options: OptionType[];
        name: string;
        value: string;
        className?: React$1.InputHTMLAttributes<HTMLInputElement>['className'];
    }>;
    initialFilterDate?: {
        name: string;
        mode?: 'range-date' | 'month-year' | 'month';
        defaultValue?: {
            from: Date;
            to: Date;
        };
        minDate?: Date;
        maxDate?: Date;
    };
}
declare function DataTableBasic<TData, TValue>({ columns, data, manualPagination, isManualCountSelectedRow, extraContent, searchInput, translationKey, initialSearchParams, renderExtraToolbarComponent, pinColumns, initialFilters, initialFilterDate, onClickRow, }: DataTableProps<TData, TValue>): react_jsx_runtime.JSX.Element;

interface UseAutosizeTextAreaProps {
    textAreaRef: HTMLTextAreaElement | null;
    minHeight?: number;
    maxHeight?: number;
    triggerAutoSize: string;
}
declare const useAutosizeTextArea: ({ textAreaRef, triggerAutoSize, maxHeight, minHeight, }: UseAutosizeTextAreaProps) => void;
type AutosizeTextAreaRef = {
    textArea: HTMLTextAreaElement;
    maxHeight: number;
    minHeight: number;
};
declare const AutosizeTextarea: React$1.ForwardRefExoticComponent<{
    maxHeight?: number;
    minHeight?: number;
} & React$1.TextareaHTMLAttributes<HTMLTextAreaElement> & React$1.RefAttributes<AutosizeTextAreaRef>>;

interface FormButtonsProps {
    cancelText: string;
    submitText: string;
    onCancel: () => void;
}
declare const FormSubmissionButton: ({ cancelText, submitText, onCancel, }: FormButtonsProps) => react_jsx_runtime.JSX.Element;

type CardStatisticProps = {
    title: string;
    icon?: React.ReactNode;
    content: string;
    subContent?: string;
};
declare const CardStatistic: ({ title, icon, content, subContent, }: CardStatisticProps) => react_jsx_runtime.JSX.Element;

interface SingleDateTimePickerProps {
    form: UseFormReturn<MustBeAny>;
    name: string;
    label?: string;
    format?: string;
    fieldWrapperClassName?: string;
    triggerProps?: ButtonProps;
    triggerClassName?: string;
    disableTimePickers?: Array<'hours' | 'minutes' | 'seconds'>;
}
/**
 * SingleDateTimePicker component
 *
 * This component renders a form field for selecting both a date and time.
 * It uses a popover to display a calendar for date selection and a time picker for time selection.
 *
 * @param {Object} props - The component props
 * @param {UseFormReturn<MustBeAny>} props.form - The form object from react-hook-form
 * @param {string} props.name - The name of the form field
 * @param {string} [props.label] - Optional label for the form field
 * @param {string} [props.format='PPP HH:mm:ss'] - Optional date-time format string
 *   The format string should use Unicode tokens as defined in date-fns.
 *   See https://github.com/date-fns/date-fns/blob/main/docs/unicodeTokens.md for the list of Unicode tokens.
 *   Default is 'PPP HH:mm:ss' which represents a long localized date format with time (e.g., "April 29th, 2023 14:30:00")
 * @param {string} [props.fieldWrapperClassName] - Optional class name for the form field wrapper
 * @param {ButtonProps} [props.triggerProps] - Optional props for the trigger button
 * @param {string} [props.triggerClassName] - Optional class name for the trigger button
 * @param {Array<'hours' | 'minutes' | 'seconds'>} [props.disableTimePickers] - Optional array of time picker types to disable
 *
 * @returns {JSX.Element} A form field component for date and time selection
 */
declare function SingleDateTimePicker({ form, name, label, format, fieldWrapperClassName, triggerProps, triggerClassName, disableTimePickers, }: SingleDateTimePickerProps): react_jsx_runtime.JSX.Element;

/**
 * Variants for the multi-select component to handle different styles.
 * Uses class-variance-authority (cva) to define different styles based on "variant" prop.
 */
declare const multiSelectVariants: (props?: ({
    variant?: "default" | "destructive" | "secondary" | "blue" | "inverted" | null | undefined;
} & class_variance_authority_types.ClassProp) | undefined) => string;
/**
 * Props for MultiSelectAdvance component
 */
interface MultiSelectProps extends React$1.ButtonHTMLAttributes<HTMLButtonElement>, VariantProps<typeof multiSelectVariants> {
    /**
     * An array of option objects to be displayed in the multi-select component.
     * Each option object has a label, value, and an optional icon.
     */
    options: {
        /** The text to display for the option. */
        label: string;
        /** The unique value associated with the option. */
        value: string;
        /** Optional icon component to display alongside the option. */
        icon?: React$1.ComponentType<{
            className?: string;
        }>;
    }[];
    /**
     * Callback function triggered when the selected values change.
     * Receives an array of the new selected values.
     */
    onValueChange: (value: string[]) => void;
    /** The default selected values when the component mounts. */
    defaultValue: string[];
    /**
     * Placeholder text to be displayed when no values are selected.
     * Optional, defaults to "Select options".
     */
    placeholder?: string;
    /**
     * Animation duration in seconds for the visual effects (e.g., bouncing badges).
     * Optional, defaults to 0 (no animation).
     */
    animation?: number;
    /**
     * Maximum number of items to display. Extra selected items will be summarized.
     * Optional, defaults to 3.
     */
    maxCount?: number;
    /**
     * The modality of the popover. When set to true, interaction with outside elements
     * will be disabled and only popover content will be visible to screen readers.
     * Optional, defaults to false.
     */
    modalPopover?: boolean;
    /**
     * If true, renders the multi-select component as a child of another component.
     * Optional, defaults to false.
     */
    asChild?: boolean;
    /**
     * Additional class names to apply custom styles to the multi-select component.
     * Optional, can be used to add custom styles.
     */
    className?: string;
}
declare const MultiSelectAdvance: React$1.ForwardRefExoticComponent<MultiSelectProps & React$1.RefAttributes<HTMLButtonElement>>;

type TreeViewElement = {
    id: string;
    name: string;
    isSelectable?: boolean;
    children?: TreeViewElement[];
};
interface TreeViewComponentProps$1 extends React__default.HTMLAttributes<HTMLDivElement> {
}
declare const Tree: React__default.ForwardRefExoticComponent<{
    initialSelectedId?: string;
    indicator?: boolean;
    elements?: TreeViewElement[];
    initialExpendedItems?: string[];
    openIcon?: React__default.ReactNode;
    closeIcon?: React__default.ReactNode;
} & TreeViewComponentProps$1 & React__default.RefAttributes<HTMLDivElement>>;
interface FolderComponentProps extends React__default.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item> {
}
declare const Folder: React__default.ForwardRefExoticComponent<{
    expendedItems?: string[];
    element: string;
    isSelectable?: boolean;
    isSelect?: boolean;
} & FolderComponentProps & React__default.HTMLAttributes<HTMLDivElement> & React__default.RefAttributes<HTMLDivElement>>;
declare const File$1: React__default.ForwardRefExoticComponent<{
    value: string;
    handleSelect?: (id: string) => void;
    isSelectable?: boolean;
    isSelect?: boolean;
    fileIcon?: React__default.ReactNode;
} & Omit<AccordionPrimitive.AccordionTriggerProps & React__default.RefAttributes<HTMLButtonElement>, "ref"> & React__default.RefAttributes<HTMLButtonElement>>;
declare const CollapseButton: React__default.ForwardRefExoticComponent<{
    elements: TreeViewElement[];
    expandAll?: boolean;
} & React__default.HTMLAttributes<HTMLButtonElement> & React__default.RefAttributes<HTMLButtonElement>>;

interface TreeViewComponentProps extends React__default.HTMLAttributes<HTMLDivElement> {
}
type TreeViewProps = {
    initialSelectedId?: string;
    elements: TreeViewElement[];
    indicator?: boolean;
} & ({
    initialExpendedItems?: string[];
    expandAll?: false;
} | {
    initialExpendedItems?: undefined;
    expandAll: true;
}) & TreeViewComponentProps;
declare const TreeView: {
    ({ elements, className, initialSelectedId, initialExpendedItems, expandAll, indicator, }: TreeViewProps): react_jsx_runtime.JSX.Element;
    displayName: string;
};
declare const TreeItem: React__default.ForwardRefExoticComponent<{
    elements?: TreeViewElement[];
    indicator?: boolean;
} & React__default.HTMLAttributes<HTMLUListElement> & React__default.RefAttributes<HTMLUListElement>>;

interface NavigatorDropdownCommonProps {
    navigation: Array<NavigatorDropdownItem>;
    userPermissions: Array<BtaskeePermissions['key']>;
    visibleItems?: number;
}
declare const NavigatorDropdownCommon: ({ navigation, userPermissions, visibleItems, }: NavigatorDropdownCommonProps) => react_jsx_runtime.JSX.Element;

type LanguageTextDisplayLayout = 'row' | 'column' | 'grid';
/**
 * LanguageTextDisplay Component
 *
 * NOTE: Wrapper of this component should not set fixed width and height
 *
 * Renders text in multiple languages with corresponding country flags.
 *
 * @component
 * @param {Object} props - The component props
 * @param {Record<string, string>} props.text - An object containing text for different languages.
 *                                              Keys are language codes, values are the translated text.
 * @param {('row'|'column'|'grid')} [props.layout='grid'] - The layout style for displaying languages.
 *
 * @returns {JSX.Element} A React component displaying multilingual text with flags
 * @example
 * <LanguageTextDisplay
 *   text={{
 *     vi: "",
 *     ko: "",
 *     th: "",
 *     id: "",
 *     en: ""
 *   }}
 *   layout="row"
 * />
 */
declare const MultiLanguageText: ({ text, layout, }: {
    text: Record<string, string>;
    layout?: LanguageTextDisplayLayout;
}) => JSX.Element;

declare const AlertDialogContext: React__default.Context<(<T extends AlertAction>(params: T) => Promise<T["type"] extends "alert" | "confirm" ? boolean : null | string>)>;
type AlertAction = {
    type: 'alert';
    title: string;
    body?: string;
    cancelButton?: string;
} | {
    type: 'confirm';
    title: string;
    body?: string | React__default.ReactNode;
    cancelButton?: string;
    actionButton?: string;
} | {
    type: 'prompt';
    title: string;
    body?: string | React__default.ReactNode;
    cancelButton?: string;
    actionButton?: string;
    defaultValue?: string;
    inputProps?: React__default.DetailedHTMLProps<React__default.InputHTMLAttributes<HTMLInputElement>, HTMLInputElement>;
} | {
    type: 'close';
};
type ConfirmationParams$1<T extends 'alert' | 'confirm' | 'prompt'> = Omit<Extract<AlertAction, {
    type: T;
}>, 'type'> | string;

interface BtaskeeResponseErrorProps {
    t: ReturnType<typeof useTranslation>['t'];
    errorStatus: ErrorResponse['status'];
}
/**
 * Renders an error message with an appropriate image based on the HTTP error status.
 *
 * @param {Object} props - The component props.
 * @param {Function} props.t - Translation function from i18next.
 * @param {number} props.errorStatus - HTTP error status code.
 *
 * @returns {JSX.Element} The rendered error component.
 *
 * @description
 * This component handles the following error codes:
 * - 403: Access Denied
 *   - title: 'ACCESS_DENIED'
 *   - description: 'NOT_PERMISSION'
 * - 404: Page Not Found
 *   - title: 'PAGE_NOT_FOUND'
 *   - description: 'PAGE_NOT_EXIST'
 * - 500 (default for unhandled errors): Server Error
 *   - title: 'SERVER_ERROR'
 *   - description: 'TRY_AGAIN_LATER'
 *
 * The component will display the appropriate error image and message based on the errorStatus.
 * If an unhandled error status is provided, it will default to the 500 error message.
 */
declare function BtaskeeResponseError({ t, errorStatus, }: BtaskeeResponseErrorProps): react_jsx_runtime.JSX.Element;

declare const PermissionEditIcon: ({ props }: {
    props?: SVGProps<SVGSVGElement>;
}) => react_jsx_runtime.JSX.Element;

interface StatusBadgeProps {
    status: string;
    statusClasses?: {
        [key: string]: string;
    };
}
declare const PartnerStatusBadge: ({ status, statusClasses, }: StatusBadgeProps) => react_jsx_runtime.JSX.Element;

declare const ExpandableCard: ({ children, t, }: {
    children: ReactNode;
    t: MustBeAny;
}) => react_jsx_runtime.JSX.Element;

declare const CardCodeType: ({ form, isEdit, }: {
    form: UseFormReturn<BRewardFormData>;
    isEdit?: boolean;
}) => react_jsx_runtime.JSX.Element;

interface ButtonConfigProps {
    nameTypeOfButton: string;
    title: string;
    form: UseFormReturn<MustBeAny>;
    services: MustBeAny;
    globalData: MustBeAny;
}
declare const ButtonConfig: ({ title, form, nameTypeOfButton, services, globalData, }: ButtonConfigProps) => react_jsx_runtime.JSX.Element;

declare const BRewardTableWithPartner: ({ data, total, filterValue, }: {
    data: Array<BReward & {
        _id: string;
    }>;
    total: number;
    filterValue: {
        search: string;
        rangeDate: {
            from: Date;
            to: Date;
        };
    };
}) => react_jsx_runtime.JSX.Element;

declare const TargetUserCardContent: ({ form, translationKey, }: {
    form: UseFormReturn<MustBeAny>;
    translationKey?: string;
}) => react_jsx_runtime.JSX.Element;

type Props$1 = {
    /**
     * You are encouraged to add a fallback that is the same dimensions
     * as the client rendered children. This will avoid content layout
     * shift which is disgusting
     */
    children(): React.ReactNode;
    fallback?: React.ReactNode;
};
/**
 * Render the children only after the JS has loaded client-side. Use an optional
 * fallback component if the JS is not yet loaded.
 *
 * Example: Render a Chart component if JS loads, renders a simple FakeChart
 * component server-side or if there is no JS. The FakeChart can have only the
 * UI without the behavior or be a loading spinner or skeleton.
 * ```tsx
 * return (
 *   <ClientOnly fallback={<FakeChart />}>
 *     {() => <Chart />}
 *   </ClientOnly>
 * );
 * ```
 */
declare function ClientOnly({ children, fallback }: Props$1): react_jsx_runtime.JSX.Element;

/**
 * Return a boolean indicating if the JS has been hydrated already.
 * When doing Server-Side Rendering, the result will always be false.
 * When doing Client-Side Rendering, the result will always be false on the
 * first render and true from then on. Even if a new component renders it will
 * always start with true.
 *
 * Example: Disable a button that needs JS to work.
 * ```tsx
 * let hydrated = useHydrated();
 * return (
 *   <button type="button" disabled={!hydrated} onClick={doSomethingCustom}>
 *     Click me
 *   </button>
 * );
 * ```
 */
declare function useHydrated(): boolean;

declare const RichTextComponent: ({ markdown, onChange, onBlur, placeholder, formFieldRefCallback, readOnly, }: {
    markdown: string;
    placeholder?: string;
    onChange?: (value: string) => void;
    onBlur?: () => void;
    formFieldRefCallback?: RefCallBack;
    readOnly?: boolean;
}) => react_jsx_runtime.JSX.Element;

/**
 * @module CardInformation
 * @description This module exports a CardInformation component and a BlockDescription component for displaying structured information in a card format.
 */

/**
 * @component BlockDescription
 * @description Renders a block of information with a label, optional value, tags, and custom node.
 * @param {Object} props - The component props
 * @param {Object} props.desc - The description object
 * @param {string} props.desc.label - The label for the description
 * @param {string} [props.desc.value] - The value for the description
 * @param {string[]} [props.desc.tags] - An array of tags
 * @param {ReactNode} [props.desc.customValueNode] - A custom React node to render as value
 * @param {'sm' | 'md'} [props.variant='md'] - The size variant of the component
 * @param {string} [props.className] - Additional CSS classes
 * @returns {JSX.Element} The rendered BlockDescription component
 */
declare const BlockDescription: ({ desc, variant, className, }: {
    desc: {
        label: string;
        value?: string;
        tags?: string[];
        customValueNode?: ReactNode;
    };
    variant?: "sm" | "md";
    className?: string;
}) => JSX.Element;
/**
 * @component CardInformation
 * @description Renders a grid of BlockDescription components.
 * @param {Object} props - The component props
 * @param {Array<Object>} props.descriptions - An array of description objects
 * @param {string} [props.className] - Additional CSS classes for the container
 * @param {string} [props.classNameBlockDescription] - Additional CSS classes for each BlockDescription
 * @param {'sm' | 'md'} [props.variant='md'] - The size variant of the component
 * @returns {JSX.Element} The rendered CardInformation component
 */
declare const CardInformation: ({ descriptions, className, classNameBlockDescription, variant, }: {
    descriptions: Array<{
        label: string;
        value?: string;
        tags?: string[];
        customValueNode?: ReactNode;
    }>;
    className?: string;
    variant?: "sm" | "md";
    classNameBlockDescription?: string;
}) => JSX.Element;

interface CommentComponentProps {
    comment: SerializeFrom<Comment>;
    isReply?: boolean;
    isLastReply?: boolean;
    isHidden?: boolean;
    adminId?: CommunityUser['_id'];
    onClickReply: (comment: SerializeFrom<Comment>, rootCommentId: SerializeFrom<Comment>['_id']) => void;
    onClickLike: (comment: SerializeFrom<Comment>) => void;
    onClickHide: (comment: SerializeFrom<Comment>) => void;
    rootCommentId: SerializeFrom<Comment>['_id'];
}
declare const CommentComponent: FC<CommentComponentProps>;

interface CommentInputProps {
    value: string;
    setValue: Dispatch<SetStateAction<string>>;
    mention: string;
    onClickRemoveMention: (e: React.MouseEvent<SVGSVGElement, MouseEvent>) => void;
    onKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
}
declare const CommentInput: React$1.ForwardRefExoticComponent<CommentInputProps & React$1.RefAttributes<HTMLTextAreaElement>>;

interface GeneralInformationProps {
    postData: SerializeFrom<CommunityPostDetail>;
}
declare const GeneralInformationComponent: FC<GeneralInformationProps>;

declare const HelpActions: ({ openAssignDialog, openResolveDialog, isDisableAssignButton, isDisableResolveButton, }: {
    openAssignDialog: () => void;
    openResolveDialog: () => void;
    isDisableAssignButton: boolean;
    isDisableResolveButton: boolean;
}) => react_jsx_runtime.JSX.Element;

interface HelpComponentProps {
    postData: SerializeFrom<CommunityPostDetail>;
    openAssignDialog: () => void;
    openResolveDialog: () => void;
    isDisableAssignButton: boolean;
    isDisableResolveButton: boolean;
    isNeedHelp: boolean;
}
declare const HelpComponent: FC<HelpComponentProps>;

declare const zodSchedulePostSchema: z.ZodObject<{
    isSchedule: z.ZodOptional<z.ZodBoolean>;
    scheduleDate: z.ZodOptional<z.ZodEffects<z.ZodDate, Date, Date>>;
}, "strip", z.ZodTypeAny, {
    isSchedule?: boolean | undefined;
    scheduleDate?: Date | undefined;
}, {
    isSchedule?: boolean | undefined;
    scheduleDate?: Date | undefined;
}>;
interface PostActionsProps {
    isAskerPost: boolean;
    isPosted: boolean;
    canEdit: boolean;
    isPinned?: boolean;
    editUrlDestination: string;
    handleHidePost: () => void;
    handleSubmitSchedulePost: (data: z.infer<typeof zodSchedulePostSchema>) => void;
    canHidePost: boolean;
    initialData: SerializeFrom<Pick<CommunityPostDetail, 'scheduleTime' | 'status'>>;
    isCanSchedule?: boolean;
    isScheduleDialogOpen?: boolean;
    onScheduleDialogOpenChange?: (open: boolean) => void;
    isCanUnhidePost: boolean;
    handleUnhidePost: () => void;
    handlePinPost?: () => void;
    handleUnpinPost?: () => void;
}
declare const PostActions: FC<PostActionsProps>;

interface PostContentProps {
    post: SerializeFrom<CommunityPostDetail & {
        user: Pick<CommunityUser, 'isAdmin'>;
    }>;
    repost?: SerializeFrom<Pick<CommunityPost, '_id' | 'content' | 'images' | 'videos' | 'tagIds' | 'scheduleTime' | 'status' | 'createdAt'> & {
        tags: Array<CommunityTag>;
        userName: string;
        userAvatar: string;
        userType: string;
    }>;
}
declare const PostContent: FC<PostContentProps>;

interface PostStatsProps {
    post: SerializeFrom<CommunityPostDetail>;
    isLiked: boolean;
    onClickLike: (e: React.MouseEvent<Element>) => void;
    onClickSharePost: (e: React.MouseEvent<Element>) => void;
    onClickComment: (e: React.MouseEvent<Element>) => void;
    canSharePost: boolean;
}
declare const PostStats: FC<PostStatsProps>;

interface ReportActionProps {
    openDismissAllReportDialog: () => void;
    isDisableDismissAllPostButton: boolean;
}
declare const ReportAction: ({ openDismissAllReportDialog, isDisableDismissAllPostButton, }: ReportActionProps) => react_jsx_runtime.JSX.Element;

interface PageHeaderProps {
    title: string;
}
declare const PageHeader: ({ title }: PageHeaderProps) => react_jsx_runtime.JSX.Element;

type MediaType = 'image' | 'video';
interface MediaFile {
    file: string | File;
    preview: string | Blob;
    type: MediaType;
}

type Slot = {
    id: string;
    file?: File;
    error?: string;
    isOld?: boolean;
    url?: string;
    type: ReturnType<typeof getFileType>;
    name: string;
    size: number;
};
type MediaFileExtended = MediaFile & {
    size: number;
    name: string;
    isOld: boolean;
};
type BtaskeeDropzoneProps = Pick<DropzoneOptions, 'accept' | 'maxFiles' | 'maxSize' | 'minSize' | 'multiple'> & {
    initSlots?: Array<Required<Pick<Slot, 'url'>> & Partial<Slot>>;
    handleUploadFile: (payload: {
        mediaFiles: MediaFileExtended[];
        mediaFilesByType: Record<ReturnType<typeof getFileType>, MediaFileExtended[]>;
    }) => void;
    isOpen: boolean;
    handleToggle: () => void;
    messageNotes?: Partial<{
        fileType: string;
        fileSize: string;
        maxFiles: string;
    }>;
    handleRemoveSlot?: (slot: Slot) => void;
};
declare const BtaskeeDropzone: (props: BtaskeeDropzoneProps) => react_jsx_runtime.JSX.Element;

type OutsideProps = Omit<BtaskeeDropzoneProps, 'isOpen' | 'handleToggle'>;
type Props = OutsideProps & {
    isOpen?: boolean;
    handleToggleUploadMedia?: () => void;
};
declare const UploadButton: (props: Props) => react_jsx_runtime.JSX.Element;

type PostFormData = z.infer<ReturnType<typeof usePostSchema>>;
interface PostFormProps extends Omit<ComponentProps<typeof UploadButton>, 'handleToggleUploadMedia' | 'isOpen' | 'handleUploadFile' | 'handleToggle'> {
    originalData?: SerializeFrom<Pick<CommunityPost, '_id' | 'content' | 'images' | 'videos' | 'tagIds' | 'scheduleTime' | 'status'>>;
    tagNameList: SerializeFrom<Array<CommunityTag>>;
    onSubmit: (data: PostFormData) => void;
    t: TFunction;
    isConfirm: (params: ConfirmationParams$1<'confirm'>) => Promise<boolean>;
}
declare const PostForm: ({ originalData, tagNameList, onSubmit, t, isConfirm, ...restProps }: PostFormProps) => react_jsx_runtime.JSX.Element;

interface ReadMarkdownOnlyProps {
    content: string;
    className?: string;
    isDetail?: boolean;
}
declare const ReadMarkdownOnly: ({ content, className, isDetail }: ReadMarkdownOnlyProps) => react_jsx_runtime.JSX.Element;

declare function AddRoleDialog({ tableData, setDraftSelectedRoles, defaultValues, }: {
    tableData: MustBeAny;
    setDraftSelectedRoles: MustBeAny;
    defaultValues?: MustBeAny;
}): react_jsx_runtime.JSX.Element;

declare function AddUserDialog({ tableData, setDraftSelectedUsers, defaultValues, }: {
    tableData: MustBeAny;
    setDraftSelectedUsers: MustBeAny;
    defaultValues?: MustBeAny;
}): react_jsx_runtime.JSX.Element;

declare const AskerInfoDialog: ({ asker, t, }: {
    asker: AskerGeneral;
    t: MustBeAny;
}) => react_jsx_runtime.JSX.Element;
declare const FooterButton: ({ warningText, isDirty, errors, }: {
    warningText?: string;
    isDirty: boolean;
    errors: FormState<MustBeAny>["errors"];
}) => react_jsx_runtime.JSX.Element;
declare const TagBadge: ({ text, variant, size, }: {
    text: string;
    variant: "success" | "info" | "danger" | string;
    size?: "sm" | "md";
}) => react_jsx_runtime.JSX.Element;
declare const TaskTypeBadge: ({ task, isServiceType, size, }: {
    task: MustBeAny;
    isServiceType: boolean;
    size?: "sm" | "md";
}) => react_jsx_runtime.JSX.Element | "-";

declare const InfoCardWrapper: ({ className, children }: InfoCardProps) => react_jsx_runtime.JSX.Element;
declare const InfoBlock: ({ label, value, variant, }: InfoBlockProps) => react_jsx_runtime.JSX.Element;

declare const RankImage: ({ rankName, SVGProps, }: {
    rankName: (typeof USER_RANKS)[keyof typeof USER_RANKS];
    SVGProps?: SVGProps<SVGSVGElement>;
}) => react_jsx_runtime.JSX.Element | null;

declare const MemberRank: () => react_jsx_runtime.JSX.Element;

declare const MockMemberIcon: () => react_jsx_runtime.JSX.Element;

declare const GoldRank: (props: SVGProps<SVGSVGElement>) => react_jsx_runtime.JSX.Element;
declare const SilverRank: (props: SVGProps<SVGSVGElement>) => react_jsx_runtime.JSX.Element;
declare const PlatinumRank: (props: SVGProps<SVGSVGElement>) => react_jsx_runtime.JSX.Element;

declare const TagIcon: ({ props }: {
    props?: SVGProps<SVGSVGElement>;
}) => react_jsx_runtime.JSX.Element;

declare const GraphLogo: (props: SVGProps<SVGSVGElement>) => react_jsx_runtime.JSX.Element;
declare const SendLogo: (props: SVGProps<SVGSVGElement>) => react_jsx_runtime.JSX.Element;
declare const ActivityLogo: (props: SVGProps<SVGSVGElement>) => react_jsx_runtime.JSX.Element;
declare const StarLogo: (props: SVGProps<SVGSVGElement>) => react_jsx_runtime.JSX.Element;
declare const HeartLogo: (props: SVGProps<SVGSVGElement>) => react_jsx_runtime.JSX.Element;

declare const VectorEmptyDataTable: (props: SVGProps<SVGSVGElement>) => react_jsx_runtime.JSX.Element;

interface ControllerComboboxProps<T extends FieldValues> {
    options: {
        value: string;
        label: string;
    }[];
    placeholder: string;
    searchPlaceholder?: string;
    emptyMessage?: string;
    field: ControllerRenderProps<T, Path<T>>;
}
declare function ControllerCombobox<T extends FieldValues>({ options, placeholder, searchPlaceholder, emptyMessage, field, }: ControllerComboboxProps<T>): react_jsx_runtime.JSX.Element;

type Month$1 = {
    number: number;
    name: string;
};
type MonthCalProps = {
    currentSelectedDate?: Date;
    onMonthSelect?: (date: Date) => void | Promise<void>;
    onYearForward?: () => void;
    onYearBackward?: () => void;
    onWholeYearSelect?: (date: Date) => void | Promise<void>;
    callbacks?: {
        yearLabel?: (year: number) => string;
        monthLabel?: (month: Month$1) => string;
    };
    variant?: {
        calendar?: {
            main?: ButtonVariant$1;
            selected?: ButtonVariant$1;
        };
        chevrons?: ButtonVariant$1;
    };
    minDate?: Date;
    maxDate?: Date;
    disabledDates?: Date[];
};
type ButtonVariant$1 = 'default' | 'outline' | 'ghost' | 'link' | 'destructive' | 'secondary' | null | undefined;
declare function MonthYearPicker({ onSelectMonth, minDate, maxDate, disabledDates, callbacks, onYearBackward, onYearForward, variant, className, initialDateFrom, initialDateTo, mode, onWholeYearSelect, ...props }: HTMLAttributes<HTMLDivElement> & Omit<MonthCalProps, 'currentSelectedDate' | 'onMonthSelect'> & {
    initialDateFrom?: Date;
    initialDateTo?: Date;
    onSelectMonth: (selectedMonth: Date) => void | Promise<void>;
    mode?: 'month' | 'year';
}): react_jsx_runtime.JSX.Element;
declare namespace MonthYearPicker {
    var displayName: string;
}

declare const DndTable: ({ columns, initialData, setDataAfterReOrder, manualPagination }: {
    columns: ColumnDef<any>[];
    initialData: any;
    setDataAfterReOrder: any;
    manualPagination?: boolean;
}) => react_jsx_runtime.JSX.Element;

type MultiLanguageSingleSectionProps = {
    form?: UseFormReturn<any>;
    data?: Record<string, Record<string, any>>;
    children: React__default.ReactElement | React__default.ReactElement[];
    childrenProps: {
        name: string;
        label: string;
        required?: string;
        layout?: 'full' | 'half';
        type?: 'richtext' | 'input';
    }[];
    parentField?: string;
    useNestedStructure?: boolean;
    order?: number;
} & Omit<AccordionSingleProps, 'collapsible' | 'type'> & React__default.RefAttributes<HTMLDivElement>;
/**
 * A component that provides multi-language form sections with accordion functionality.
 * Supports both form input mode and read-only display mode.
 *
 * @param {Object} props - Component props
 * @param {UseFormReturn<any>} [props.form] - React Hook Form instance for form handling
 * @param {Record<string, Record<string, any>>} [props.data] - Data for read-only mode
 * @param {React.ReactElement | React.ReactElement[]} props.children - Form input elements
 * @param {Object[]} props.childrenProps - Configuration for each child element
 * @param {string} props.childrenProps[].name - Field name
 * @param {string} props.childrenProps[].label - Field label
 * @param {string} [props.childrenProps[].required] - Required field validation message
 * @param {('full' | 'half')} [props.childrenProps[].layout] - Layout width configuration
 * @param {('richtext' | 'input')} [props.childrenProps[].type] - Input type
 * @param {string} [props.parentField] - Parent field name for nested structures
 * @param {boolean} [props.useNestedStructure=false] - Whether to use nested field naming
 * @param {string} [props.className] - Additional CSS classes for the accordion
 * @param {number} [props.order] - Scroll priority
 *
 * @returns {JSX.Element} A multi-language form section component
 */
declare function MultiLanguageSingleSection({ children: inputChildren, form, data, childrenProps, parentField, useNestedStructure, className: AccordionExpandClassName, order, ...restAccordionProps }: MultiLanguageSingleSectionProps): react_jsx_runtime.JSX.Element;

interface CommunityTagManagementFormProps {
    translationKey: string;
    defaultValues?: CommunityTagCreationForm;
    isDisabledRangeDate?: boolean;
}
declare const CommunityTagManagementForm: ({ translationKey, isDisabledRangeDate, defaultValues, }: CommunityTagManagementFormProps) => react_jsx_runtime.JSX.Element;

declare const ZoomableImage: ({ src, alt, className, }: DetailedHTMLProps<ImgHTMLAttributes<HTMLImageElement>, HTMLImageElement>) => react_jsx_runtime.JSX.Element | null;

interface LocationBase {
    localizeKey: string;
    defaultValue?: CustomLocation;
}
interface ReadOnlyProps extends LocationBase {
    onlyRead: true;
    onLocationSelect?: (location: CustomLocation) => void;
}
interface EditableProps extends LocationBase {
    onlyRead?: false;
    onLocationSelect: (location: CustomLocation) => void;
}
type LocationPickerProps = ReadOnlyProps | EditableProps;
/**
 * LocationPicker is a component that provides an interactive interface for selecting and visualizing locations using Google Maps.
 * It includes address autocomplete, radius control, and map visualization features.
 *
 * @param {Object} props - Component props
 * @param {Function} props.onLocationSelect - Callback function that receives the selected location data
 * in the format: { address: string, lat: number, lng: number, radius: number }
 * @param {string} props.localizeKey - Translation namespace key for i18n
 * @param {Object} [props.defaultValue] - Initial location data
 * @param {string} props.defaultValue.address - Default address string
 * @param {number} props.defaultValue.lat - Default latitude
 * @param {number} props.defaultValue.lng - Default longitude
 * @param {number} props.defaultValue.radius - Default radius in meters
 * @param {boolean} [props.onlyRead=false] - Enable read-only mode
 *
 * @requires @react-google-maps/api
 * @requires btaskee-ui
 * @requires react-i18next
 * @requires window.GOOGLE_API_KEY
 *
 * @returns {JSX.Element} A form with address input, radius control, and map visualization
 */
declare const LocationPicker: ({ onLocationSelect, localizeKey, defaultValue, onlyRead, }: LocationPickerProps) => react_jsx_runtime.JSX.Element;

declare function useBtaskeeFormController<TFieldValues extends FieldValues>({ zodRaw, defaultValues, confirmParams, formDataProvided, }: {
    zodRaw: ZodRawShape;
    defaultValues: UseFormProps<TFieldValues>['defaultValues'];
    confirmParams: ConfirmationParams<'confirm'>;
    formDataProvided: (data: TFieldValues) => FormData;
}): {
    form: react_hook_form.UseFormReturn<TFieldValues, any, undefined>;
    onSubmit: (data: TFieldValues) => Promise<void>;
};

declare function useConfirm(): (params: ConfirmationParams$1<"confirm">) => Promise<boolean>;
declare function usePrompt(): (params: ConfirmationParams$1<"prompt">) => Promise<string | null>;
declare function useAlert(): (params: ConfirmationParams$1<"alert">) => Promise<boolean>;

declare function useErrorBoundaryDialogRoute(): {};

type DateRangeProps = {
    from: Date;
    to: Date;
} | undefined;
interface DateRangePickerProps {
    onUpdate?: (value: DateRangeProps) => void;
    initialRangeDate?: DateRangeProps;
    align?: 'start' | 'center' | 'end';
    className?: string;
    formatDateTriggerButtonText?: string;
    resetSignal?: number;
    calendarProps?: Omit<CalendarProps, 'mode' | 'selected' | 'onSelect' | 'numberOfMonths' | 'defaultMonth'>;
}
declare const DateRangePickerV2: FC<DateRangePickerProps>;

type Month = {
    number: number;
    name: string;
    yearOffset: number;
};
type QuickSelector = {
    label: string;
    startMonth: Date;
    endMonth: Date;
    variant?: ButtonVariant;
    onClick?: (selector: QuickSelector) => void;
};
type MonthRangeCalProps = {
    selectedMonthRange?: {
        from: Date;
        to: Date;
    };
    onStartMonthSelect?: (date: Date) => void;
    onMonthRangeSelect?: ({ from, to }: {
        from: Date;
        to: Date;
    }) => void;
    callbacks?: {
        yearLabel?: (year: number) => string;
        monthLabel?: (month: Month) => string;
    };
    variant?: {
        calendar?: {
            main?: ButtonVariant;
            selected?: ButtonVariant;
        };
        chevrons?: ButtonVariant;
    };
    minDate?: Date;
    maxDate?: Date;
    quickSelectors?: QuickSelector[];
    showQuickSelectors?: boolean;
};
type ButtonVariant = 'default' | 'outline' | 'ghost' | 'link' | 'destructive' | 'secondary' | null | undefined;
declare function MonthRangePicker({ onMonthRangeSelect, onStartMonthSelect, callbacks, variant, minDate, maxDate, quickSelectors, showQuickSelectors, defaultRangeMonth, className, ...props }: React$1.HTMLAttributes<HTMLDivElement> & Omit<MonthRangeCalProps, 'selectedMonthRange'> & {
    defaultRangeMonth?: {
        from: Date;
        to: Date;
    };
}): react_jsx_runtime.JSX.Element;
declare namespace MonthRangePicker {
    var displayName: string;
}
declare function YearRangePicker({ onYearRangeSelect, onStartYearSelect, variant, minDate, maxDate, defaultRangeYear, className, ...props }: React$1.HTMLAttributes<HTMLDivElement> & {
    onYearRangeSelect?: ({ from, to }: {
        from: Date;
        to: Date;
    }) => void;
    onStartYearSelect?: (date: Date) => void;
    variant?: {
        calendar?: {
            main?: ButtonVariant;
            selected?: ButtonVariant;
        };
        chevrons?: ButtonVariant;
    };
    minDate?: Date;
    maxDate?: Date;
    defaultRangeYear?: {
        from: Date;
        to: Date;
    };
}): react_jsx_runtime.JSX.Element;
declare namespace YearRangePicker {
    var displayName: string;
}

type ImageCropperProps = {
    imageName: string;
    imageUrl: string;
    onCropComplete: (payload: {
        file: File;
        url: string;
    }) => void;
    onCancel: () => void;
    open: boolean;
};
declare function ImageCropper({ imageName, imageUrl, onCropComplete, onCancel, open }: ImageCropperProps): react_jsx_runtime.JSX.Element | null;

type MediaPreview = Required<Omit<Slot, 'error' | 'file'>> & Pick<Slot, 'file'>;

type MediaFilePreviewProps = {
    media: MediaPreview[];
    initialIndex?: number;
    open: boolean;
    onClose: () => void;
    onCrop?: (index: number) => void;
};
declare const MediaFilePreview: ({ media, initialIndex, open, onClose, onCrop }: MediaFilePreviewProps) => react_jsx_runtime.JSX.Element | null;

export { Accordion, AccordionContent, AccordionItem, AccordionTrigger, AccountantLoginLogo, AccountantLogo, ActivityLogo, AddRoleDialog, AddUserDialog, AdminLoginLogo, AdminLogo, Alert, type AlertAction, AlertBase, type AlertBaseProps, AlertDescription, AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogContext, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogOverlay, AlertDialogPortal, AlertDialogProvider, AlertDialogTitle, AlertDialogTrigger, AlertTitle, AreaBase, type AreaBaseProps, AreaChartBase, type AreaChartBaseProps, AskerInfoDialog, AspectRatio, AutoComplete, type AutoCompleteProps, type AutosizeTextAreaRef, AutosizeTextarea, Avatar, AvatarFallback, AvatarImage, AvatarUpload, BRewardTableWithPartner, BTaskeeTable, BTaskeeTableV2, Badge, type BadgeProps, BarBase, type BarBaseProps, BarChartBase, BarChartLabel, type BarChartLabelProps, type BarChartProps, BlockDescription, Breadcrumb, BreadcrumbEllipsis, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator, Breadcrumbs, BreadcrumbsLink, BtaskeeDropzone, type BtaskeeDropzoneProps, BtaskeeResponseError, Button, ButtonConfig, type ButtonConfigProps, type ButtonProps, Calendar, Card, CardCodeType, CardContent, CardDescription, CardFooter, CardHeader, CardInformation, CardStatistic, CardTitle, Carousel, type CarouselApi, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious, CartesianGridBase, type CartesianGridBaseProps, CellBase, type CellBaseProps, type ChartConfig, ChartContainer, ChartLegend, ChartLegendContent, ChartStyle, ChartTooltip, ChartTooltipContent, Checkbox, ClientOnly, CollapseButton, Collapsible, CollapsibleContent, CollapsibleTrigger, Command, CommandDialog, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, CommandSeparator, CommandShortcut, CommentComponent, CommentInput, CommunityTagManagementForm, type CommunityTagManagementFormProps, type ConfirmationParams$1 as ConfirmationParams, Container, ContextMenu, ContextMenuCheckboxItem, ContextMenuContent, ContextMenuGroup, ContextMenuItem, ContextMenuLabel, ContextMenuPortal, ContextMenuRadioGroup, ContextMenuRadioItem, ContextMenuSeparator, ContextMenuShortcut, ContextMenuSub, ContextMenuSubContent, ContextMenuSubTrigger, ContextMenuTrigger, ControllerCombobox, CustomerSupportLoginLogo, CustomerSupportLogo, DATE_RANGE_PICKER_OPTIONS, DataTableBasic, DataTableColumnHeader, DataTableFacetedFilter, DataTablePagination, DataTableRowActions, type DataTableRowActionsProps, DataTableToolbar, DataTableViewOptions, DateRangePicker, type DateRangePickerOptions, type DateRangePickerProps, DateRangePickerV2, type DateRangeProps$1 as DateRangeProps, DateTimePicker, Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogOverlay, DialogPortal, DialogTitle, DialogTrigger, DndTable, Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerFooter, DrawerHeader, DrawerOverlay, DrawerPortal, DrawerTitle, DrawerTrigger, DropdownMenu, DropdownMenuBase, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuPortal, DropdownMenuRadioGroup, DropdownMenuRadioItem, DropdownMenuSeparator, DropdownMenuShortcut, DropdownMenuSub, DropdownMenuSubContent, DropdownMenuSubTrigger, DropdownMenuTrigger, ErrorMessageBase, ExpandableCard, File$1 as File, Folder, FooterButton, Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage, FormSubmissionButton, GeneralInformationComponent, GoldRank, GraphLogo, Grid, GridItem, HeartLogo, HelpActions, HelpComponent, HourRangePicker, type HourRangePickerProps, HoverCard, HoverCardContent, HoverCardTrigger, ITEMS_LANGUAGE, ImageCropper, type ImageCropperProps, ImageUpload, type ImageUploadProps, InfoBlock, InfoCardWrapper, Input$1 as Input, InputOTP, InputOTPGroup, InputOTPSeparator, InputOTPSlot, Label, LegendBase, type LegendBaseProps, LineBase, type LineBaseProps, LineChartBase, type LineChartBaseProps, LoadingGlobal, LoadingSpinner, LocationPicker, MarketingLoginLogo, MarketingLogo, MediaFilePreview, type MediaFilePreviewProps, MemberRank, Menubar, MenubarCheckboxItem, MenubarContent, MenubarGroup, MenubarItem, MenubarLabel, MenubarMenu, MenubarPortal, MenubarRadioGroup, MenubarRadioItem, MenubarSeparator, MenubarShortcut, MenubarSub, MenubarSubContent, MenubarSubTrigger, MenubarTrigger, MockMemberIcon, MoneyInput, MonthRangePicker, MonthYearPicker, MultiLanguageSection, MultiLanguageSectionView, MultiLanguageSingleSection, MultiLanguageText, MultiSelect, MultiSelectAdvance, MultiSelectAsync, NavigationLink, NavigationMenu, NavigationMenuContent, NavigationMenuIndicator, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuTrigger, NavigationMenuViewport, NavigatorDropdownCommon, type NavigatorDropdownCommonProps, PageHeader, Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious, PartnerStatusBadge, Input as PasswordInput, PermissionEditIcon, PieBase, type PieBaseProps, PieChartBase, type PieChartBaseProps, PlatinumRank, Popover, PopoverAnchor, PopoverContent, PopoverTrigger, PostActions, PostContent, PostForm, PostStats, Progress, RadioGroup, RadioGroupItem, RadioGroupsBase, type RadioGroupsProps, RankImage, ReadMarkdownOnly, RectangleBase, type RectangleBaseProps, ReferenceLineBase, type ReferenceLineBaseProps, ReportAction, ResizableHandle, ResizablePanel, ResizablePanelGroup, ResponsiveContainerBase, type ResponsiveContainerBaseProps, RichTextComponent, ScrollArea, ScrollBar, Select, SelectBase, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectScrollDownButton, SelectScrollUpButton, SelectSearchAsync, SelectSeparator, SelectTrigger, SelectValue, SendLogo, Separator, Sheet, SheetClose, SheetContent, SheetDescription, SheetFooter, SheetHeader, SheetOverlay, SheetPortal, SheetTitle, SheetTrigger, SilverRank, SimpleAreaChart, type SimpleAreaChartProps, SimpleBarChart, type SimpleBarChartProps, SimplePieChart, type SimplePieChartProps, SingleDatePicker, SingleDateTimePicker, type SingleDateTimePickerProps, SingleMonthPicker, SingleYearPicker, Skeleton, Slider, StackedBarChart, type StackedBarChartProps, StarLogo, StatusBadge, Switch, Table, TableBody, TableCaption, TableCell, TableFooter, TableHead, TableHeader, TableRoot, TableRow, Tabs, TabsContent, TabsList, TabsTrigger, TagBadge, TagIcon, TargetUserCardContent, TaskTypeBadge, TaskerOperationLoginLogo, TaskerOperationLogo, Textarea, TimePicker, TimePickerInput, Toast$1 as Toast, ToastAction, type ToastActionElement, ToastClose, ToastDescription, type ToastProps, ToastProvider, ToastTitle, ToastViewport, Toaster, ToasterBase, Toggle, ToggleGroup, ToggleGroupItem, Tooltip, TooltipBase, TooltipContent, TooltipProvider, TooltipTrigger, TooltipUIBase, Tree, TreeItem, TreeView, type TreeViewElement, Typography, UploadButton, VectorEmptyDataTable, XAxisBase, type XAxisBaseProps, YAxisBase, type YAxisBaseProps, YearRangePicker, ZoomableImage, badgeVariants, buttonVariants, cn, containerVariants, dropdownMenuTriggerStyle, navigationMenuItemTriggerStyle, navigationMenuTriggerStyle, toast, toggleVariants, useAlert, useAutosizeTextArea, useBtaskeeFormController, useConfirm, useErrorBoundaryDialogRoute, useFormField, useHydrated, usePrompt, useToast };
