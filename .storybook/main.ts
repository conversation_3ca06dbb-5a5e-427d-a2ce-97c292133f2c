import type { StorybookConfig } from '@storybook/react-webpack5';

const TsconfigPathsPlugin = require('tsconfig-paths-webpack-plugin');

const config: StorybookConfig = {
  stories: [
    '../stories/**/*.mdx',
    '../stories/**/*.stories.@(js|jsx|mjs|ts|tsx)',
  ],
  addons: [
    '@storybook/addon-webpack5-compiler-swc',
    '@storybook/addon-onboarding',
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@chromatic-com/storybook',
    '@storybook/addon-interactions',
    '@storybook/addon-postcss',
    'storybook-addon-remix-react-router',
  ],
  // new version of Storybook, it uses SWC compiler by default
  swc: () => ({
    jsc: {
      transform: {
        react: {
          runtime: 'automatic',
        },
      },
    },
  }),
  framework: {
    name: '@storybook/react-webpack5',
    options: {},
  },
  webpackFinal: async config => {
    if (config.resolve) {
      config.resolve.plugins = [new TsconfigPathsPlugin()];
    }
    // Configure PostCSS with Tailwind
    if (config.module?.rules) {
      const rules = config.module.rules as Array<any>;
      const cssRule = rules.find(rule => rule.test?.toString().includes('.css'));
      if (cssRule) {
        cssRule.use = [
          'style-loader',
          'css-loader',
          {
            loader: 'postcss-loader',
            options: {
              implementation: require('postcss'),
            },
          },
        ];
      }
    }
    return config;
  },
};

export default config satisfies StorybookConfig;
