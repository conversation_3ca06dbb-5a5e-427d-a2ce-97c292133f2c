import { BTaskeeTableV2 } from '@/components/btaskee/BTaskeeTable';
import { DateRangeProps } from '@/components/btaskee/DateRangePicker';
import { Button } from '@/components/ui/button';
import type { Meta, StoryObj } from '@storybook/react';
import { momentTz } from 'btaskee-utils';
import { useState } from 'react';

// Define the data type for better type safety
type TableData = {
  id: string;
  name: string;
  status: string;
  amount: number;
  created_at: Date;
  valid_from: Date;
  valid_to: Date;
};

const meta: Meta<typeof BTaskeeTableV2> = {
  title: 'Components/Btaskee/BTaskeeTable',
  component: BTaskeeTableV2,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof BTaskeeTableV2<TableData, unknown>>;

// Mock data for the table
const generateData = (count: number): TableData[] => {
  return Array.from({ length: count }).map((_, index) => ({
    id: `item-${index + 1}`,
    name: `Item ${index + 1}`,
    status:
      index % 3 === 0 ? 'Active' : index % 3 === 1 ? 'Pending' : 'Inactive',
    amount: Math.floor(Math.random() * 10000),
    created_at: momentTz()
      .subtract(Math.floor(Math.random() * 365), 'days')
      .toDate(),
    valid_from: momentTz()
      .subtract(Math.floor(Math.random() * 30), 'days')
      .toDate(),
    valid_to: momentTz()
      .add(Math.floor(Math.random() * 30), 'days')
      .toDate(),
  }));
};

// Default Story: Basic Table with Date Filtering
export const BasicTable: Story = {
  args: {
    columns: [
      {
        id: 'name',
        header: 'Name',
        accessorKey: 'name',
      },
      {
        id: 'status',
        header: 'Status',
        accessorKey: 'status',
        enableColumnFilter: true,
        meta: {
          filterVariant: 'select',
          filterOptions: [
            { label: 'Active', value: 'Active' },
            { label: 'Pending', value: 'Pending' },
            { label: 'Inactive', value: 'Inactive' },
          ],
        },
      },
      {
        id: 'amount',
        header: 'Amount',
        accessorKey: 'amount',
        cell: ({ row }) => {
          return `$${row.getValue('amount')}`;
        },
        enableColumnFilter: true,
        meta: {
          filterVariant: 'range',
        },
      },
      {
        id: 'created_at',
        header: 'Created At',
        accessorKey: 'created_at',
        cell: ({ row }) => {
          return momentTz(row.getValue('created_at')).format('DD/MM/YYYY');
        },
        enableColumnFilter: true,
        meta: {
          filterVariant: 'date',
          dateFilterOptions: {
            formatTriggerText: 'DD/MM/YYYY',
          },
        },
      },
    ],
    data: generateData(20),
    total: 20,
    pagination: { pageIndex: 0, pageSize: 10 },
  },
};

// Story: Table with Date Range Filtering and Default Selection
export const WithDefaultDateRange: Story = {
  args: {
    ...BasicTable.args,
    columns: [
      {
        id: 'name',
        header: 'Name',
        accessorKey: 'name',
      },
      {
        id: 'status',
        header: 'Status',
        accessorKey: 'status',
      },
      {
        id: 'created_at',
        header: 'Created At',
        accessorKey: 'created_at',
        cell: ({ row }) => {
          return momentTz(row.getValue('created_at')).format('DD/MM/YYYY');
        },
        enableColumnFilter: true,
        meta: {
          filterVariant: 'date',
          dateFilterOptions: {
            defaultDateRange: {
              from: momentTz().subtract(7, 'days').toDate(),
              to: momentTz().toDate(),
            },
            formatTriggerText: 'DD/MM/YYYY',
          },
        },
      },
      {
        id: 'validity_period',
        header: 'Validity Period',
        accessorKey: 'valid_from',
        cell: ({ row }) => {
          const typedRow = row as any;
          return `${momentTz(typedRow.original.valid_from).format('DD/MM/YYYY')} - ${momentTz(typedRow.original.valid_to).format('DD/MM/YYYY')}`;
        },
        enableColumnFilter: true,
        meta: {
          filterVariant: 'date',
          dateFilterOptions: {
            formatTriggerText: 'DD/MM/YYYY',
          },
        },
      },
    ],
  },
};

// Story: Multiple Date Range Filters
export const MultipleDataRangeFilters: Story = {
  args: {
    ...BasicTable.args,
    columns: [
      {
        id: 'name',
        header: 'Name',
        accessorKey: 'name',
      },
      {
        id: 'created_at',
        header: 'Created Date',
        accessorKey: 'created_at',
        cell: ({ row }) => {
          return momentTz(row.getValue('created_at')).format('DD/MM/YYYY');
        },
        enableColumnFilter: true,
        meta: {
          filterVariant: 'date',
          dateFilterOptions: {
            formatTriggerText: 'DD/MM/YYYY',
          },
        },
      },
      {
        id: 'valid_from',
        header: 'Valid From',
        accessorKey: 'valid_from',
        cell: ({ row }) => {
          return momentTz(row.getValue('valid_from')).format('DD/MM/YYYY');
        },
        enableColumnFilter: true,
        meta: {
          filterVariant: 'date',
          dateFilterOptions: {
            formatTriggerText: 'DD/MM/YYYY',
          },
        },
      },
      {
        id: 'valid_to',
        header: 'Valid To',
        accessorKey: 'valid_to',
        cell: ({ row }) => {
          return momentTz(row.getValue('valid_to')).format('DD/MM/YYYY');
        },
        enableColumnFilter: true,
        meta: {
          filterVariant: 'date',
          dateFilterOptions: {
            formatTriggerText: 'DD/MM/YYYY',
          },
        },
      },
    ],
  },
};

// Story: Interactive DateRangeFilter Example with State Management
export const InteractiveDateFilter = () => {
  const [filterParams, setFilterParams] = useState<Record<string, string>>({});
  const [data] = useState<TableData[]>(generateData(100));
  const [filteredData, setFilteredData] = useState<TableData[]>(data);
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });

  // Apply filters when URL parameters change
  const onSearchParamsChange = (newParams: URLSearchParams) => {
    const paramsObj: Record<string, string> = {};

    // Convert URLSearchParams to object
    newParams.forEach((value, key) => {
      paramsObj[key] = value;
    });

    setFilterParams(paramsObj);

    // Filter data based on date range parameters
    let tempData = [...data];

    if (paramsObj.created_at) {
      try {
        const dateRange = JSON.parse(paramsObj.created_at) as DateRangeProps;
        // Check if dateRange has the necessary properties
        if (dateRange && dateRange.from && dateRange.to) {
          const from = momentTz(dateRange.from).startOf('day');
          const to = momentTz(dateRange.to).endOf('day');

          tempData = tempData.filter(item => {
            const itemDate = momentTz(item.created_at);
            return itemDate.isSameOrAfter(from) && itemDate.isSameOrBefore(to);
          });
        }
      } catch (e) {
        console.error('Error parsing date range', e);
      }
    }

    setFilteredData(tempData);

    // Reset to first page when filters change
    if (paramsObj.pageIndex !== pagination.pageIndex.toString()) {
      setPagination(prev => ({ ...prev, pageIndex: 0 }));
    }
  };

  const columns = [
    {
      id: 'name',
      header: 'Name',
      accessorKey: 'name',
    },
    {
      id: 'status',
      header: 'Status',
      accessorKey: 'status',
    },
    {
      id: 'created_at',
      header: 'Created At',
      accessorKey: 'created_at',
      cell: ({ row }: { row: any }) => {
        return momentTz(row.getValue('created_at')).format('DD/MM/YYYY');
      },
      enableColumnFilter: true,
      meta: {
        filterVariant: 'date' as const,
        dateFilterOptions: {
          formatTriggerText: 'DD/MM/YYYY',
        },
      },
    },
  ];

  const customSetSearchParams = (
    paramSetter: (params: URLSearchParams) => URLSearchParams,
  ) => {
    // Create a mock URLSearchParams object
    const mockURLParams = new URLSearchParams(
      Object.entries(filterParams).map(([key, value]) => [key, value]),
    );

    // Apply the parameter setter function
    const newParams = paramSetter(mockURLParams);

    // Call the onChange handler with the new parameters
    onSearchParamsChange(newParams);
  };

  return (
    <div className="w-full max-w-4xl space-y-4">
      <div className="bg-blue-50 p-4 rounded-md">
        <h3 className="text-lg font-semibold mb-2">Current Filter State:</h3>
        {Object.keys(filterParams).length > 0 ? (
          <pre className="bg-white p-2 rounded-md text-sm">
            {JSON.stringify(filterParams, null, 2)}
          </pre>
        ) : (
          <p className="text-gray-500 italic">No filters applied</p>
        )}

        <div className="mt-4">
          <Button
            variant="outline"
            onClick={() => {
              setFilterParams({});
              setFilteredData(data);
            }}>
            Reset All Filters
          </Button>
        </div>
      </div>

      <div className="border rounded-md shadow-sm">
        <BTaskeeTableV2
          columns={columns}
          data={filteredData.slice(
            pagination.pageIndex * pagination.pageSize,
            (pagination.pageIndex + 1) * pagination.pageSize,
          )}
          total={filteredData.length}
          pagination={pagination}
          onClickRow={record => console.log('Row clicked:', record)}
          // @ts-ignore - Using a custom mock of useSearchParams
          _useSearchParamsOverride={{
            searchParams: new URLSearchParams(
              Object.entries(filterParams).map(([key, value]) => [key, value]),
            ),
            setSearchParams: customSetSearchParams,
          }}
        />
      </div>
    </div>
  );
};

// Story: Table With Custom Filter UI Styling
export const CustomStyledFilters: Story = {
  args: {
    ...BasicTable.args,
    columns: [
      {
        id: 'name',
        header: 'Name',
        accessorKey: 'name',
      },
      {
        id: 'status',
        header: 'Status',
        accessorKey: 'status',
        meta: {
          headerCellClassName: 'text-blue-600',
        },
      },
      {
        id: 'created_at',
        header: 'Created At',
        accessorKey: 'created_at',
        cell: ({ row }) => {
          return momentTz(row.getValue('created_at')).format('DD/MM/YYYY');
        },
        enableColumnFilter: true,
        meta: {
          filterVariant: 'date',
          headerCellClassName: 'bg-gray-50',
          dateFilterOptions: {
            formatTriggerText: 'DD/MM/YYYY',
          },
        },
      },
    ],
  },
};

// Story: Table with all date filtering columns
export const AllFilterTypes: Story = {
  args: {
    ...BasicTable.args,
    columns: [
      {
        id: 'name',
        header: 'Name',
        accessorKey: 'name',
        enableColumnFilter: true,
        meta: {
          filterVariant: 'text',
        },
      },
      {
        id: 'status',
        header: 'Status',
        accessorKey: 'status',
        enableColumnFilter: true,
        meta: {
          filterVariant: 'select',
          filterOptions: [
            { label: 'Active', value: 'Active' },
            { label: 'Pending', value: 'Pending' },
            { label: 'Inactive', value: 'Inactive' },
          ],
        },
      },
      {
        id: 'amount',
        header: 'Amount',
        accessorKey: 'amount',
        cell: ({ row }) => {
          return `$${row.getValue('amount')}`;
        },
        enableColumnFilter: true,
        meta: {
          filterVariant: 'range',
        },
      },
      {
        id: 'created_at',
        header: 'Created At',
        accessorKey: 'created_at',
        cell: ({ row }) => {
          return momentTz(row.getValue('created_at')).format('DD/MM/YYYY');
        },
        enableColumnFilter: true,
        meta: {
          filterVariant: 'date',
          dateFilterOptions: {
            formatTriggerText: 'DD/MM/YYYY',
          },
        },
      },
    ],
  },
};
