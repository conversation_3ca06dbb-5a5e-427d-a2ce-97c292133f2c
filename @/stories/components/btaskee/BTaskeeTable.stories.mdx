import { <PERSON><PERSON>, <PERSON>a, <PERSON> } from '@storybook/addon-docs';

<Meta title="Documentation/Components/BTaskeeTable" />

# BTaskeeTable Component

The `BTaskeeTableV2` component is a powerful and flexible table component for displaying and filtering data.
It supports various features including:

- Sorting
- Filtering (text, select, range, date)
- Pagination
- Column pinning and resizing

## Date Range Filtering

One of the key features is the ability to add date range filtering to columns. This allows users to filter table data by date ranges with an intuitive and clean UI.

### Date Range Filter Features

- **Compact Icon Interface**: Shows just a calendar icon in the column header
- **Visual Indicators**: Changes color when a filter is active
- **Preset Support**: Remembers selected date presets (This Year, Last Month, etc.)
- **Tooltip Information**: Shows the selected date range on hover
- **Clear Filter Option**: Allows users to easily remove the filter

### Basic Usage

```tsx
const columns = [
  {
    id: 'created_at',
    header: 'Created At',
    accessorKey: 'created_at',
    cell: ({ row }) => {
      return momentTz(row.getValue('created_at')).format('DD/MM/YYYY');
    },
    enableColumnFilter: true,
    meta: {
      filterVariant: 'date',
      dateFilterOptions: {
        formatTriggerText: 'DD/MM/YYYY',
      },
    },
  },
];

<BTaskeeTableV2
  columns={columns}
  data={data}
  total={total}
  pagination={pagination}
/>;
```

### With Default Date Range

If you want to provide a default date range selection:

```tsx
{
  id: 'created_at',
  header: 'Created At',
  accessorKey: 'created_at',
  enableColumnFilter: true,
  meta: {
    filterVariant: 'date',
    dateFilterOptions: {
      defaultDateRange: {
        from: momentTz().subtract(7, 'days').toDate(),
        to: momentTz().toDate(),
      },
      formatTriggerText: 'DD/MM/YYYY',
    },
  },
}
```

### Multiple Date Range Filters

You can add multiple date range filters to different columns:

```tsx
const columns = [
  // ...other columns
  {
    id: 'created_at',
    header: 'Created Date',
    accessorKey: 'created_at',
    enableColumnFilter: true,
    meta: {
      filterVariant: 'date',
    },
  },
  {
    id: 'valid_from',
    header: 'Valid From',
    accessorKey: 'valid_from',
    enableColumnFilter: true,
    meta: {
      filterVariant: 'date',
    },
  },
];
```

## UI Features

The date range filter UI includes several enhancements:

- **Calendar Icon**: A small, unobtrusive calendar icon appears next to the column header
- **Blue Indication**: The icon turns blue with a light blue background when a filter is active
- **Hover Effects**: The icon has hover states for improved user feedback
- **Animation**: A subtle scale animation occurs when the icon is clicked
- **Tooltip**: Hovering over the icon shows the selected date range
- **Clear Option**: When a filter is active, a clear button allows easy removal of the filter

## Implementation Details

The date range filter is implemented with these key components:

1. **Icon-only Trigger**: A small calendar icon in the column header
2. **Popover**: Opens when the icon is clicked, containing the date picker
3. **Date Preset Detection**: Automatically detects if the selected range matches a preset (This Year, Last 30 Days, etc.)
4. **Search Parameter Integration**: Filter state is stored in URL parameters for persistence
5. **Tooltip Information**: Selected date range is shown on hover for quick reference

## Working with Presets

When a user selects a date preset like "This Year" or "Last 30 Days", the component remembers this selection even when the filter is reopened. This creates a consistent experience and avoids the issue of always defaulting back to the "Last 30 Days" option.
