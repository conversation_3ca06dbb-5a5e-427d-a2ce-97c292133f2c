import { BarBase, type BarBaseProps } from '@/components/base/BarBase';
import { BarChartBase } from '@/components/base/BarChartBase';
import { CartesianGridBase } from '@/components/base/CartesianGridBase';
import { LegendBase, type LegendBaseProps } from '@/components/base/LegendBase';
import { XAxisBase } from '@/components/base/XAxisBase';
import { YAxisBase } from '@/components/base/YAxisBase';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import type { XAxis, YAxis } from 'recharts';

export interface SimpleBarChartProps {
  chartConfig: ChartConfig;
  chartData: any[];
  barProps: BarBaseProps[];
  xAxis: XAxis;
  yAxis?: YAxis;
  legend?: Omit<LegendBaseProps, 'ref'>;
}

export const SimpleBarChart = ({
  chartConfig,
  chartData,
  barProps,
  xAxis,
  yAxis,
  legend,
  ...props
}: SimpleBarChartProps & React.ComponentProps<'div'>) => (
  <ChartContainer {...props} config={chartConfig}>
    <BarChartBase accessibilityLayer data={chartData}>
      <CartesianGridBase vertical={false} />
      <XAxisBase {...xAxis} />
      {yAxis ? <YAxisBase {...yAxis} /> : null}
      <ChartTooltip content={<ChartTooltipContent />} />
      {legend ? <LegendBase {...legend} /> : null}
      {barProps?.map(barProp => <BarBase {...barProp} />)}
    </BarChartBase>
  </ChartContainer>
);
