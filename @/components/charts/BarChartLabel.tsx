import { BarBase } from '@/components/base/BarBase';
import { BarChartBase } from '@/components/base/BarChartBase';
import { CartesianGridBase } from '@/components/base/CartesianGridBase';
import { XAxisBase } from '@/components/base/XAxisBase';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { LabelList } from 'recharts';
import { DataKey } from 'recharts/types/util/types';

export interface BarChartLabelProps {
  chartConfig: ChartConfig;
  // Todo replace type
  chartData: MustBeAny[];
  dataKey: DataKey<MustBeAny>;
  dataValue: DataKey<MustBeAny>;
  colorBar: string;
}

export const BarChartLabel = ({
  chartConfig,
  chartData,
  dataKey,
  dataValue,
  colorBar,
}: BarChartLabelProps) => {
  return (
    <ChartContainer config={chartConfig}>
      <BarChartBase accessibilityLayer data={chartData} margin={{ top: 20 }}>
        <CartesianGridBase vertical={false} />
        <XAxisBase
          dataKey={dataKey}
          tickLine={false}
          tickMargin={10}
          axisLine={false}
        />
        <ChartTooltip cursor={false} content={<ChartTooltipContent hidden />} />
        <BarBase dataKey={dataValue} fill={colorBar} radius={8}>
          <LabelList
            position="top"
            offset={12}
            className="fill-foreground"
            fontSize={12}
          />
        </BarBase>
      </BarChartBase>
    </ChartContainer>
  );
};
