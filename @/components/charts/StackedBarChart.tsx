import { BarBase, BarBaseProps } from '@/components/base/BarBase';
import { BarChartBase, BarChartProps } from '@/components/base/BarChartBase';
import { CartesianGridBase } from '@/components/base/CartesianGridBase';
import { XAxisBase } from '@/components/base/XAxisBase';
import { YAxisBase } from '@/components/base/YAxisBase';
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import type { XAxis, YAxis } from 'recharts';

export interface StackedBarChartProps {
  barChart: BarChartProps;
  chartConfig: ChartConfig;
  xAxis: XAxis;
  yAxis?: YAxis;
  isLegend?: boolean;
  barProps: BarBaseProps[];
}

export const StackedBarChart = ({
  barChart,
  chartConfig,
  barProps,
  xAxis,
  yAxis,
  isLegend = true,
  ...props
}: StackedBarChartProps & React.ComponentProps<'div'>) => {
  return (
    <ChartContainer {...props} config={chartConfig}>
      <BarChartBase accessibilityLayer {...barChart}>
        <CartesianGridBase vertical={false} />
        <XAxisBase {...xAxis} />
        {yAxis ? <YAxisBase {...yAxis} /> : null}
        <ChartTooltip content={<ChartTooltipContent />} />
        {isLegend ? <ChartLegend content={<ChartLegendContent />} /> : null}
        {barProps.length ? barProps.map(bar => <BarBase {...bar} />) : null}
      </BarChartBase>
    </ChartContainer>
  );
};
