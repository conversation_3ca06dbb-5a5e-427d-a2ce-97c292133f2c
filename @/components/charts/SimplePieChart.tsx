import { LegendBase, type <PERSON>B<PERSON>Props } from '@/components/base/LegendBase';
import { PieBase, type PieBaseProps } from '@/components/base/PieBase';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { PIE_CHART_COLOR } from 'btaskee-constants';
import { PieChart } from 'recharts';

export interface SimplePieChartProps {
  chartProps: Omit<PieBaseProps, 'data' | 'dataKey' | 'nameKey'> & {
    data: { label: string; value: number }[];
  };
  legend?: boolean | Omit<LegendBaseProps, 'ref'>;
}

export const SimplePieChart = ({
  chartProps,
  legend,
  ...props
}: SimplePieChartProps & React.ComponentProps<'div'>) => {
  const getChartConfig = (
    values: SimplePieChartProps['chartProps']['data'],
  ) => {
    const transformedArray = values.map((value, index) => [
      value.label.replace(/\s+/g, ''),
      { label: value.label, color: PIE_CHART_COLOR[index] },
    ]);

    const getChartConfig = transformedArray.reduce(
      // TODO replace type
      (acc: MustBeAny, [key, value]) => {
        acc[key as string] = value;

        return acc;
      },
      {},
    );

    return getChartConfig;
  };

  return (
    <ChartContainer {...props} config={getChartConfig(chartProps.data)}>
      <PieChart>
        <ChartTooltip content={<ChartTooltipContent />} />
        {typeof legend === 'boolean' && legend ? <LegendBase /> : null}
        {typeof legend === 'object' ? <LegendBase {...legend} /> : null}
        <PieBase
          {...{
            ...chartProps,
            data: chartProps.data?.map(data => ({
              label: data.label,
              value: data.value,
              fill: `var(--color-${data.label.replace(/\s+/g, '')})`,
            })),
          }}
          dataKey="value"
          nameKey="label"
        />
      </PieChart>
    </ChartContainer>
  );
};
