import { SVGProps } from 'react';

const VectorEmptyDataTable = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width="273"
    height="246"
    viewBox="0 0 273 246"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <g clipPath="url(#clip0_5283_58381)">
      <path
        d="M139.61 46.7955V184.795L153.4 207.225L102.26 210.345C102.26 210.345 92.8203 168.696 92.8203 167.646C92.8203 166.596 105.08 50.9955 105.08 50.9955L107.54 44.9955H139.41L139.61 46.7955Z"
        fill="#F5F5F5"
      />
      <path
        d="M30.1191 45.0455V161.631C30.1191 173.519 31.3555 186.082 39.7586 194.491C44.8584 199.595 52.4155 204.66 63.3992 207.225H144.319L126.003 191.888C118.515 185.618 114.189 176.353 114.189 166.587V44.9955L30.1191 45.0455Z"
        fill="#FAFAFA"
      />
      <path
        d="M212.26 113.345C212.26 113.345 220.06 91.0447 240.61 91.9847C240.61 91.9847 270.04 92.9848 266.18 120.525L272.49 113.345L261.2 83.8948L227.33 80.7748L209.93 92.9948L212.26 113.345Z"
        fill="#F5F5F5"
      />
      <path
        d="M272.45 103.364C267.23 78.6838 245.17 76.8738 244.79 76.8538C240.7 76.7658 236.61 77.01 232.56 77.5838H145.91V40.4838H7.20001C5.29045 40.4838 3.45909 41.2423 2.10883 42.5926C0.758564 43.9429 0 45.7742 0 47.6838V184.554C0 184.834 2.64 212.784 33.94 213.554H177.71C179.86 213.554 210.84 212.964 212.3 181.554V101.084C212.421 97.353 213.955 93.8074 216.59 91.1638C220.13 87.4638 225.73 85.0238 232.97 83.8938H233.97V83.7338C237.453 83.2646 240.967 83.0706 244.48 83.1537C245.19 83.2037 261.87 84.7238 266.19 104.354V163.994C266.193 164.83 266.527 165.631 267.119 166.222C267.711 166.812 268.514 167.144 269.35 167.144C270.185 167.144 270.987 166.812 271.577 166.221C272.168 165.63 272.5 164.829 272.5 163.994V103.994L272.45 103.364ZM34.03 207.224C9.20003 206.634 6.5 186.024 6.31 184.154V53.9938C6.30868 53.0487 6.49369 52.1127 6.85443 51.2392C7.21517 50.3657 7.74457 49.5719 8.41235 48.9032C9.08014 48.2345 9.87323 47.704 10.7462 47.342C11.6192 46.9801 12.555 46.7938 13.5 46.7938H139.61V182.244C139.61 182.464 139.83 198.154 153.4 207.244L34.03 207.224ZM212.03 86.8038C208.225 90.6639 206.073 95.854 206.03 101.274V181.414C204.83 207.414 178.86 207.214 177.7 207.224C146.5 207.224 145.96 183.224 145.95 182.224V83.8938H215.48C214.242 84.7584 213.084 85.7323 212.02 86.8038H212.03Z"
        fill="#F5F5F5"
      />
      <path
        d="M92.8196 245.165C136.478 245.165 171.87 243.213 171.87 240.805C171.87 238.397 136.478 236.445 92.8196 236.445C49.1615 236.445 13.7695 238.397 13.7695 240.805C13.7695 243.213 49.1615 245.165 92.8196 245.165Z"
        fill="#F5F5F5"
      />
      <path
        style={{ mixBlendMode: 'multiply' }}
        opacity="0.8"
        d="M42.5593 140.694C48.6951 140.694 53.6693 135.72 53.6693 129.584C53.6693 123.448 48.6951 118.474 42.5593 118.474C36.4234 118.474 31.4492 123.448 31.4492 129.584C31.4492 135.72 36.4234 140.694 42.5593 140.694Z"
        fill="url(#paint0_radial_5283_58381)"
      />
      <path
        style={{ mixBlendMode: 'multiply' }}
        opacity="0.8"
        d="M83.0007 140.694C89.1366 140.694 94.1107 135.72 94.1107 129.584C94.1107 123.448 89.1366 118.474 83.0007 118.474C76.8648 118.474 71.8906 123.448 71.8906 129.584C71.8906 135.72 76.8648 140.694 83.0007 140.694Z"
        fill="url(#paint1_radial_5283_58381)"
      />
      <path
        d="M62.8399 150.045C68.8709 150.045 73.7599 146.455 73.7599 142.025C73.7599 137.596 68.8709 134.005 62.8399 134.005C56.809 134.005 51.9199 137.596 51.9199 142.025C51.9199 146.455 56.809 150.045 62.8399 150.045Z"
        fill="#FAFAFA"
      />
      <path
        d="M35.3793 120.535C39.3447 120.535 42.5593 117.321 42.5593 113.355C42.5593 109.39 39.3447 106.175 35.3793 106.175C31.4139 106.175 28.1992 109.39 28.1992 113.355C28.1992 117.321 31.4139 120.535 35.3793 120.535Z"
        fill="#E5E5E5"
      />
      <path
        d="M85.8207 120.535C89.7861 120.535 93.0007 117.321 93.0007 113.355C93.0007 109.39 89.7861 106.175 85.8207 106.175C81.8553 106.175 78.6406 109.39 78.6406 113.355C78.6406 117.321 81.8553 120.535 85.8207 120.535Z"
        fill="#E5E5E5"
      />
      <path
        d="M74.1599 143.795H74.0999C73.7478 143.789 73.4091 143.659 73.1425 143.429C72.8759 143.199 72.6981 142.883 72.6398 142.535C72.2966 140.796 71.3642 139.228 69.9998 138.095C68.037 136.495 65.5822 135.622 63.0498 135.622C60.5175 135.622 58.0627 136.495 56.0999 138.095C54.7386 139.231 53.807 140.797 53.4598 142.535C53.4035 142.884 53.2262 143.201 52.9592 143.431C52.6922 143.662 52.3525 143.791 51.9998 143.795H51.9398C51.724 143.794 51.5111 143.746 51.3159 143.654C51.1207 143.562 50.948 143.429 50.8098 143.263C50.6717 143.097 50.5715 142.903 50.5162 142.695C50.4609 142.486 50.4519 142.268 50.4898 142.055C50.9482 139.616 52.2424 137.413 54.1499 135.826C56.6572 133.759 59.8054 132.628 63.0548 132.628C66.3042 132.628 69.4525 133.759 71.9598 135.826C73.861 137.417 75.151 139.619 75.6098 142.055C75.6477 142.268 75.6387 142.486 75.5834 142.695C75.5282 142.903 75.4279 143.097 75.2898 143.263C75.1517 143.429 74.979 143.562 74.7838 143.654C74.5886 143.746 74.3756 143.794 74.1599 143.795Z"
        fill="#E5E5E5"
      />
      <path
        d="M136.261 95.2353C162.328 95.2353 183.461 74.1031 183.461 48.0353C183.461 21.9675 162.328 0.835327 136.261 0.835327C110.193 0.835327 89.0605 21.9675 89.0605 48.0353C89.0605 74.1031 110.193 95.2353 136.261 95.2353Z"
        fill="#E5E5E5"
      />
      <path
        d="M136.289 71.3548C132.43 71.3548 129 70.433 125.999 68.5895C123.041 66.7459 120.704 64.0878 118.989 60.615C117.274 57.1423 116.417 52.9193 116.417 47.946C116.417 42.9727 117.274 38.7496 118.989 35.2769C120.704 31.8042 123.041 29.146 125.999 27.3025C129 25.4589 132.43 24.5371 136.289 24.5371C140.105 24.5371 143.492 25.4589 146.45 27.3025C149.451 29.146 151.809 31.8042 153.524 35.2769C155.239 38.7496 156.096 42.9727 156.096 47.946C156.096 52.9193 155.239 57.1423 153.524 60.615C151.809 64.0878 149.451 66.7459 146.45 68.5895C143.492 70.433 140.105 71.3548 136.289 71.3548ZM136.289 60.9366C137.661 60.9366 138.861 60.5079 139.89 59.6504C140.962 58.7929 141.798 57.421 142.398 55.5345C142.998 53.6052 143.299 51.0757 143.299 47.946C143.299 44.7733 142.998 42.2438 142.398 40.3574C141.798 38.471 140.962 37.099 139.89 36.2415C138.861 35.3841 137.661 34.9553 136.289 34.9553C134.917 34.9553 133.695 35.3841 132.623 36.2415C131.594 37.099 130.758 38.471 130.115 40.3574C129.515 42.2438 129.215 44.7733 129.215 47.946C129.215 51.0757 129.515 53.6052 130.115 55.5345C130.758 57.421 131.594 58.7929 132.623 59.6504C133.695 60.5079 134.917 60.9366 136.289 60.9366Z"
        fill="white"
      />
    </g>
    <defs>
      <radialGradient
        id="paint0_radial_5283_58381"
        cx="0"
        cy="0"
        r="1"
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(42.5592 129.584) scale(11.11 11.11)">
        <stop stopColor="#E5E5E5" />
        <stop offset="1" stopColor="white" stopOpacity="0" />
      </radialGradient>
      <radialGradient
        id="paint1_radial_5283_58381"
        cx="0"
        cy="0"
        r="1"
        gradientUnits="userSpaceOnUse"
        gradientTransform="translate(83.0006 129.584) scale(11.11 11.11)">
        <stop stopColor="#E5E5E5" />
        <stop offset="1" stopColor="white" stopOpacity="0" />
      </radialGradient>
      <clipPath id="clip0_5283_58381">
        <rect
          width="272.5"
          height="244.329"
          fill="white"
          transform="translate(0 0.835327)"
        />
      </clipPath>
    </defs>
  </svg>
);

export { VectorEmptyDataTable };