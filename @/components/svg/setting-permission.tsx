import type { SVGProps } from 'react';

const PermissionEditIcon = ({ props }: { props?: SVGProps<SVGSVGElement> }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    {...props}>
    <g clipPath="url(#clip0_9186_37917)">
      <path
        d="M6.41699 2.33398H2.33366C2.02424 2.33398 1.72749 2.4569 1.5087 2.67569C1.28991 2.89449 1.16699 3.19123 1.16699 3.50065V11.6673C1.16699 11.9767 1.28991 12.2735 1.5087 12.4923C1.72749 12.7111 2.02424 12.834 2.33366 12.834H10.5003C10.8097 12.834 11.1065 12.7111 11.3253 12.4923C11.5441 12.2735 11.667 11.9767 11.667 11.6673V7.58398"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.792 1.45814C11.0241 1.22608 11.3388 1.0957 11.667 1.0957C11.9952 1.0957 12.3099 1.22608 12.542 1.45814C12.7741 1.6902 12.9044 2.00495 12.9044 2.33314C12.9044 2.66133 12.7741 2.97608 12.542 3.20814L7.00033 8.74981L4.66699 9.33314L5.25033 6.99981L10.792 1.45814Z"
        stroke="black"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_9186_37917">
        <rect width="14" height="14" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export { PermissionEditIcon };
