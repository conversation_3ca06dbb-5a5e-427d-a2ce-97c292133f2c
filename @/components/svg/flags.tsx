const IconENFlag = () => {
  return (
    <svg
      width="20"
      height="21"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        d="M17.7778 3.49213H2.22222C1.63285 3.49213 1.06762 3.72625 0.650874 4.143C0.234126 4.55975 0 5.12498 0 5.71435L0 15.7143C0 16.3037 0.234126 16.8689 0.650874 17.2857C1.06762 17.7024 1.63285 17.9366 2.22222 17.9366H17.7778C18.3671 17.9366 18.9324 17.7024 19.3491 17.2857C19.7659 16.8689 20 16.3037 20 15.7143V5.71435C20 5.12498 19.7659 4.55975 19.3491 4.143C18.9324 3.72625 18.3671 3.49213 17.7778 3.49213Z"
        fill="#DA251D"
      />
      <path
        d="M10.9739 9.62381L10 6.62659L9.02611 9.62381H5.875L8.42444 11.4755L7.45056 14.4727L10 12.6205L12.5494 14.4727L11.5756 11.4755L14.125 9.62381H10.9739Z"
        fill="#FFFF00"
      />
      <g clipPath="url(#clip0_614_10824)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0 3.49213V18.4921H20V3.49213H0Z"
          fill="#2E42A5"
        />
        <mask
          id="mask0_614_10824"
          style={{ maskType: 'luminance' }}
          maskUnits="userSpaceOnUse"
          x="0"
          y="3"
          width="20"
          height="16">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M0 3.49213V18.4921H20V3.49213H0Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask0_614_10824)">
          <path
            d="M-2.22687 17.4202L2.17438 19.2821L20.0994 5.51587L22.4213 2.74962L17.715 2.12775L10.4038 8.05962L4.51876 12.0571L-2.22687 17.4202Z"
            fill="white"
          />
          <path
            d="M-1.625 18.7246L0.618125 19.8046L21.5875 2.49275H18.4394L-1.625 18.7246Z"
            fill="#F50100"
          />
          <path
            d="M22.2269 17.4202L17.8256 19.2821L-0.0993588 5.51587L-2.42123 2.74962L2.28502 2.12775L9.59627 8.05962L15.4813 12.0571L22.2269 17.4202Z"
            fill="white"
          />
          <path
            d="M22.0769 18.3565L19.8344 19.4365L10.9056 12.024L8.25814 11.1965L-2.64499 2.759H0.503761L11.4 10.9965L14.2944 11.989L22.0769 18.3565Z"
            fill="#F50100"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12.3613 2.24213H7.63877V8.49213H-1.23248V13.4921H7.63877V19.7421H12.3613V13.4921H21.2675V8.49213H12.3613V2.24213Z"
            fill="#F50100"
          />
          <mask
            id="mask1_614_10824"
            style={{ maskType: 'luminance' }}
            maskUnits="userSpaceOnUse"
            x="-2"
            y="2"
            width="24"
            height="18">
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M12.3613 2.24213H7.63877V8.49213H-1.23248V13.4921H7.63877V19.7421H12.3613V13.4921H21.2675V8.49213H12.3613V2.24213Z"
              fill="white"
            />
          </mask>
          <g mask="url(#mask1_614_10824)">
            <path
              d="M7.63877 2.24213V0.992126H6.38877V2.24213H7.63877ZM12.3613 2.24213H13.6113V0.992126H12.3613V2.24213ZM7.63877 8.49213V9.74213H8.88877V8.49213H7.63877ZM-1.23248 8.49213V7.24213H-2.48248V8.49213H-1.23248ZM-1.23248 13.4921H-2.48248V14.7421H-1.23248V13.4921ZM7.63877 13.4921H8.88877V12.2421H7.63877V13.4921ZM7.63877 19.7421H6.38877V20.9921H7.63877V19.7421ZM12.3613 19.7421V20.9921H13.6113V19.7421H12.3613ZM12.3613 13.4921V12.2421H11.1113V13.4921H12.3613ZM21.2675 13.4921V14.7421H22.5175V13.4921H21.2675ZM21.2675 8.49213H22.5175V7.24213H21.2675V8.49213ZM12.3613 8.49213H11.1113V9.74213H12.3613V8.49213ZM7.63877 3.49213H12.3613V0.992126H7.63877V3.49213ZM8.88877 8.49213V2.24213H6.38877V8.49213H8.88877ZM-1.23248 9.74213H7.63877V7.24213H-1.23248V9.74213ZM0.0175171 13.4921V8.49213H-2.48248V13.4921H0.0175171ZM7.63877 12.2421H-1.23248V14.7421H7.63877V12.2421ZM8.88877 19.7421V13.4921H6.38877V19.7421H8.88877ZM12.3613 18.4921H7.63877V20.9921H12.3613V18.4921ZM11.1113 13.4921V19.7421H13.6113V13.4921H11.1113ZM21.2675 12.2421H12.3613V14.7421H21.2675V12.2421ZM20.0175 8.49213V13.4921H22.5175V8.49213H20.0175ZM12.3613 9.74213H21.2675V7.24213H12.3613V9.74213ZM11.1113 2.24213V8.49213H13.6113V2.24213H11.1113Z"
              fill="white"
            />
          </g>
        </g>
      </g>
      <defs>
        <clipPath id="clip0_614_10824">
          <rect y="3.49213" width="20" height="15" rx="2.22222" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

const IconIDFlag = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        d="M17.7778 2.77783H2.22222C1.63285 2.77783 1.06762 3.01196 0.650874 3.42871C0.234126 3.84545 0 4.41068 0 5.00005L0 10.0001H20V5.00005C20 4.41068 19.7659 3.84545 19.3491 3.42871C18.9324 3.01196 18.3671 2.77783 17.7778 2.77783Z"
        fill="#DC1F26"
      />
      <path
        d="M20 15C20 15.5894 19.7659 16.1546 19.3491 16.5713C18.9324 16.9881 18.3671 17.2222 17.7778 17.2222H2.22222C1.63285 17.2222 1.06762 16.9881 0.650874 16.5713C0.234126 16.1546 0 15.5894 0 15V10H20V15Z"
        fill="#EEEEEE"
      />
    </svg>
  );
};

const IconKOFlag = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        d="M20 15.0001C20 15.5894 19.7659 16.1547 19.3491 16.5714C18.9324 16.9882 18.3671 17.2223 17.7778 17.2223H2.22222C1.63285 17.2223 1.06762 16.9882 0.650874 16.5714C0.234126 16.1547 0 15.5894 0 15.0001V5.00005C0 4.41068 0.234126 3.84545 0.650874 3.42871C1.06762 3.01196 1.63285 2.77783 2.22222 2.77783H17.7778C18.3671 2.77783 18.9324 3.01196 19.3491 3.42871C19.7659 3.84545 20 4.41068 20 5.00005V15.0001Z"
        fill="#EEEEEE"
      />
      <path
        d="M11.9115 7.26964C11.1873 6.76266 10.2914 6.56412 9.4208 6.71769C8.55022 6.87126 7.7763 7.36436 7.26928 8.08853C7.02986 8.45096 6.94135 8.89253 7.02258 9.31924C7.10381 9.74595 7.34837 10.1241 7.70421 10.3732C8.06005 10.6224 8.49906 10.7228 8.92781 10.6531C9.35656 10.5834 9.74119 10.3492 9.99984 10.0002C10.1254 9.8209 10.285 9.6681 10.4696 9.5505C10.6543 9.43291 10.8602 9.35283 11.0758 9.31483C11.2913 9.27684 11.5123 9.28167 11.726 9.32906C11.9397 9.37645 12.1419 9.46547 12.3212 9.59103C12.5005 9.71658 12.6533 9.87623 12.7709 10.0608C12.8885 10.2455 12.9686 10.4514 13.0066 10.667C13.0446 10.8825 13.0397 11.1035 12.9924 11.3172C12.945 11.5309 12.8559 11.7331 12.7304 11.9124C12.9815 11.5538 13.1595 11.1492 13.2543 10.7218C13.349 10.2944 13.3587 9.85254 13.2826 9.42141C13.2066 8.99028 13.0463 8.57835 12.8111 8.20914C12.5758 7.83994 12.2702 7.52069 11.9115 7.26964Z"
        fill="#C60C30"
      />
      <path
        d="M12.3209 9.59122C11.9589 9.33768 11.511 9.23832 11.0757 9.315C10.6404 9.39168 10.2534 9.63812 9.99983 10.0001C9.74181 10.3509 9.35678 10.5869 8.92707 10.6575C8.49736 10.7281 8.05708 10.6277 7.70038 10.3779C7.34368 10.1281 7.09887 9.7487 7.01831 9.32074C6.93774 8.89279 7.02782 8.4503 7.26927 8.08789C7.01083 8.4461 6.82616 8.85211 6.726 9.28231C6.62583 9.71251 6.61217 10.1583 6.6858 10.5939C6.75943 11.0294 6.91889 11.4459 7.15491 11.8193C7.39093 12.1927 7.6988 12.5154 8.06064 12.7687C8.42247 13.0221 8.83105 13.201 9.26262 13.2951C9.6942 13.3891 10.1402 13.3965 10.5746 13.3167C11.0091 13.2369 11.4233 13.0716 11.7933 12.8303C12.1633 12.589 12.4816 12.2766 12.7298 11.9112C12.8554 11.7321 12.9445 11.5299 12.9919 11.3164C13.0393 11.1028 13.0442 10.8819 13.0062 10.6665C12.9682 10.451 12.8881 10.2452 12.7706 10.0607C12.653 9.87616 12.5002 9.71664 12.3209 9.59122Z"
        fill="#003478"
      />
      <path
        d="M13.5187 14.2065L14.5898 12.9298L15.0153 13.287L13.9442 14.5637L13.5187 14.2065ZM14.9464 12.5048L16.0175 11.2287L16.4431 11.5859L15.372 12.862L14.9464 12.5048ZM14.3698 14.922L15.4409 13.6459L15.8664 14.0031L14.7953 15.2793L14.3698 14.922ZM15.7987 13.2187L16.8709 11.9426L17.2964 12.3004L16.2242 13.5759L15.7987 13.2187ZM15.2203 15.6365L16.2925 14.3604L16.7175 14.7176L15.6459 15.9937L15.2203 15.6365ZM16.6487 13.9337L17.7192 12.6559L18.1453 13.0131L17.0748 14.2909L16.6487 13.9337ZM16.6509 6.06593L17.0764 5.7087L18.1487 6.98593L17.7225 7.34315L16.6509 6.06593ZM15.222 4.36259L15.6475 4.00537L16.7186 5.28259L16.2925 5.63926L15.222 4.36259ZM14.3703 5.07815L14.7959 4.72093L17.2959 7.69982L16.8703 8.05704L14.3703 5.07815ZM13.5192 5.79148L13.9453 5.43426L15.0159 6.71093L14.5903 7.06759L13.5192 5.79148ZM14.947 7.49426L15.3725 7.13704L16.4448 8.41315L16.0198 8.77037L14.947 7.49426ZM3.55532 11.5854L3.98087 11.2281L6.48032 14.2076L6.05421 14.5648L3.55532 11.5854ZM2.70532 12.2998L3.13032 11.9431L4.20198 13.2198L3.77587 13.577L2.70532 12.2998ZM4.13143 14.0031L4.55698 13.6459L5.62921 14.922L5.20365 15.2793L4.13143 14.0031ZM1.85309 13.0137L2.27865 12.6565L4.77865 15.6354L4.35254 15.9926L1.85309 13.0137ZM1.85254 6.98482L4.35143 4.00593L4.77754 4.36259L2.27754 7.34204L1.85254 6.98482ZM2.70365 7.69982L5.20365 4.72037L5.62921 5.07759L3.12921 8.05648L2.70365 7.69982ZM3.55532 8.4137L6.05532 5.43426L6.48087 5.79148L3.98087 8.77093L3.55532 8.4137Z"
        fill="#292F33"
      />
    </svg>
  );
};

const IconTHFlag = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        d="M0 14.7324V15.0002C0 15.5896 0.234126 16.1548 0.650874 16.5715C1.06762 16.9883 1.63285 17.2224 2.22222 17.2224H17.7778C18.3671 17.2224 18.9324 16.9883 19.3491 16.5715C19.7659 16.1548 20 15.5896 20 15.0002V14.7324H0Z"
        fill="#A7122D"
      />
      <path d="M0 12.3228H20V14.8144H0V12.3228Z" fill="#EEEEEE" />
      <path d="M0 7.50732H20V12.4079H0V7.50732Z" fill="#292648" />
      <path d="M0 5.10059H20V7.59225H0V5.10059Z" fill="#EEEEEE" />
      <path
        d="M0 5.18505V5.00005C0 4.41068 0.234126 3.84545 0.650874 3.42871C1.06762 3.01196 1.63285 2.77783 2.22222 2.77783H17.7778C18.3671 2.77783 18.9324 3.01196 19.3491 3.42871C19.7659 3.84545 20 4.41068 20 5.00005V5.18505H0Z"
        fill="#A7122D"
      />
    </svg>
  );
};

const IconVNFlag = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        d="M17.7778 2.77783H2.22222C1.63285 2.77783 1.06762 3.01196 0.650874 3.42871C0.234126 3.84545 0 4.41068 0 5.00005L0 15.0001C0 15.5894 0.234126 16.1547 0.650874 16.5714C1.06762 16.9882 1.63285 17.2223 2.22222 17.2223H17.7778C18.3671 17.2223 18.9324 16.9882 19.3491 16.5714C19.7659 16.1547 20 15.5894 20 15.0001V5.00005C20 4.41068 19.7659 3.84545 19.3491 3.42871C18.9324 3.01196 18.3671 2.77783 17.7778 2.77783Z"
        fill="#DA251D"
      />
      <path
        d="M10.9739 8.90933L10 5.91211L9.02611 8.90933H5.875L8.42444 10.761L7.45056 13.7582L10 11.906L12.5494 13.7582L11.5756 10.761L14.125 8.90933H10.9739Z"
        fill="#FFFF00"
      />
    </svg>
  );
};

const IconMYFlag = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <path
        d="M20 15.0001C20 15.5894 19.7659 16.1546 19.3491 16.5714C18.9324 16.9882 18.3672 17.2223 17.7777 17.2223H2.22223C1.63285 17.2223 1.06762 16.9882 0.650873 16.5714C0.234127 16.1546 0 15.5894 0 15.0001V5.00005C0 4.41067 0.234127 3.84545 0.650873 3.4287C1.06762 3.01195 1.63285 2.77783 2.22223 2.77783H17.7777C18.3672 2.77783 18.9324 3.01195 19.3491 3.4287C19.7659 3.84545 20 4.41067 20 5.00005V15.0001Z"
        fill="#DD2E44"
      />
      <path
        d="M0.308333 16.1111H19.6923C19.8915 15.7746 19.9977 15.3911 20 15H0C0 15.4067 0.117222 15.7828 0.308333 16.1111ZM0 12.7778H20V13.889H0V12.7778ZM0 10.5556H20V11.6667H0V10.5556ZM0 8.33337H20V9.44447H0V8.33337ZM0 6.11114H20V7.22225H0V6.11114ZM0.308333 3.88892C0.108595 4.22529 0.0021612 4.60884 0 5.00004H20C20 4.59337 19.8828 4.2167 19.6916 3.88892H0.308333Z"
        fill="#EEEEEE"
      />
      <path
        d="M10 2.77783H2.22223C1.63285 2.77783 1.06762 3.01195 0.650873 3.4287C0.234127 3.84545 0 4.41067 0 5.00005V10.5556H10V2.77783Z"
        fill="#010066"
      />
      <path
        d="M5.2964 9.57381C4.5352 9.559 3.81016 9.24622 3.27704 8.70269C2.74393 8.15916 2.44525 7.4282 2.44518 6.66685C2.4451 5.90551 2.74364 5.17451 3.27665 4.63087C3.80966 4.08723 4.53464 3.77432 5.29584 3.75936C5.69306 3.75936 6.07196 3.84048 6.41696 3.98436C5.84624 3.56131 5.15458 3.33304 4.44417 3.33325C3.56012 3.33325 2.71228 3.68444 2.08716 4.30956C1.46204 4.93468 1.11084 5.78253 1.11084 6.66659C1.11084 7.55064 1.46204 8.39849 2.08716 9.02361C2.71228 9.64873 3.56012 9.99992 4.44417 9.99992C5.15458 10.0001 5.84624 9.77185 6.41696 9.34881C6.06214 9.49768 5.68117 9.57418 5.2964 9.57381Z"
        fill="#FFCC4D"
      />
      <path
        d="M7.17872 4.90503L7.36428 5.80503L7.9215 5.07447L7.69817 5.96559L8.51761 5.54947L7.92928 6.25559L8.84817 6.23614L8.01205 6.61726L8.84817 6.99836L7.92928 6.97892L8.51761 7.68503L7.69817 7.26892L7.9215 8.16059L7.36428 7.42947L7.17872 8.33003L6.99317 7.42947L6.43594 8.16059L6.65928 7.26892L5.83984 7.68503L6.42761 6.97892L5.50928 6.99836L6.34538 6.61726L5.50928 6.23614L6.42761 6.25559L5.83984 5.54947L6.65928 5.96559L6.43594 5.07447L6.99317 5.80503L7.17872 4.90503Z"
        fill="#FFCC4D"
      />
    </svg>
  );
};

export {
  IconENFlag,
  IconIDFlag,
  IconKOFlag,
  IconMYFlag,
  IconTHFlag,
  IconVNFlag,
};
