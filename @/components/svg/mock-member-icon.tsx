const MockMemberIcon = () => (
  <svg
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_4489_3674)">
      <circle cx="14" cy="14" r="12.8333" fill="#01D6FF" />
      <circle
        cx="14"
        cy="14"
        r="12.8333"
        fill="url(#paint0_linear_4489_3674)"
      />
      <circle
        cx="14"
        cy="14"
        r="12.8333"
        fill="url(#paint1_linear_4489_3674)"
      />
      <circle
        cx="14"
        cy="14"
        r="12.8333"
        stroke="white"
        strokeWidth="2.33333"
      />
      <path
        d="M14.0001 5.59961L15.6521 10.2279C16.005 11.2166 16.7831 11.9947 17.7718 12.3476L22.4001 13.9996L17.7718 15.6516C16.7831 16.0045 16.005 16.7826 15.6521 17.7713L14.0001 22.3996L12.3481 17.7713C11.9952 16.7826 11.2171 16.0045 10.2284 15.6516L5.6001 13.9996L10.2284 12.3476C11.2171 11.9947 11.9952 11.2166 12.3481 10.2279L14.0001 5.59961Z"
        fill="white"
      />
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_4489_3674"
        x1="28"
        y1="6.01872e-06"
        x2="-3.71809"
        y2="5.16432"
        gradientUnits="userSpaceOnUse">
        <stop offset="0.253028" stopColor="#00D0FF" />
        <stop offset="0.858273" stopColor="#B5F0FD" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_4489_3674"
        x1="27.2875"
        y1="-2.33828e-06"
        x2="-1.66492"
        y2="2.25282"
        gradientUnits="userSpaceOnUse">
        <stop stopColor="#FDBD24" />
        <stop offset="1" stopColor="white" />
      </linearGradient>
      <clipPath id="clip0_4489_3674">
        <rect width="28" height="28" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export { MockMemberIcon };
