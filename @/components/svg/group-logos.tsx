import { SVGProps } from 'react';

const GraphLogo = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width="44"
      height="44"
      viewBox="0 0 44 44"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <rect width="44" height="44" rx="6.6" fill="#FFF7ED" />
      <path
        d="M20.1528 15.5563C20.2037 15.66 20.2373 15.771 20.2524 15.8851L20.5308 20.025V20.025L20.669 22.1059C20.6705 22.3199 20.704 22.5325 20.7687 22.7368C20.9356 23.1333 21.3372 23.3854 21.7741 23.3678L28.4313 22.9323C28.7196 22.9276 28.998 23.0354 29.2052 23.2321C29.3779 23.396 29.4894 23.6104 29.5246 23.841L29.5364 23.981C29.2609 27.7957 26.4592 30.9774 22.6524 31.7988C18.8456 32.6201 14.9419 30.8851 13.0607 27.5357C12.5184 26.5626 12.1797 25.493 12.0644 24.3898C12.0162 24.0632 11.995 23.7333 12.001 23.4033C11.995 19.3135 14.9075 15.7778 18.9843 14.9254C19.475 14.849 19.956 15.1087 20.1528 15.5563Z"
        fill="#F97316"
      />
      <path
        opacity="0.4"
        d="M22.8686 12.0008C27.4285 12.1168 31.2609 15.3958 31.9986 19.8123L31.9916 19.8449V19.8449L31.9714 19.8923L31.9742 20.0224C31.9638 20.1947 31.8972 20.3605 31.7826 20.4945C31.6631 20.634 31.4999 20.729 31.3202 20.7659L31.2106 20.7809L23.5298 21.2786C23.2743 21.3038 23.0199 21.2214 22.8299 21.052C22.6716 20.9107 22.5704 20.7201 22.5418 20.5147L22.0263 12.8451C22.0173 12.8191 22.0173 12.791 22.0263 12.7651C22.0333 12.5537 22.1264 12.3538 22.2846 12.2102C22.4429 12.0666 22.6532 11.9912 22.8686 12.0008Z"
        fill="#FDBA74"
      />
    </svg>
  );
};

const SendLogo = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width="44"
      height="44"
      viewBox="0 0 44 44"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <rect width="44" height="44" rx="6.6" fill="#FEFCE8" />
      <path
        d="M31.4274 12.5783C30.9274 12.0673 30.1874 11.8783 29.4974 12.0783L13.4074 16.7273C12.6794 16.9293 12.1634 17.5063 12.0244 18.2383C11.8824 18.9843 12.3784 19.9323 13.0264 20.3283L18.0574 23.4003C18.5734 23.7163 19.2394 23.6373 19.6664 23.2093L25.4274 17.4483C25.7174 17.1473 26.1974 17.1473 26.4874 17.4483C26.7774 17.7373 26.7774 18.2083 26.4874 18.5083L20.7164 24.2693C20.2884 24.6973 20.2084 25.3613 20.5234 25.8783L23.5974 30.9283C23.9574 31.5273 24.5774 31.8683 25.2574 31.8683C25.3374 31.8683 25.4274 31.8683 25.5074 31.8573C26.2874 31.7583 26.9074 31.2273 27.1374 30.4773L31.9074 14.5083C32.1174 13.8283 31.9274 13.0883 31.4274 12.5783Z"
        fill="#EAB308"
      />
      <path
        opacity="0.4"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.0095 26.8084C12.8175 26.8084 12.6255 26.7354 12.4795 26.5884C12.1865 26.2954 12.1865 25.8214 12.4795 25.5284L13.8445 24.1624C14.1375 23.8704 14.6125 23.8704 14.9055 24.1624C15.1975 24.4554 15.1975 24.9304 14.9055 25.2234L13.5395 26.5884C13.3935 26.7354 13.2015 26.8084 13.0095 26.8084ZM16.7714 28.0007C16.5794 28.0007 16.3874 27.9277 16.2414 27.7807C15.9484 27.4877 15.9484 27.0137 16.2414 26.7207L17.6064 25.3547C17.8994 25.0627 18.3744 25.0627 18.6674 25.3547C18.9594 25.6477 18.9594 26.1227 18.6674 26.4157L17.3014 27.7807C17.1554 27.9277 16.9634 28.0007 16.7714 28.0007ZM17.0235 31.5684C17.1695 31.7154 17.3615 31.7884 17.5535 31.7884C17.7455 31.7884 17.9375 31.7154 18.0835 31.5684L19.4495 30.2034C19.7415 29.9104 19.7415 29.4354 19.4495 29.1424C19.1565 28.8504 18.6815 28.8504 18.3885 29.1424L17.0235 30.5084C16.7305 30.8014 16.7305 31.2754 17.0235 31.5684Z"
        fill="#F1CC5A"
      />
    </svg>
  );
};

const ActivityLogo = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width="44"
      height="44"
      viewBox="0 0 44 44"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <rect width="44" height="44" rx="6.6" fill="#F0FDF4" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M25.2447 14.7376C25.2447 16.9586 27.0479 18.759 29.2721 18.759C29.517 18.7578 29.7613 18.7343 30.002 18.6888V26.6615C30.002 30.0156 28.0234 32 24.6644 32H17.3483C13.9805 32 12.002 30.0156 12.002 26.6615V19.3561C12.002 16.002 13.9805 14 17.3483 14H25.3151C25.2678 14.243 25.2443 14.49 25.2447 14.7376ZM23.1525 24.8966L26.0104 21.2088V21.1912C26.255 20.8625 26.1926 20.3989 25.8697 20.1463C25.7134 20.0257 25.5148 19.9735 25.3192 20.0016C25.1237 20.0297 24.9479 20.1358 24.8321 20.2956L22.4227 23.3951L19.6792 21.2351C19.5225 21.1131 19.3233 21.0592 19.1264 21.0857C18.9295 21.1121 18.7516 21.2166 18.6328 21.3756L15.6782 25.1863C15.5743 25.3158 15.5184 25.4771 15.5199 25.6429C15.5028 25.9781 15.7144 26.2826 16.0349 26.3838C16.3555 26.485 16.704 26.3573 16.8829 26.0732L19.3538 22.8771L22.0973 25.0283C22.2534 25.1541 22.454 25.2111 22.653 25.1863C22.852 25.1615 23.0323 25.0569 23.1525 24.8966Z"
        fill="#22C55E"
      />
      <ellipse
        opacity="0.4"
        cx="29.502"
        cy="14.5"
        rx="2.5"
        ry="2.5"
        fill="#6BD893"
      />
    </svg>
  );
};

const StarLogo = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width="44"
      height="44"
      viewBox="0 0 44 44"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <rect width="44" height="44" rx="6.6" fill="#EFF6FF" />
      <path
        opacity="0.4"
        d="M22.9763 13.1136L25.2028 17.5879C25.3668 17.9121 25.6799 18.1372 26.041 18.1872L31.042 18.9156C31.3341 18.9566 31.5992 19.1107 31.7782 19.3458C31.9552 19.5779 32.0312 19.8721 31.9882 20.1612C31.9532 20.4013 31.8402 20.6234 31.6672 20.7935L28.0434 24.3063C27.7783 24.5514 27.6583 24.9146 27.7223 25.2698L28.6145 30.2083C28.7095 30.8046 28.3144 31.3669 27.7223 31.48C27.4783 31.519 27.2282 31.478 27.0082 31.3659L22.5472 29.0417C22.2161 28.8746 21.8251 28.8746 21.494 29.0417L17.033 31.3659C16.4849 31.657 15.8058 31.4589 15.5007 30.9187C15.3877 30.7036 15.3477 30.4584 15.3847 30.2193L16.2769 25.2798C16.3409 24.9256 16.2199 24.5604 15.9558 24.3153L12.332 20.8045C11.9009 20.3883 11.8879 19.703 12.303 19.2717C12.312 19.2627 12.322 19.2527 12.332 19.2427C12.5041 19.0676 12.7301 18.9566 12.9742 18.9276L17.9752 18.1982C18.3353 18.1472 18.6484 17.9241 18.8134 17.5979L20.9599 13.1136C21.1509 12.7294 21.547 12.4903 21.9771 12.5003H22.1111C22.4842 12.5453 22.8093 12.7764 22.9763 13.1136Z"
        fill="#7CABF9"
      />
      <path
        d="M21.992 28.9171C21.7983 28.9231 21.6096 28.9752 21.4399 29.0682L17.0007 31.3871C16.4576 31.6464 15.8076 31.4452 15.503 30.9258C15.3902 30.7136 15.3493 30.4704 15.3872 30.2322L16.2738 25.3032C16.3338 24.9449 16.2139 24.5806 15.9533 24.3284L12.3279 20.8185C11.8976 20.3971 11.8896 19.7056 12.311 19.2742C12.317 19.2682 12.3219 19.2632 12.3279 19.2582C12.4997 19.0881 12.7213 18.976 12.96 18.9409L17.9652 18.2043C18.3277 18.1583 18.6422 17.9321 18.8019 17.6038L20.9776 13.0631C21.1843 12.6968 21.5806 12.4786 22 12.5017C21.992 12.7989 21.992 28.715 21.992 28.9171Z"
        fill="#3B82F6"
      />
    </svg>
  );
};

const HeartLogo = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width="44"
      height="44"
      viewBox="0 0 44 44"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <rect width="44" height="44" rx="6.6" fill="#FEF2F2" />
      <path
        opacity="0.4"
        d="M21.7757 31.8374C19.4926 30.4273 17.3703 28.7645 15.4476 26.8796C14.0902 25.5338 13.0536 23.8905 12.4169 22.0753C11.2792 18.5352 12.6035 14.4895 16.3008 13.2884C18.2523 12.6755 20.3747 13.0518 22.0067 14.2998C23.6393 13.0532 25.7611 12.677 27.7127 13.2884C31.41 14.4895 32.7431 18.5352 31.6055 22.0753C30.974 23.8888 29.9435 25.5319 28.5926 26.8796C26.6681 28.7625 24.546 30.4251 22.2645 31.8374L22.0156 32L21.7757 31.8374Z"
        fill="#F8A9A9"
      />
      <path
        d="M22.0109 31.9996L21.776 31.8371C19.4901 30.427 17.3649 28.7643 15.439 26.8792C14.0752 25.5352 13.0324 23.8918 12.3905 22.0749C11.2618 18.5348 12.586 14.4891 16.2834 13.288C18.2349 12.6751 20.3853 13.0516 22.0109 14.3102V31.9996Z"
        fill="#EF4445"
      />
      <path
        d="M28.2304 19.9992C28.0296 19.9863 27.8425 19.8859 27.7131 19.7216C27.5836 19.5572 27.5232 19.3434 27.5459 19.1302C27.5677 18.4278 27.168 17.7885 26.5517 17.5398C26.1609 17.4331 25.9243 17.0099 26.022 16.5925C26.1148 16.1818 26.4993 15.9265 26.8858 16.0189C26.9346 16.027 26.9816 16.0447 27.0244 16.071C28.2601 16.5466 29.0601 17.8264 28.9965 19.2258C28.9944 19.4379 28.9117 19.64 28.7673 19.7858C28.6229 19.9316 28.4291 20.0087 28.2304 19.9992Z"
        fill="#EF4445"
      />
    </svg>
  );
};

export { ActivityLogo, GraphLogo, HeartLogo, SendLogo, StarLogo };
