import { SVGProps } from 'react';

export const HeartIcon = ({ props }: { props?: SVGProps<SVGSVGElement> }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M6.24935 2.49228C3.71802 2.49228 1.66602 4.54395 1.66602 7.07562C1.66602 9.72896 3.17727 12.2323 5.67643 14.524C6.52943 15.3056 7.44785 15.9915 8.35868 16.6073C8.67793 16.8223 8.97652 17.0173 9.24418 17.1798C9.40735 17.279 9.51493 17.3506 9.58268 17.3881C9.83435 17.5281 10.1644 17.5281 10.416 17.3881C10.4838 17.3506 10.5914 17.279 10.7545 17.1798C11.0222 17.0173 11.3208 16.8223 11.64 16.6073C12.5509 15.9915 13.4693 15.3056 14.3223 14.524C16.8214 12.2323 18.3327 9.72896 18.3327 7.07562C18.3327 4.54395 16.2807 2.49228 13.7494 2.49228C12.3117 2.49228 10.908 3.27562 10.0254 4.41896C9.16468 3.25562 7.76593 2.49228 6.24935 2.49228ZM6.24935 4.15895C7.26093 4.15895 8.2056 4.67229 8.74935 5.46063C8.80218 5.5373 9.21877 6.14979 9.29618 6.26813C9.6186 6.76229 10.3379 6.7773 10.6764 6.29396C10.7588 6.17646 11.1924 5.56645 11.2494 5.48728C11.8169 4.69395 12.8104 4.15895 13.7494 4.15895C15.3602 4.15895 16.666 5.46479 16.666 7.07562C16.666 9.16146 15.3648 11.2673 13.1764 13.274C12.3888 13.9956 11.5494 14.654 10.7025 15.2265C10.4464 15.3998 10.218 15.5348 9.99935 15.669C9.78068 15.5348 9.55227 15.3998 9.29618 15.2265C8.44927 14.654 7.60993 13.9956 6.82227 13.274C4.63393 11.2673 3.33268 9.16146 3.33268 7.07562C3.33268 5.46479 4.63852 4.15895 6.24935 4.15895Z"
      fill="#383838"
    />
  </svg>
);

export const HideCommentIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M2.34178 2.34177L1.15845 3.51677L3.05011 5.40844C2.14452 6.76826 1.66307 8.36634 1.66678 10.0001C1.66678 14.6001 5.40011 18.3334 10.0001 18.3334C11.7001 18.3334 13.2751 17.8251 14.5918 16.9501L16.4834 18.8418L17.6584 17.6668L2.34178 2.34177ZM10.0001 16.6668C6.32511 16.6668 3.33345 13.6751 3.33345 10.0001C3.33345 8.76677 3.67511 7.61677 4.26678 6.61677L13.3834 15.7334C12.3834 16.3251 11.2334 16.6668 10.0001 16.6668ZM6.61678 4.26677L5.40845 3.0501C6.76827 2.14451 8.36635 1.66306 10.0001 1.66677C14.6001 1.66677 18.3334 5.4001 18.3334 10.0001C18.3334 11.7001 17.8251 13.2751 16.9501 14.5918L15.7334 13.3751C16.3447 12.3555 16.6673 11.1889 16.6668 10.0001C16.6668 6.3251 13.6751 3.33344 10.0001 3.33344C8.76678 3.33344 7.61678 3.6751 6.61678 4.26677Z"
      fill="#525252"
    />
  </svg>
);

export const UnHideCommentIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <g clipPath="url(#clip0_8870_99418)">
      <path
        d="M10.0001 18.3334C14.6025 18.3334 18.3334 14.6025 18.3334 10.0001C18.3334 5.39771 14.6025 1.66675 10.0001 1.66675C5.39771 1.66675 1.66675 5.39771 1.66675 10.0001C1.66675 14.6025 5.39771 18.3334 10.0001 18.3334Z"
        stroke="#525252"
        stroke-width="1.66667"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8870_99418">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ReplyIcon = ({ props }: { props?: SVGProps<SVGSVGElement> }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M8.08656 7.25502C8.16615 7.17815 8.22964 7.08619 8.27331 6.98452C8.31699 6.88285 8.33997 6.7735 8.34094 6.66285C8.3419 6.55221 8.32081 6.44247 8.27891 6.34006C8.23701 6.23765 8.17513 6.1446 8.09689 6.06636C8.01865 5.98811 7.9256 5.92624 7.82319 5.88434C7.72078 5.84243 7.61104 5.82135 7.50039 5.82231C7.38974 5.82327 7.28039 5.84626 7.17872 5.88994C7.07705 5.93361 6.9851 5.99709 6.90823 6.07669L2.74406 10.2467C2.58809 10.4029 2.50049 10.6147 2.50049 10.8354C2.50049 11.0562 2.58809 11.2679 2.74406 11.4242L6.90823 15.5892C6.9856 15.6666 7.07746 15.728 7.17857 15.77C7.27968 15.8119 7.38806 15.8335 7.49752 15.8335C7.60697 15.8336 7.71537 15.812 7.81651 15.7702C7.91765 15.7283 8.00955 15.667 8.08698 15.5896C8.1644 15.5122 8.22583 15.4204 8.26775 15.3193C8.30968 15.2181 8.33127 15.1098 8.33131 15.0003C8.33135 14.8909 8.30983 14.7825 8.26798 14.6813C8.22613 14.5802 8.16476 14.4883 8.08739 14.4109L5.34156 11.6667H10.8332C12.5653 11.6667 14.2293 10.9926 15.473 9.78713C16.7167 8.58169 17.4424 6.93953 17.4966 5.20835L17.4999 5.00002C17.4999 4.77901 17.4121 4.56705 17.2558 4.41076C17.0995 4.25448 16.8876 4.16669 16.6666 4.16669C16.4455 4.16669 16.2336 4.25448 16.0773 4.41076C15.921 4.56705 15.8332 4.77901 15.8332 5.00002C15.8333 6.29367 15.3319 7.53699 14.4345 8.46875C13.5371 9.40051 12.3135 9.94818 11.0207 9.99669L10.8332 10H5.34489L8.08656 7.25502Z"
      fill="#383838"
    />
  </svg>
);

export const MessageIcon = ({ props }: { props?: SVGProps<SVGSVGElement> }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M10.012 2.47321C5.43013 2.47321 1.67871 5.80737 1.67871 9.9732C1.67871 11.2832 2.04696 12.5565 2.74638 13.6707C3.12221 14.2699 3.15721 15.1224 2.59021 16.2749C2.33279 16.7982 2.68671 17.4182 3.26721 17.4732C4.39796 17.5807 5.35304 17.3907 6.65271 16.8482C7.68804 17.2515 8.85829 17.4732 10.012 17.4732C14.594 17.4732 18.3454 14.1382 18.3454 9.9732C18.3454 5.80737 14.594 2.47321 10.012 2.47321ZM10.012 4.13987C13.7144 4.13987 16.6787 6.77487 16.6787 9.9732C16.6787 13.1715 13.7144 15.8065 10.012 15.8065C8.94654 15.8065 7.91813 15.5949 6.99121 15.1815C6.77679 15.0857 6.52913 15.0874 6.31413 15.1815C5.54721 15.5174 5.11279 15.7007 4.54329 15.7807C4.85896 14.6932 4.62713 13.5415 4.15271 12.7857C3.61754 11.9332 3.34538 10.9649 3.34538 9.9732C3.34538 6.77487 6.30971 4.13987 10.012 4.13987Z"
      fill="#383838"
    />
  </svg>
);

export const ShareIcon = ({ props }: { props?: SVGProps<SVGSVGElement> }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M14.1649 1.62762C12.784 1.62762 11.6649 2.74679 11.6649 4.12762C11.6649 4.34179 11.699 4.60846 11.7499 4.80679L7.28904 7.92012C6.88987 7.65012 6.34904 7.46096 5.83154 7.46096C4.45071 7.46096 3.33154 8.58012 3.33154 9.96096C3.33154 11.3418 4.45071 12.461 5.83154 12.461C6.35821 12.461 6.8782 12.2651 7.28153 11.9876L11.7515 15.126C11.6965 15.3318 11.6649 15.571 11.6649 15.7943C11.6649 17.1751 12.784 18.2943 14.1649 18.2943C15.5457 18.2943 16.6649 17.1751 16.6649 15.7943C16.6649 14.4135 15.5457 13.2943 14.1649 13.2943C13.6382 13.2943 13.1249 13.4843 12.7224 13.7626L8.2407 10.6268C8.29487 10.421 8.33154 10.1843 8.33154 9.96096C8.33154 9.73762 8.29987 9.4968 8.24487 9.29097L12.7157 6.16429C13.1149 6.43346 13.6474 6.62762 14.1649 6.62762C15.5457 6.62762 16.6649 5.50846 16.6649 4.12762C16.6649 2.74679 15.5457 1.62762 14.1649 1.62762ZM14.1649 3.29429C14.6249 3.29429 14.9982 3.66762 14.9982 4.12762C14.9982 4.58762 14.6249 4.96096 14.1649 4.96096C13.7049 4.96096 13.3315 4.58762 13.3315 4.12762C13.3315 3.66762 13.7049 3.29429 14.1649 3.29429ZM5.83154 9.12762C6.29154 9.12762 6.66488 9.50096 6.66488 9.96096C6.66488 10.421 6.29154 10.7943 5.83154 10.7943C5.37154 10.7943 4.99821 10.421 4.99821 9.96096C4.99821 9.50096 5.37154 9.12762 5.83154 9.12762ZM14.1649 14.961C14.6249 14.961 14.9982 15.3343 14.9982 15.7943C14.9982 16.2543 14.6249 16.6276 14.1649 16.6276C13.7049 16.6276 13.3315 16.2543 13.3315 15.7943C13.3315 15.3343 13.7049 14.961 14.1649 14.961Z"
      fill="#383838"
    />
  </svg>
);

export const EmojiIcon = ({ props }: { props?: SVGProps<SVGSVGElement> }) => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M16.0189 2.66516C8.65515 2.66516 2.68555 8.63449 2.68555 15.9985C2.68555 23.3625 8.65515 29.3318 16.0189 29.3318C23.3826 29.3318 29.3522 23.3625 29.3522 15.9985C29.3522 8.63449 23.3826 2.66516 16.0189 2.66516ZM12.0189 11.9985C12.7553 11.9985 13.3522 12.5958 13.3522 13.3318V14.6652C13.3522 15.4012 12.7553 15.9985 12.0189 15.9985C11.2825 15.9985 10.6855 15.4012 10.6855 14.6652V13.3318C10.6855 12.5958 11.2825 11.9985 12.0189 11.9985ZM20.0189 11.9985C20.7553 11.9985 21.3522 12.5958 21.3522 13.3318V14.6652C21.3522 15.4012 20.7553 15.9985 20.0189 15.9985C19.2825 15.9985 18.6855 15.4012 18.6855 14.6652V13.3318C18.6855 12.5958 19.2825 11.9985 20.0189 11.9985ZM12.2689 18.4571C12.6101 18.4571 12.925 18.5718 13.1855 18.8318C13.9317 19.5772 14.943 19.9985 16.0189 19.9985C17.091 19.9985 18.1067 19.5732 18.8522 18.8318C19.3745 18.3132 20.2081 18.3092 20.7271 18.8318C21.2462 19.3545 21.2495 20.1878 20.7271 20.7065C19.4871 21.9398 17.8026 22.6652 16.0189 22.6652C14.229 22.6652 12.5517 21.9465 11.3106 20.7065C10.7895 20.1865 10.7901 19.3532 11.3106 18.8318C11.5707 18.5718 11.9277 18.4571 12.2689 18.4571Z"
      fill="#A3A3A3"
    />
  </svg>
);

export const PictureIcon = ({ props }: { props?: SVGProps<SVGSVGElement> }) => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <g clipPath="url(#clip0_8467_7798)">
      <path
        d="M28.4445 3.55554H3.55561C3.08411 3.55554 2.63193 3.74284 2.29853 4.07624C1.96513 4.40964 1.77783 4.86182 1.77783 5.33332V26.6667C1.77783 27.1381 1.96513 27.5903 2.29853 27.9237C2.63193 28.2571 3.08411 28.4444 3.55561 28.4444H28.4445C28.916 28.4444 29.3682 28.2571 29.7016 27.9237C30.035 27.5903 30.2223 27.1381 30.2223 26.6667V5.33332C30.2223 4.86182 30.035 4.40964 29.7016 4.07624C29.3682 3.74284 28.916 3.55554 28.4445 3.55554ZM7.92894 7.1111C8.45636 7.1111 8.97193 7.2675 9.41046 7.56051C9.849 7.85353 10.1908 8.27001 10.3926 8.75728C10.5945 9.24454 10.6473 9.78072 10.5444 10.298C10.4415 10.8153 10.1875 11.2904 9.81456 11.6634C9.44162 12.0363 8.96647 12.2903 8.44918 12.3932C7.9319 12.4961 7.39572 12.4433 6.90845 12.2414C6.42118 12.0396 6.00471 11.6978 5.71169 11.2593C5.41867 10.8208 5.26228 10.3052 5.26228 9.77776C5.26228 9.07052 5.54323 8.39224 6.04333 7.89215C6.54342 7.39205 7.2217 7.1111 7.92894 7.1111ZM5.33339 24V20.3555L10.6667 14.9511C10.8333 14.7855 11.0586 14.6926 11.2934 14.6926C11.5282 14.6926 11.7535 14.7855 11.9201 14.9511L14.2223 17.2L7.39561 24H5.33339ZM26.6667 24H9.91117L15.4489 18.4622L20.2489 13.6622C20.4155 13.4967 20.6408 13.4037 20.8756 13.4037C21.1104 13.4037 21.3357 13.4967 21.5023 13.6622L26.6667 18.8267V24Z"
        fill="#A3A3A3"
      />
    </g>
    <defs>
      <clipPath id="clip0_8467_7798">
        <rect width="32" height="32" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const HelpResolvedIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.25 1.25003V5.62503C11.25 6.12231 11.4476 6.59923 11.7992 6.95086C12.1508 7.30249 12.6278 7.50003 13.125 7.50003H17.5V16.875C17.5 17.3723 17.3025 17.8492 16.9509 18.2009C16.5992 18.5525 16.1223 18.75 15.625 18.75H10.8288C12.1591 17.817 13.116 16.4429 13.5296 14.8715C13.9432 13.3001 13.7868 11.6329 13.0883 10.1658C12.3897 8.69874 11.1941 7.52649 9.71346 6.85704C8.23284 6.18758 6.56296 6.0642 5.00004 6.50878V3.12503C5.00004 2.62775 5.19758 2.15084 5.54921 1.79921C5.90084 1.44757 6.37775 1.25003 6.87504 1.25003H11.25ZM12.5 1.56253V5.62503C12.5 5.79079 12.5659 5.94976 12.6831 6.06697C12.8003 6.18418 12.9593 6.25003 13.125 6.25003H17.1875L12.5 1.56253ZM1.25 13.125C1.25 11.6332 1.84264 10.2024 2.89753 9.14753C3.95243 8.09263 5.38318 7.5 6.87504 7.5C8.36689 7.5 9.79764 8.09263 10.8525 9.14753C11.9074 10.2024 12.5001 11.6332 12.5001 13.125C12.5001 14.6169 11.9074 16.0476 10.8525 17.1025C9.79764 18.1574 8.36689 18.7501 6.87504 18.7501C5.38318 18.7501 3.95243 18.1574 2.89753 17.1025C1.84264 16.0476 1.25 14.6169 1.25 13.125ZM10.4125 11.8461C10.6663 11.5922 10.6663 11.1807 10.4125 10.9268C10.1586 10.673 9.74705 10.673 9.49321 10.9268L6.20283 14.2172L4.41245 12.4268C4.15861 12.173 3.74705 12.173 3.49321 12.4268C3.23937 12.6807 3.23937 13.0922 3.49321 13.3461L5.6725 15.5254C5.9654 15.8183 6.44027 15.8183 6.73316 15.5254L10.4125 11.8461Z"
      fill="#F97316"
    />
  </svg>
);

export const AssignIcon = ({ props }: { props?: SVGProps<SVGSVGElement> }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M11.25 5.62503V1.25003H6.875C6.37772 1.25003 5.90081 1.44757 5.54917 1.79921C5.19754 2.15084 5 2.62775 5 3.12503V6.50878C6.56293 6.0642 8.23281 6.18758 9.71342 6.85704C11.194 7.52649 12.3897 8.69874 13.0882 10.1658C13.7868 11.6329 13.9432 13.3001 13.5295 14.8715C13.1159 16.4429 12.1591 17.817 10.8288 18.75H15.625C16.1223 18.75 16.5992 18.5525 16.9508 18.2009C17.3025 17.8492 17.5 17.3723 17.5 16.875V7.50003H13.125C12.6277 7.50003 12.1508 7.30249 11.7992 6.95086C11.4475 6.59923 11.25 6.12231 11.25 5.62503ZM12.5 5.62503V1.56253L17.1875 6.25003H13.125C12.9592 6.25003 12.8003 6.18418 12.6831 6.06697C12.5658 5.94976 12.5 5.79079 12.5 5.62503Z"
      fill="#F97316"
    />
    <path
      d="M6.8691 7.50802C3.76351 7.50802 1.24609 10.0254 1.24609 13.131C1.24609 16.2366 3.76351 18.754 6.8691 18.754C9.97468 18.754 12.4921 16.2366 12.4921 13.131C12.4921 10.0254 9.97468 7.50802 6.8691 7.50802ZM9.6806 13.6933H7.4314V15.9425H6.3068V13.6933H4.0576V12.5687H6.3068V10.3195H7.4314V12.5687H9.6806V13.6933Z"
      fill="#F97316"
    />
  </svg>
);

export const CalendarClockIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M16 4.5C16 3.83696 15.7366 3.20107 15.2678 2.73223C14.7989 2.26339 14.163 2 13.5 2H4.5C3.83696 2 3.20107 2.26339 2.73223 2.73223C2.26339 3.20107 2 3.83696 2 4.5V5H16V4.5ZM16 8.6V6H2V13.5C2 14.163 2.26339 14.7989 2.73223 15.2678C3.20107 15.7366 3.83696 16 4.5 16H8.6C8.07116 14.9648 7.88393 13.7886 8.06518 12.6404C8.24643 11.4921 8.78684 10.4308 9.60883 9.60883C10.4308 8.78684 11.4921 8.24643 12.6404 8.06518C13.7886 7.88393 14.9648 8.07116 16 8.6ZM13.5 18C14.6935 18 15.8381 17.5259 16.682 16.682C17.5259 15.8381 18 14.6935 18 13.5C18 12.3065 17.5259 11.1619 16.682 10.318C15.8381 9.47411 14.6935 9 13.5 9C12.3065 9 11.1619 9.47411 10.318 10.318C9.47411 11.1619 9 12.3065 9 13.5C9 14.6935 9.47411 15.8381 10.318 16.682C11.1619 17.5259 12.3065 18 13.5 18ZM13 11.5C13 11.3674 13.0527 11.2402 13.1464 11.1464C13.2402 11.0527 13.3674 11 13.5 11C13.6326 11 13.7598 11.0527 13.8536 11.1464C13.9473 11.2402 14 11.3674 14 11.5V13H15C15.1326 13 15.2598 13.0527 15.3536 13.1464C15.4473 13.2402 15.5 13.3674 15.5 13.5C15.5 13.6326 15.4473 13.7598 15.3536 13.8536C15.2598 13.9473 15.1326 14 15 14H13.5C13.3674 14 13.2402 13.9473 13.1464 13.8536C13.0527 13.7598 13 13.6326 13 13.5V11.5Z"
      fill="#F97316"
    />
  </svg>
);

export const BasilEditIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M13.7308 2.50333C13.87 2.51583 13.9308 2.68583 13.8308 2.785L6.89751 9.71833C6.81919 9.7967 6.76311 9.89449 6.73501 10.0017L5.90168 13.1933C5.87417 13.2988 5.87473 13.4096 5.90329 13.5148C5.93185 13.62 5.98743 13.7159 6.06451 13.793C6.14159 13.8701 6.23748 13.9257 6.34268 13.9542C6.44787 13.9828 6.55871 13.9833 6.66418 13.9558L9.85501 13.1225C9.96227 13.0941 10.06 13.0378 10.1383 12.9592L17.17 5.9275C17.192 5.90491 17.22 5.88911 17.2507 5.88194C17.2814 5.87478 17.3135 5.87657 17.3433 5.88709C17.373 5.89761 17.3991 5.91642 17.4184 5.94131C17.4378 5.96619 17.4496 5.9961 17.4525 6.0275C17.7451 8.81886 17.7283 11.634 17.4025 14.4217C17.2167 16.0092 15.9408 17.255 14.3592 17.4325C11.4622 17.7537 8.53864 17.7537 5.64168 17.4325C4.05918 17.255 2.78335 16.0092 2.59751 14.4217C2.25395 11.4842 2.25395 8.51666 2.59751 5.57917C2.78335 3.99083 4.05918 2.745 5.64168 2.56833C8.32878 2.27055 11.0393 2.24877 13.7308 2.50333Z"
      fill="#F97316"
    />
    <path
      d="M14.8523 3.53084C14.8716 3.51144 14.8946 3.49605 14.9199 3.48554C14.9452 3.47504 14.9724 3.46964 14.9998 3.46964C15.0272 3.46964 15.0543 3.47504 15.0796 3.48554C15.1049 3.49605 15.1279 3.51144 15.1473 3.53084L16.3256 4.71001C16.3645 4.74906 16.3864 4.80195 16.3864 4.85709C16.3864 4.91223 16.3645 4.96512 16.3256 5.00417L9.41478 11.9167C9.38843 11.9428 9.35566 11.9615 9.31978 11.9708L7.72478 12.3875C7.68962 12.3967 7.65267 12.3965 7.61761 12.387C7.58254 12.3774 7.55058 12.3589 7.52489 12.3332C7.49919 12.3075 7.48067 12.2756 7.47115 12.2405C7.46163 12.2054 7.46144 12.1685 7.47061 12.1333L7.88728 10.5383C7.89655 10.5024 7.91525 10.4696 7.94144 10.4433L14.8523 3.53084Z"
      fill="#F97316"
    />
  </svg>
);

export const HeartReactedIcon = ({
  props,
}: {
  props?: SVGProps<SVGSVGElement>;
}) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M6.24911 2.4924C3.71777 2.4924 1.66577 4.54407 1.66577 7.07574C1.66577 9.72908 3.17702 12.2324 5.67619 14.5241C6.52919 15.3058 7.44761 15.9916 8.35844 16.6074C8.67769 16.8224 8.97627 17.0174 9.24394 17.1799C9.40711 17.2791 9.51469 17.3508 9.58244 17.3883C9.83411 17.5283 10.1641 17.5283 10.4158 17.3883C10.4835 17.3508 10.5911 17.2791 10.7543 17.1799C11.0219 17.0174 11.3205 16.8224 11.6398 16.6074C12.5506 15.9916 13.469 15.3058 14.322 14.5241C16.8212 12.2324 18.3324 9.72908 18.3324 7.07574C18.3324 4.54407 16.2804 2.4924 13.7491 2.4924C12.3114 2.4924 10.9078 3.27575 10.0251 4.41908C9.16444 3.25575 7.76569 2.4924 6.24911 2.4924Z"
      fill="url(#paint0_linear_4367_47145)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_4367_47145"
        x1="9.99911"
        y1="2.4924"
        x2="9.99911"
        y2="17.4933"
        gradientUnits="userSpaceOnUse">
        <stop stopColor="#FFCDA8" />
        <stop offset="1" stopColor="#FF8228" />
      </linearGradient>
    </defs>
  </svg>
);

export const DismissIcon = ({ props }: { props?: SVGProps<SVGSVGElement> }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M11.25 5.62506V1.25006H6.87503C6.37775 1.25006 5.90084 1.44761 5.54921 1.79924C5.19758 2.15087 5.00003 2.62778 5.00003 3.12506V6.50881C6.56296 6.06423 8.23284 6.18761 9.71345 6.85706C11.1941 7.52652 12.3897 8.69877 13.0883 10.1659C13.7868 11.633 13.9432 13.3001 13.5296 14.8715C13.116 16.4429 12.1591 17.817 10.8288 18.7501H15.625C16.1223 18.7501 16.5992 18.5525 16.9509 18.2009C17.3025 17.8493 17.5 17.3723 17.5 16.8751V7.50006H13.125C12.6278 7.50006 12.1508 7.30252 11.7992 6.95089C11.4476 6.59926 11.25 6.12234 11.25 5.62506ZM12.5 5.62506V1.56256L17.1875 6.25006H13.125C12.9593 6.25006 12.8003 6.18421 12.6831 6.067C12.5659 5.94979 12.5 5.79082 12.5 5.62506ZM2.89753 9.14756C1.84264 10.2025 1.25 11.6332 1.25 13.1251C1.25 14.6169 1.84264 16.0477 2.89753 17.1026C3.95243 18.1575 5.38318 18.7501 6.87503 18.7501C8.36689 18.7501 9.79764 18.1575 10.8525 17.1026C11.9074 16.0477 12.5001 14.6169 12.5001 13.1251C12.5001 11.6332 11.9074 10.2025 10.8525 9.14756C9.79764 8.09266 8.36689 7.50003 6.87503 7.50003C5.38318 7.50003 3.95243 8.09266 2.89753 9.14756ZM9.08503 15.3351C8.96783 15.4522 8.80889 15.5181 8.64316 15.5181C8.47743 15.5181 8.31849 15.4522 8.20128 15.3351L6.87503 14.0101L5.55003 15.3351C5.43284 15.4524 5.27383 15.5184 5.10798 15.5185C4.94212 15.5187 4.78302 15.4529 4.66566 15.3357C4.5483 15.2185 4.4823 15.0595 4.48219 14.8936C4.48207 14.7278 4.54784 14.5687 4.66503 14.4513L5.99003 13.1251L4.66503 11.8001C4.54768 11.6829 4.48168 11.5239 4.48156 11.358C4.4815 11.2759 4.49762 11.1946 4.52899 11.1187C4.56037 11.0428 4.60638 10.9738 4.66441 10.9157C4.72244 10.8576 4.79134 10.8115 4.86719 10.78C4.94304 10.7485 5.02435 10.7323 5.10647 10.7322C5.27232 10.7321 5.43143 10.7979 5.54878 10.9151L6.87503 12.2401L8.20003 10.9151C8.31723 10.7977 8.47624 10.7317 8.64209 10.7316C8.80794 10.7315 8.96705 10.7972 9.08441 10.9144C9.20177 11.0316 9.26777 11.1906 9.26788 11.3565C9.268 11.5223 9.20223 11.6815 9.08503 11.7988L7.76003 13.1251L9.08503 14.4501C9.14324 14.5081 9.18942 14.5771 9.22092 14.653C9.25243 14.729 9.26865 14.8104 9.26865 14.8926C9.26865 14.9748 9.25243 15.0562 9.22092 15.1321C9.18942 15.208 9.14324 15.277 9.08503 15.3351Z"
      fill="#F97316"
    />
  </svg>
);

export const TagIcon = ({ props }: { props?: SVGProps<SVGSVGElement> }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}>
    <path
      d="M3.33317 5.00018C2.89114 5.00018 2.46722 5.17578 2.15466 5.48834C1.8421 5.8009 1.6665 6.22482 1.6665 6.66685V13.3335C1.6665 13.7755 1.8421 14.1995 2.15466 14.512C2.46722 14.8246 2.89114 15.0002 3.33317 15.0002H13.0107C13.4255 15.0003 13.8254 14.8458 14.1323 14.5668L17.799 11.2335C17.9709 11.0773 18.1082 10.8869 18.2022 10.6745C18.2961 10.4621 18.3447 10.2324 18.3447 10.0002C18.3447 9.76793 18.2961 9.53825 18.2022 9.32586C18.1082 9.11347 17.9709 8.92306 17.799 8.76685L14.1323 5.43352C13.8254 5.15453 13.4255 5.00002 13.0107 5.00018H3.33317Z"
      fill="#FF8228"
    />
  </svg>
);

export const PinIcon =  ({ props }: { props?: SVGProps<SVGSVGElement> }) => (
  <svg
    width="17"
    height="17"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M13.7091 9.185L14.8257 8.06833L15.4149 8.6575L16.5941 7.47917L9.52242 0.408333L8.34408 1.58667L8.93325 2.17583L7.81658 3.2925L13.7091 9.185Z" fill="#F97316"/>
    <path d="M13.7091 9.185L11.3516 11.5425L11.2907 11.6042L10.7016 14.55L9.52242 15.7292L5.98742 12.1933L1.86242 16.3183L0.684082 15.14L4.80908 11.015L1.27325 7.47917L2.45158 6.30083L5.39825 5.71167L5.45908 5.65L7.81658 3.29167L13.7091 9.185Z" fill="#F97316"/>
  </svg>
);

export const UnpinIcon =  ({ props }: { props?: SVGProps<SVGSVGElement> }) => (
<svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"
{...props}>
<path d="M15.4757 13.31L14.2974 14.4883L11.3516 11.5425L11.2907 11.6042L10.7016 14.55L9.52242 15.7292L5.98742 12.1933L1.86242 16.3183L0.684082 15.14L4.80908 11.015L1.27325 7.47916L2.45158 6.30083L5.39825 5.71166L5.45908 5.64999L2.51242 2.70333L3.69075 1.52416L15.4757 13.31ZM13.7091 9.18499L14.8257 8.06833L15.4149 8.65749L16.5941 7.47916L9.52242 0.408325L8.34408 1.58666L8.93325 2.17583L7.81658 3.29249L13.7091 9.18499Z" fill="#F97316"
/>
</svg>
   
);