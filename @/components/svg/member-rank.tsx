import { SVGProps } from 'react';

const MemberRank = () => {
  return (
    <svg
      width="28"
      height="28"
      viewBox="0 0 150 150"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_4718_13568)">
        <circle cx="75" cy="75" r="68.75" fill="#01D6FF" />
        <circle
          cx="75"
          cy="75"
          r="68.75"
          fill="url(#paint0_linear_4718_13568)"
        />
        <circle
          cx="75"
          cy="75"
          r="68.75"
          fill="url(#paint1_linear_4718_13568)"
        />
        <circle cx="75" cy="75" r="68.75" stroke="white" strokeWidth="12.5" />
        <path
          d="M75 30L83.8499 54.7943C85.7405 60.0911 89.9089 64.2595 95.2057 66.1501L120 75L95.2057 83.8499C89.9089 85.7405 85.7405 89.9089 83.8499 95.2057L75 120L66.1501 95.2057C64.2595 89.9089 60.0911 85.7405 54.7943 83.8499L30 75L54.7943 66.1501C60.0911 64.2595 64.2595 60.0911 66.1501 54.7943L75 30Z"
          fill="white"
        />
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_4718_13568"
          x1="150"
          y1="3.22431e-05"
          x2="-19.9183"
          y2="27.666"
          gradientUnits="userSpaceOnUse">
          <stop offset="0.253028" stopColor="#00D0FF" />
          <stop offset="0.858273" stopColor="#B5F0FD" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_4718_13568"
          x1="150"
          y1="3.22431e-05"
          x2="-19.9183"
          y2="27.666"
          gradientUnits="userSpaceOnUse">
          <stop offset="0.253028" stopColor="#F99651" />
          <stop offset="0.858273" stopColor="#F8CEB1" />
        </linearGradient>
        <clipPath id="clip0_4718_13568">
          <rect width="150" height="150" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export { MemberRank };
