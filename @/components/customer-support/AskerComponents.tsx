import { getBadgeColor } from 'btaskee-utils';
import type { FormState } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Typography } from '../btaskee/Typography';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { DialogClose } from '../ui/dialog';
import { Separator } from '../ui/separator';
import { InfoBlock, InfoCardWrapper } from './InformationCard';

const AskerInfoDialog = ({
  asker,
  t,
}: {
  asker: AskerGeneral;
  // TODO replace type
  t: MustBeAny;
}) => {
  return (
    <InfoCardWrapper className="p-4 rounded-md">
      <div className="flex flex-col gap-3">
        <InfoBlock label={t('ASKER_NAME')} value={asker?.name} />
        <Separator />
        <InfoBlock label={t('PHONE')} value={asker?.phone} />
      </div>
    </InfoCardWrapper>
  );
};

const FooterButton = ({
  warningText,
  isDirty,
  errors,
}: {
  warningText?: string;
  isDirty: boolean;
  errors: FormState<MustBeAny>['errors'];
}) => {
  const { t } = useTranslation('common');
  return (
    <div className="flex sm:flex-col gap-3 mt-4">
      {warningText && (
        <Typography
          className="text-sm text-end leading-tight text-yellow-500"
          affects="removePMargin"
          variant="p">
          {warningText}
        </Typography>
      )}
      <div className="ml-auto flex gap-4">
        <DialogClose asChild>
          <Button
            className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
            type="button"
            variant="outline">
            {t('CANCEL')}
          </Button>
        </DialogClose>
        <Button
          className={
            !isDirty || Object.keys(errors).length > 0 ? 'bg-gray-300' : ''
          }
          disabled={!isDirty || Object.keys(errors).length > 0}
          type="submit">
          {t('CONFIRM')}
        </Button>
      </div>
    </div>
  );
};

const TagBadge = ({
  text,
  variant,
  size = 'md',
}: {
  text: string;
  variant: 'success' | 'info' | 'danger' | string;
  size?: 'sm' | 'md';
}) => {
  const classSize = {
    sm: 'py-0.5 px-2',
    md: 'py-1.5 px-3',
  };

  return (
    <Badge
      className={`text-nowrap font-medium rounded-md ${classSize[size]} ${getBadgeColor(variant)}`}>
      {text}
    </Badge>
  );
};

const TaskTypeBadge = ({
  task,
  isServiceType,
  size = 'md',
}: {
  // TODO replace task type
  task: MustBeAny;
  isServiceType: boolean;
  size?: 'sm' | 'md';
}) => {
  if (isServiceType) {
    if (task?.isPremium)
      return <TagBadge text={'Premium'} variant="info" size={size} />;
    if (task?.isEco)
      return <TagBadge text="Eco Sub" variant="info" size={size} />;
    if (task?.isTetBooking)
      return <TagBadge text="Tet Booking" variant="info" size={size} />;
    return <TagBadge text="Normal" variant="info" size={size} />;
  } else {
    if (task?.scheduleId)
      return <TagBadge text="Schedule" variant="info" size={size} />;
    if (task?.subscriptionId)
      return <TagBadge text="Subscription" variant="info" size={size} />;
    return '-';
  }
};

export { AskerInfoDialog, FooterButton, TagBadge, TaskTypeBadge };
