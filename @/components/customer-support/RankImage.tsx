import { MemberRank } from '@/components/svg/member-rank';
import {
  GoldRank,
  PlatinumRank,
  SilverRank,
} from '@/components/svg/rank-icons';
import { USER_RANKS } from 'btaskee-constants';
import type { SVGProps as SVGProperties } from 'react';

export const RankImage = ({
  rankName,
  SVGProps = {
    width: 28,
    height: 28,
  },
}: {
  rankName: (typeof USER_RANKS)[keyof typeof USER_RANKS];
  SVGProps?: SVGProperties<SVGSVGElement>;
}) => {
  switch (rankName) {
    case USER_RANKS.MEMBER:
      return <MemberRank {...SVGProps} />;
    case USER_RANKS.SILVER:
      return <SilverRank {...SVGProps} />;
    case USER_RANKS.GOLD:
      return <GoldRank {...SVGProps} />;
    case USER_RANKS.PLATINUM:
      return <PlatinumRank {...SVGProps} />;
    default:
      return null;
  }
};
