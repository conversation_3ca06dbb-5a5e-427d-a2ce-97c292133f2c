import { zodResolver } from '@hookform/resolvers/zod';
import { useSubmit } from '@remix-run/react';
import type { FieldValues, UseFormProps } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import type { ZodRawShape } from 'zod';
import { z } from 'zod';
import { useConfirm } from './useConfirmation';

export function useBtaskeeFormController<TFieldValues extends FieldValues>({
  zodRaw,
  defaultValues,
  confirmParams,
  formDataProvided,
}: {
  zodRaw: ZodRawShape;
  defaultValues: UseFormProps<TFieldValues>['defaultValues'];
  confirmParams: ConfirmationParams<'confirm'>;
  formDataProvided: (data: TFieldValues) => FormData;
}) {
  const confirm = useConfirm();
  const submit = useSubmit();

  const resolverSchema = zodResolver(z.object(zodRaw));
  const form = useForm<TFieldValues>({
    resolver: resolverSchema,
    defaultValues,
  });

  const onSubmit = async (data: TFieldValues) => {
    // Handle string case
    if (typeof confirmParams === 'string') {
      if (await confirm(confirmParams)) {
        submit(formDataProvided(data), {
          method: 'post',
          encType: 'multipart/form-data',
        });
      }
      return;
    }

    // Handle object case
    const confirmContent = {
      ...confirmParams,
      body:
        typeof confirmParams.body === 'function' ?
          confirmParams.body(data)
        : confirmParams.body,
    } as const;

    if (await confirm(confirmContent)) {
      submit(formDataProvided(data), {
        method: 'post',
        encType: 'multipart/form-data',
      });
    }
  };

  return { form, onSubmit };
}
