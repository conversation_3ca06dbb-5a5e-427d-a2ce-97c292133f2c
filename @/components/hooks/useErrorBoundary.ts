import { useNavigate } from '@remix-run/react';
import { toast } from '@/components/ui/use-toast';
import { useEffect, useState } from 'react';

export function useErrorBoundaryDialogRoute() {
  const navigate = useNavigate();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    toast({
      description: 'Error! Please try again later.',
    });
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted) return navigate(-1);
  }, [mounted, navigate]);

  return {};
}
