import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import { Link, useSearchParams } from '@remix-run/react';
import { type ColumnDef } from '@tanstack/react-table';
import { ROUTE_NAME } from 'btaskee-constants';
import { getPageSizeAndPageIndex } from 'btaskee-utils';
import { format } from 'date-fns';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { StatusBadge } from '../btaskee/StatusBadge';
import { BTaskeeTable } from '../btaskee/TableBase';
import { DataTableColumnHeader } from '../btaskee/table-data/data-table-column-header';
import { Button } from '../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';

export const BRewardTableWithPartner = ({
  data,
  total,
  filterValue,
}: {
  data: Array<BReward & { _id: string }>;
  total: number;
  filterValue: {
    search: string;
    rangeDate: { from: Date; to: Date };
  };
}) => {
  const { t } = useTranslation('partner');
  const [searchParams] = useSearchParams();

  const columns: ColumnDef<BReward & { _id: string }>[] = useMemo(
    () => [
      {
        accessorKey: 'image',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('IMAGE')} />
        ),
        cell: ({ row }) => (
          <img
            className="w-[100px]"
            src={row.getValue('image')}
            alt="breward-img"
          />
        ),
      },
      {
        accessorKey: 'title',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('TITLE')} />
        ),
        cell: ({ row }) => (
          <div>
            {/* Alway get title by english, not need use user language */}
            {row.original.title.en}
          </div>
        ),
      },
      {
        accessorKey: 'point',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('BPOINTS')} />
        ),
        cell: ({ row }) => <div>{row.original.exchange.point}</div>,
      },
      {
        accessorKey: 'validity_period',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('VALIDITY_PERIOD')} />
        ),
        cell: ({ row }) => (
          <div>
            {format(row.original.startDate, 'HH:mm dd/MM/yyyy') +
              ' - ' +
              format(row.original.endDate, 'HH:mm dd/MM/yyyy')}
          </div>
        ),
        enableSorting: false,
      },
      {
        accessorKey: 'status',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('STATUS')} />
        ),
        cell: ({ row }) => <StatusBadge status={row.getValue('status')} />,
      },
      {
        id: 'actions',
        header: ({ column }) => (
          <DataTableColumnHeader column={column} title={t('ACTIONS')} />
        ),
        cell: ({ row }) => (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex h-8 w-8 p-0 data-[state=open]:bg-muted">
                <DotsHorizontalIcon className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[120px]">
              <Link to={`${ROUTE_NAME.MARKETING_BREWARDS}/${row.original._id}`}>
                <DropdownMenuItem>{t('VIEW')}</DropdownMenuItem>
              </Link>
            </DropdownMenuContent>
          </DropdownMenu>
        ),
      },
    ],
    [t],
  );

  return (
    <BTaskeeTable
      isShowClearButton
      total={total || 0}
      data={data || []}
      columns={columns}
      pagination={getPageSizeAndPageIndex({
        total: total || 0,
        pageSize: Number(searchParams.get('pageSize') || 0),
        pageIndex: Number(searchParams.get('pageIndex') || 0),
      })}
      search={{
        defaultValue: searchParams.get('title') || '',
        name: 'title',
      }}
      filterDate={{
        name: 'createdAt',
        defaultValue: filterValue?.rangeDate,
      }}
    />
  );
};
