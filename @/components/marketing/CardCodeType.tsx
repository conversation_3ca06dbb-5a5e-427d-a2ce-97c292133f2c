import { type UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { AutosizeTextarea } from '../btaskee/AutoResizeTextArea';
import { RadioGroupsBase } from '../btaskee/RadioGroupBase';
import { Card, CardContent, CardHeader } from '../ui/card';
import { FormControl } from '../ui/form';
import { FormLabel } from '../ui/form';
import { FormField } from '../ui/form';
import { FormItem } from '../ui/form';
import { Input } from '../ui/input';

export const CardCodeType = ({
  form,
  isEdit = false,
}: {
  form: UseFormReturn<BRewardFormData>;
  isEdit?: boolean;
}) => {
  const { t } = useTranslation('breward');
  const { control, watch } = form;
  const watchCodeType = watch('codeType');

  return (
    <Card>
      <CardHeader className="rounded-t-lg bg-gray-50 p-4">
        <FormField
          name="codeType"
          control={control}
          render={({ field: { onChange, value } }) => (
            <RadioGroupsBase
              disabled={isEdit}
              className="flex-row items-center"
              defaultValue={value}
              onValueChange={onChange}
              options={[
                { label: t('CODE_LIST'), value: 'CODE_LIST' },
                {
                  label: t('CODE_FROM_PARTNER'),
                  value: 'CODE_FROM_PARTNER',
                },
                {
                  label: t('GIFT_INFORMATION'),
                  value: 'GIFT_INFORMATION',
                },
              ]}
            />
          )}
        />
      </CardHeader>
      <CardContent className="p-4">
        <div className="grid grid-cols-2 gap-6">
          {watchCodeType === 'CODE_LIST' && (
            <div className="col-span-1">
              <FormField
                name="codeList"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {t('CODE_LIST')}
                    </FormLabel>
                    <FormControl>
                      <AutosizeTextarea
                        value={value}
                        onChange={onChange}
                        className={`min-h-10 ${isEdit ? 'bg-gray-300' : ''}`}
                        rows={isEdit ? 5 : 1}
                        placeholder={t('ENTER_CONTENT')}
                        disabled={isEdit}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          )}
          {watchCodeType === 'CODE_FROM_PARTNER' && (
            <>
              <FormField
                name="codeFromPartner.code"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">{t('CODE')}</FormLabel>
                    <FormControl>
                      <Input
                        className={`${isEdit ? 'bg-gray-300' : ''}`}
                        value={value}
                        onChange={onChange}
                        placeholder={t('ENTER_CODE')}
                        disabled={isEdit}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name="codeFromPartner.limit"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {t('LIMIT')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        className={`${isEdit ? 'bg-gray-300' : ''}`}
                        type="number"
                        defaultValue={0}
                        value={value}
                        onChange={onChange}
                        placeholder={t('ENTER_LIMIT')}
                        disabled={isEdit}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </>
          )}
          {watchCodeType === 'GIFT_INFORMATION' && (
            <>
              <FormField
                name="giftInfo.id"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">{t('ID')}</FormLabel>
                    <FormControl>
                      <Input
                        className={`${isEdit ? 'bg-gray-300' : ''}`}
                        value={value}
                        onChange={onChange}
                        placeholder={t('ENTER_ID')}
                        disabled={isEdit}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name="giftInfo.campaignCode"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {t('CAMPAIGN_CODE')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        className={`${isEdit ? 'bg-gray-300' : ''}`}
                        value={value}
                        onChange={onChange}
                        placeholder={t('CHOOSE_CAMPAIGN_CODE')}
                        disabled={isEdit}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name="giftInfo.cat_id"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {t('CAT_ID')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        className={`${isEdit ? 'bg-gray-300' : ''}`}
                        value={value}
                        onChange={onChange}
                        placeholder={t('ENTER_CAT_ID')}
                        disabled={isEdit}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name="giftInfo.gift_id"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {t('GIFT_ID')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        className={`${isEdit ? 'bg-gray-300' : ''}`}
                        value={value}
                        onChange={onChange}
                        placeholder={t('ENTER_GIFT_ID')}
                        disabled={isEdit}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                name="giftInfo.price"
                control={control}
                render={({ field: { onChange, value } }) => (
                  <FormItem>
                    <FormLabel className="text-gray-700">
                      {t('PRICE')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        className={`${isEdit ? 'bg-gray-300' : ''}`}
                        type="number"
                        defaultValue={0}
                        value={value}
                        onChange={onChange}
                        placeholder={t('ENTER_PRICE')}
                        disabled={isEdit}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
