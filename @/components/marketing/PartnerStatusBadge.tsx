import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';

import { Badge } from '../ui/badge';

interface StatusBadgeProps {
  status: string;
  statusClasses?: { [key: string]: string };
}

const DefaultStatusClasses: { [key: string]: string } = {
  WAITING: 'bg-gray-100 text-gray-500 rounded-md text-center',
  PROCESSING: 'bg-blue-50 text-blue rounded-md text-center',
  COMPLETED: 'bg-secondary text-secondary-foreground rounded-md text-center',
  REJECT: 'bg-red-50 text-red rounded-md text-center',
};

export const PartnerStatusBadge = ({
  status,
  statusClasses = DefaultStatusClasses,
}: StatusBadgeProps) => {
  const { t } = useTranslation('partner');
  const statusClass = statusClasses[status];

  return (
    <div>
      <Badge className={cn(statusClass, 'text-sm font-normal')}>
        {t(status)}
      </Badge>
    </div>
  );
};
