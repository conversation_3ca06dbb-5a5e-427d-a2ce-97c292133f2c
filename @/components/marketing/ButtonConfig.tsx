import {
  NAVIGATE_TO,
  NAVIGATE_TO_TAB_ACTIVITY,
  NAVIGATE_TO_TAB_NOTIFICATION,
  POST_TASK_STEP_1,
  TAB_ACTIVITY,
  TAB_NOTIFICATION,
  TYPE_OF_REDIRECT,
} from 'btaskee-constants';
import { useState } from 'react';
import type { UseFormReturn } from 'react-hook-form';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { Grid } from '../btaskee/Grid';
import { GridItem } from '../btaskee/GridItem';
import { ErrorMessageBase } from '../btaskee/MessageBase';
import { MultiLanguageSection } from '../btaskee/MultiLanguageSection';
import { RadioGroupsBase } from '../btaskee/RadioGroupBase';
import { SelectBase } from '../btaskee/SelectBase';
import { Typography } from '../btaskee/Typography';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Switch } from '../ui/switch';

export interface ButtonConfigProps {
  nameTypeOfButton: string;
  title: string;
  form: UseFormReturn<MustBeAny>;
  services: MustBeAny;
  globalData: MustBeAny;
}

export const ButtonConfig = ({
  title,
  form,
  nameTypeOfButton,
  services,
  globalData,
}: ButtonConfigProps) => {
  const { t } = useTranslation('campaign');

  const { control, register, watch, formState } = form;
  const type = watch(`${nameTypeOfButton}.typeOfRedirect`);
  const typeOfNavigate = watch(`${nameTypeOfButton}.action.navigateTo`);

  const [optionNavigate, setOptionNavigate] = useState(() => {
    const initialOptions = [...NAVIGATE_TO];

    if (typeOfNavigate) {
      initialOptions.push({
        label: typeOfNavigate,
        value: typeOfNavigate,
      });
    }

    const uniqueOptions = initialOptions.filter(
      (option, index, self) =>
        index === self.findIndex(o => o.value === option.value),
    );

    return uniqueOptions;
  });

  const addNewOption = (newOption: OptionType) => {
    setOptionNavigate([...optionNavigate, newOption]);
  };

  const ParamsOption = ({ type }: { type: MustBeAny }) => {
    switch (type) {
      case POST_TASK_STEP_1:
        return (
          <div className="grid">
            <Controller
              control={control}
              rules={{ required: t('THIS_FIELD_IS_REQUIRED') }}
              name={`${nameTypeOfButton}.action.params.serviceId`}
              render={({ field: { onChange, value, ref } }) => (
                <SelectBase
                  selectTriggerRef={ref}
                  onValueChange={onChange}
                  defaultValue={value}
                  options={services.map((service: MustBeAny) => ({
                    label: `${service.text[globalData?.language]}${
                      service.isSubscription ? ` (${t('SUBSCRIPTION')})` : ''
                    }`,
                    value: service?._id,
                  }))}
                  placeholder={t('SELECT_SERVICES')}
                />
              )}
            />
            <ErrorMessageBase
              errors={formState.errors}
              name={`${nameTypeOfButton}.action.params.serviceId`}
            />
          </div>
        );
      case TAB_ACTIVITY:
        return (
          <div className="grid">
            <Controller
              control={control}
              name={`${nameTypeOfButton}.action.params.screen`}
              rules={{ required: t('THIS_FIELD_IS_REQUIRED') }}
              render={({ field: { onChange, value, ref } }) => (
                <SelectBase
                  selectTriggerRef={ref}
                  onValueChange={onChange}
                  defaultValue={value}
                  options={NAVIGATE_TO_TAB_ACTIVITY.map(activity => ({
                    label: activity?.label,
                    value: activity.value,
                  }))}
                  placeholder={t('SELECT_ACTIVITY')}
                />
              )}
            />
            <ErrorMessageBase
              errors={formState.errors}
              name={`${nameTypeOfButton}.action.params.screen`}
            />
          </div>
        );
      case TAB_NOTIFICATION:
        return (
          <div className="grid">
            <Controller
              control={control}
              name={`${nameTypeOfButton}.action.params.screen`}
              rules={{ required: t('THIS_FIELD_IS_REQUIRED') }}
              render={({ field: { onChange, value, ref } }) => (
                <SelectBase
                  selectTriggerRef={ref}
                  onValueChange={onChange}
                  defaultValue={value}
                  options={NAVIGATE_TO_TAB_NOTIFICATION.map(activity => ({
                    label: activity?.label,
                    value: activity.value,
                  }))}
                  placeholder={t('SELECT_ACTIVITY')}
                />
              )}
            />
            <ErrorMessageBase
              errors={formState.errors}
              name={`${nameTypeOfButton}.action.params.screen`}
            />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Grid className="h-fit">
      <Typography variant="h4">{t(title)}</Typography>
      <Controller
        control={control}
        name={`${nameTypeOfButton}.typeOfRedirect`}
        render={({ field }) => (
          <RadioGroupsBase
            className="my-6 flex-row items-center"
            defaultValue={field.value}
            onValueChange={field.onChange}
            options={[
              {
                label: t('NAVIGATE_TO'),
                value: TYPE_OF_REDIRECT.NAVIGATE_TO,
              },
              {
                label: t('URL'),
                value: TYPE_OF_REDIRECT.URL,
              },
            ]}
          />
        )}
      />
      {type === TYPE_OF_REDIRECT.NAVIGATE_TO ? (
        <GridItem>
          <Label className="text-gray-700">{t('NAVIGATION')}</Label>
          <Controller
            control={control}
            name={`${nameTypeOfButton}.action.navigateTo`}
            render={({ field: { onChange, value, ref } }) => (
              <SelectBase
                selectTriggerRef={ref}
                onValueChange={onChange}
                defaultValue={value}
                options={Object.values(optionNavigate).map(navigate => ({
                  label: navigate.label,
                  value: navigate.value,
                }))}
                placeholder={t('SELECT_ITEM')}
                isAddItem
                addItem={addNewOption}
                newItemPlaceholder={t('ENTER_NEW_ITEM')}
              />
            )}
          />
          <ErrorMessageBase
            errors={formState.errors}
            name={`${nameTypeOfButton}.action.navigateTo`}
          />
          <ParamsOption type={typeOfNavigate} />
        </GridItem>
      ) : (
        <GridItem>
          <Label>{t('LINK_URL')}</Label>
          <Input
            type="url"
            {...register(`${nameTypeOfButton}.action.url`)}
            placeholder={t('ENTER_LINK_URL')}
            ref={register(`${nameTypeOfButton}.action.url`).ref}
          />
          <ErrorMessageBase
            errors={formState.errors}
            name={`${nameTypeOfButton}.action.url`}
          />
        </GridItem>
      )}
      <div className="my-6 flex items-center gap-3 space-y-0">
        <Controller
          control={control}
          name={`${nameTypeOfButton}.action.isRequireLogin`}
          render={({ field: { onChange, value } }) => (
            <Switch checked={value} onCheckedChange={onChange} />
          )}
        />
        <Label>{t('REQUIRED_LOGIN')}</Label>
      </div>
      <div>
        <MultiLanguageSection
          form={form}
          childrenProps={[
            {
              name: `${nameTypeOfButton}.text`,
              label: t('TEXT'),
            },
          ]}>
          {[<Input key={`${nameTypeOfButton}.text`} />]}
        </MultiLanguageSection>
      </div>
    </Grid>
  );
};
