import { DateTimePicker } from '@/components/btaskee/DateTimePicker';
import { ImageUpload } from '@/components/btaskee/FileUpload';
import { Grid } from '@/components/btaskee/Grid';
import { MultiLanguageSingleSection } from '@/components/btaskee/MultiLanguageSingleSection';
import { MultiSelectAdvance } from '@/components/btaskee/MultiSelectAdvance';
import { useBtaskeeFormController } from '@/components/hooks/useBtaskeeFormController';
import { Button } from '@/components/ui/button';
import { Form, FormField, FormItem } from '@/components/ui/form';
import { FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { useNavigate } from '@remix-run/react';
import {
  DISPLAY_POSITION_OF_COMMUNITY_TAG,
  MAXIMUM_IMAGE_FILE_LENGTH_BTASKEE_PROFILE,
  TAG_COMMUNITY_STATUS,
} from 'btaskee-constants';
import { useDateRangeValidation } from 'btaskee-hooks';
import { XCircle } from 'lucide-react';
import { useEffect } from 'react';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

export interface CommunityTagManagementFormProps {
  translationKey: string;
  defaultValues?: CommunityTagCreationForm;
  isDisabledRangeDate?: boolean;
}

const COMMUNITY_TAG_CREATION_FORM_DEFAULT_VALUE: CommunityTagCreationForm = {
  text: {
    vi: '',
    en: '',
    ko: '',
    th: '',
    id: '',
    ms: '',
  },
  rangeDate: undefined,
  displayPosition: [],
  isPublished: false,
  isAdminOnly: false,
};

const CommunityTagManagementForm = ({
  translationKey,
  isDisabledRangeDate,
  defaultValues = COMMUNITY_TAG_CREATION_FORM_DEFAULT_VALUE,
}: CommunityTagManagementFormProps) => {
  const { t: tCommunityTagForm } = useTranslation(translationKey ?? 'common');
  const dateRangeValidation = useDateRangeValidation();
  const navigate = useNavigate();

  const { form: communityTagForm, onSubmit } =
    useBtaskeeFormController<CommunityTagCreationForm>({
      zodRaw: {
        text: z.object({
          vi: z.string().min(1, tCommunityTagForm('FIELD_IS_REQUIRED')),
          en: z.string().min(1, tCommunityTagForm('FIELD_IS_REQUIRED')),
          ko: z.string().min(1, tCommunityTagForm('FIELD_IS_REQUIRED')),
          th: z.string().min(1, tCommunityTagForm('FIELD_IS_REQUIRED')),
          id: z.string().min(1, tCommunityTagForm('FIELD_IS_REQUIRED')),
          ms: z.string().min(1, tCommunityTagForm('FIELD_IS_REQUIRED')),
        }),
        frame: z
          .custom(file => file instanceof File, {
            message: tCommunityTagForm('IMAGE_IS_REQUIRED'),
          })
          .refine(
            file =>
              file instanceof File &&
              file.size <= MAXIMUM_IMAGE_FILE_LENGTH_BTASKEE_PROFILE.VALUE,
            {
              message: tCommunityTagForm('FILE_SIZE_TOO_LARGE', {
                size: MAXIMUM_IMAGE_FILE_LENGTH_BTASKEE_PROFILE.DISPLAY_TEXT,
              }),
            },
          )
          .or(z.string().url())
          .optional(),
        displayPosition: z
          .array(z.any())
          .min(1, tCommunityTagForm('MUST_HAVE_AT_LEAST_A_DISPLAY_POSITION')),
        rangeDate: z
          .object({
            from: z.date(),
            to: z.date(),
          })
          .nullable()
          .optional(),
        isPublished: z.boolean(),
        isAdminOnly: z.boolean(),
      },
      // TODO: Handle that remove rangeDate in form, need find the better way to handle this
      defaultValues: { ...defaultValues, rangeDate: undefined },
      confirmParams: {
        title: tCommunityTagForm('TAG_LIST_CONFIRM_TITLE'),
        body: tCommunityTagForm('TAG_LIST_CONFIRM_DESCRIPTION'),
        actionButton: tCommunityTagForm('SUBMIT'),
        cancelButton: tCommunityTagForm('CANCEL'),
      },
      formDataProvided: data => {
        const formData = new FormData();

        if (data?.frame instanceof File) formData.append('frame', data.frame);

        if (defaultValues.frame)
          formData.append('frameAlreadyInServer', defaultValues.frame);

        if (data.rangeDate)
          formData.append('rangeDate', JSON.stringify(data.rangeDate));

        formData.append('text', JSON.stringify(data.text));
        formData.append('displayPosition', data.displayPosition?.join(','));
        formData.append('isAdminOnly', data.isAdminOnly?.toString());
        formData.append(
          'status',
          data.isPublished
            ? TAG_COMMUNITY_STATUS.PUBLISHED
            : TAG_COMMUNITY_STATUS.DRAFT,
        );

        return formData;
      },
    });

  useEffect(() => {
    if (defaultValues.rangeDate) {
      communityTagForm.setValue('rangeDate', defaultValues.rangeDate);
    }
    // set default value for rangeDate  in the first time
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Form {...communityTagForm}>
      <form
        onSubmit={communityTagForm.handleSubmit(onSubmit)}
        className="space-y-2">
        <Grid className="gap-6">
          <MultiLanguageSingleSection
            form={communityTagForm}
            defaultValue="content"
            childrenProps={[
              {
                name: 'text',
                label: tCommunityTagForm('TAG_NAME'),
                required: tCommunityTagForm('THIS_FIELD_IS_REQUIRED'),
                layout: 'full',
                type: 'input',
              },
            ]}>
            <Input placeholder={tCommunityTagForm('ENTER_TAG_NAME')} />
          </MultiLanguageSingleSection>
          <div className="grid grid-cols-3 gap-6">
            <div>
              <div className="flex gap-1">
                <DateTimePicker
                  form={communityTagForm}
                  name="rangeDate"
                  label={tCommunityTagForm('START_DATE_AND_END_DATE')}
                  showTime
                  disabled={!!isDisabledRangeDate || !!communityTagForm.watch('isAdminOnly')}
                  formatString="HH:mm dd/MM/yy"
                  rules={dateRangeValidation.bothDatesRequired}
                />
                {!isDisabledRangeDate && !communityTagForm.watch('isAdminOnly') ? (
                  <XCircle
                    className="mb-2 mt-auto min-w-5 cursor-pointer text-xl text-gray-500"
                    onClick={() => communityTagForm.resetField('rangeDate')}
                  />
                ) : null}
              </div>
              <div className="mt-6 flex items-center gap-4">
                <Controller
                  control={communityTagForm.control}
                  name="isPublished"
                  render={({ field: { onChange, value } }) => {
                    return (
                      <Switch checked={value} onCheckedChange={onChange} />
                    );
                  }}
                />
                <Label>{tCommunityTagForm('PUBLISH')}</Label>
              </div>
            </div>
            <div>
              <Grid className="mt-[6px] gap-2">
                <Label>{tCommunityTagForm('DISPLAY_POSITION')}</Label>
                <Controller
                  control={communityTagForm.control}
                  name="displayPosition"
                  render={({ field }) => (
                    <MultiSelectAdvance
                      options={Object.values(
                        DISPLAY_POSITION_OF_COMMUNITY_TAG,
                      ).map(displayPosition => ({
                        label: tCommunityTagForm(displayPosition),
                        value: displayPosition,
                      }))}
                      disabled={!!communityTagForm.watch('isAdminOnly')}
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      placeholder={tCommunityTagForm('CHOOSE_DISPLAY_POSITION')}
                      variant="blue"
                      maxCount={1}
                    />
                  )}
                />
              </Grid>
              <div className="mt-6 flex items-center gap-4">
                <Controller
                  control={communityTagForm.control}
                  name="isAdminOnly"
                  render={({ field: { onChange, value } }) => {
                    return (
                      <Switch
                        checked={value}
                        onCheckedChange={value => {
                          onChange(value);

                          if (value) {
                            communityTagForm.setValue('displayPosition', [
                              DISPLAY_POSITION_OF_COMMUNITY_TAG.FILTER_MENU_PAGE,
                            ]);
                            communityTagForm.trigger('displayPosition');

                            if (!isDisabledRangeDate) {
                              communityTagForm.setValue('rangeDate', undefined);
                              communityTagForm.trigger('rangeDate');
                            }
                          } else {
                            communityTagForm.setValue('rangeDate', defaultValues?.rangeDate ?? undefined);
                            communityTagForm.resetField('displayPosition');
                          }
                        }}
                      />
                    );
                  }}
                />
                <Label>{tCommunityTagForm('ADMIN')}</Label>
              </div>
            </div>
            <FormField
              control={communityTagForm.control}
              name="frame"
              render={({ field }) => (
                <FormItem>
                  <ImageUpload
                    title={tCommunityTagForm('UPDATE_FRAME_IMAGE')}
                    onFileChange={file => field.onChange(file)}
                    ratio={1}
                    imageUrl={
                      defaultValues?.frame
                        ? String(defaultValues.frame)
                        : undefined
                    }
                  />
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <Separator className="w-full" />
          <div className="flex justify-end gap-4">
            <Button
              className="border-primary text-primary hover:bg-primary-foreground hover:text-primary"
              type="button"
              variant="outline"
              onClick={() => navigate(-1)}>
              {tCommunityTagForm('CANCEL')}
            </Button>
            <Button
              variant="default"
              type="submit"
              disabled={!communityTagForm.formState?.isValid}>
              {tCommunityTagForm('SUBMIT')}
            </Button>
          </div>
        </Grid>
      </form>
    </Form>
  );
};

export { CommunityTagManagementForm };
