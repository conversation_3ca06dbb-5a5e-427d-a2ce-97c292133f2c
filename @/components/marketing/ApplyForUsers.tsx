import {
  CAMPAIGN_TARGET_OPTIONS,
  TYPE_OF_TARGET_USER_CAMPAIGN,
} from 'btaskee-constants';
import { Controller, type UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { GridItem } from '../btaskee/GridItem';
import { RadioGroupsBase } from '../btaskee/RadioGroupBase';
import { SelectBase } from '../btaskee/SelectBase';
import { Typography } from '../btaskee/Typography';
import { Card, CardContent, CardHeader } from '../ui/card';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';

export const TargetUserCardContent = ({
  form,
  translationKey = 'common',
}: {
  form: UseFormReturn<MustBeAny>;
  translationKey?: string;
}) => {
  const { t } = useTranslation(translationKey);

  const { control, register, watch } = form;
  const targetUser = watch('targetUser');

  return (
    <Card>
      <CardHeader className="item-centers flex flex-row gap-3 space-y-0 rounded-t-lg bg-gray-50 p-4">
        <Typography variant="p" affects="removePMargin">
          {t('APPLY_FOR_USER')}
        </Typography>
        <Controller
          control={control}
          name="targetUser"
          render={({ field }) => (
            <RadioGroupsBase
              className="flex-row items-center"
              defaultValue={field.value}
              onValueChange={field.onChange}
              options={Object.values(TYPE_OF_TARGET_USER_CAMPAIGN).map(
                target => ({
                  label: t(target),
                  value: target,
                }),
              )}
            />
          )}
        />
      </CardHeader>
      <CardContent className="p-4">
        {targetUser === TYPE_OF_TARGET_USER_CAMPAIGN.TARGET ? (
          <GridItem>
            <Label className="text-gray-700">{t('TARGET')}</Label>
            <Controller
              control={control}
              name="applyForUser.target"
              render={({ field: { onChange, value } }) => (
                <SelectBase
                  defaultValue={value}
                  onValueChange={onChange}
                  options={Object.values(CAMPAIGN_TARGET_OPTIONS).map(
                    target => ({
                      label: t(target),
                      value: target,
                    }),
                  )}
                />
              )}
            />
          </GridItem>
        ) : (
          <GridItem>
            <Label>{t('USER_IDS')}</Label>
            <Textarea
              {...register('applyForUser.userIds')}
              className="min-h-10"
              rows={1}
              placeholder={t('ENTER_USER_ID')}
            />
          </GridItem>
        )}
      </CardContent>
    </Card>
  );
};
