import { Button } from '@/components/ui/button';

interface FormButtonsProps {
  cancelText: string;
  submitText: string;
  onCancel: () => void;
}

const FormSubmissionButton = ({
  cancelText,
  submitText,
  onCancel,
}: FormButtonsProps) => (
  <div className="mt-3 flex justify-end gap-4">
    <Button type="button" variant="outline" onClick={onCancel}>
      {cancelText}
    </Button>
    <Button
      className="bg-primary text-white"
      variant="default"
      type="submit"
      color="primary">
      {submitText}
    </Button>
  </div>
);

export { FormSubmissionButton };
