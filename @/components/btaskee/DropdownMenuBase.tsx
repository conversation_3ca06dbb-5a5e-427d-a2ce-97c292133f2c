import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu';
import { DotsHorizontalIcon } from '@radix-ui/react-icons';
import { Link } from '@remix-run/react';
import React from 'react';
import { cn } from '../../lib/utils';

interface DropdownMenuBaseProps
  extends DropdownMenuPrimitive.DropdownMenuContentProps {
  links: { link: string; label: string }[]; // TODO refactor name, links and link ?
  children?: React.ReactNode;
  btnClassName?: string;
}

const DropdownMenuBase = ({
  links,
  children,
  btnClassName,
  ...props
}: DropdownMenuBaseProps) => (
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      {children || (
        <Button
          variant="ghost"
          className={cn('flex h-8 w-8 p-0 data-[state=open]:bg-muted', btnClassName)}
        >
          <DotsHorizontalIcon className="h-4 w-4" />
          <span className="sr-only">Open menu</span>
        </Button>
      )}
    </DropdownMenuTrigger>
    <DropdownMenuContent {...props}>
      {links.map(({ link, label }) => (
        <Link to={link} key={link} onClick={e => e.stopPropagation()}>
          <DropdownMenuItem>{label}</DropdownMenuItem>
        </Link>
      ))}
    </DropdownMenuContent>
  </DropdownMenu>
);

export { DropdownMenuBase };
