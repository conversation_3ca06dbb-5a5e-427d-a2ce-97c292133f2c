import type { DateRangeProps } from '@/components/btaskee/DateRangePicker';
import TableFilterOnColumn from '@/components/btaskee/TableFilterOnColumn';
import { VectorEmptyDataTable } from '@/components/svg/empty-data-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  type Column,
  type ColumnDef,
  type ColumnFiltersState,
  type ColumnPinningState,
  type SortingState,
  type VisibilityState,
  flexRender,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import type { FlatNamespace } from 'i18next';
import * as React from 'react';
import { useSearchParams } from 'react-router-dom';

import { TableToolbarForBasicPagination } from './TableToolbarForBasicPagination';
import { DataTablePaginationBasic } from './table-data/data-table-pagination-basic';

type CustomColumnDef<TData, TValue> = ColumnDef<TData, TValue> & {
  enableColumnFilter?: boolean;
  dateFilterOptions?: {
    defaultDateRange?: DateRangeProps;
    minDate?: Date;
    maxDate?: Date;
    formatTriggerText?: string;
  };
};

interface DataTableProps<TData, TValue> {
  columns: CustomColumnDef<TData, TValue>[];
  data: TData[];
  manualPagination?: boolean;
  isManualCountSelectedRow?: boolean;
  onClickRow?: (record: TData) => void | Promise<void>;
  extraContent?: React.ReactElement;
  pinColumns?: ColumnPinningState; //Default column def is 150px, using pinning with modified size(column implementation) for better UI
  initialSearchParams?: MustBeAny;
  renderExtraToolbarComponent?: Array<{
    // This is the extra component that will be rendered in the toolbar block
    position: 'left' | 'right';
    component: React.ReactElement;
    initialSearchParams?: MustBeAny; // URLSearchParamsInit;
  }>;
  translationKey?: FlatNamespace;
  searchInput?: {
    name: React.InputHTMLAttributes<HTMLInputElement>['name'];
    defaultValue?: React.InputHTMLAttributes<HTMLInputElement>['defaultValue'];
    placeholder?: React.InputHTMLAttributes<HTMLInputElement>['placeholder'];
    setSearchInputValue?: (
      newValue: React.InputHTMLAttributes<HTMLInputElement>['value'],
    ) => void;
    triggerMode?: MustBeAny; // TSearchInputTriggerMode;
    delayTime?: number;
    customTrigger?: (
      value: React.InputHTMLAttributes<HTMLInputElement>['value'],
    ) => void;
    inputClassName?: React.InputHTMLAttributes<HTMLInputElement>['className'];
  };
  initialFilters?: Array<{
    placeholder: string;
    options: OptionType[];
    name: string;
    value: string;
    className?: React.InputHTMLAttributes<HTMLInputElement>['className'];
  }>;
  initialFilterDate?: {
    name: string;
    mode?: 'range-date' | 'month-year' | 'month'; // Default is range-date
    defaultValue?: {
      from: Date;
      to: Date;
    };
    minDate?: Date;
    maxDate?: Date;
  };
}

export function DataTableBasic<TData, TValue>({
  columns,
  data,
  manualPagination = false,
  isManualCountSelectedRow = false,
  extraContent,
  searchInput,
  translationKey = 'common',
  initialSearchParams = {},
  renderExtraToolbarComponent,
  pinColumns = { left: [], right: [] },
  initialFilters,
  initialFilterDate,
  onClickRow = undefined,
}: DataTableProps<TData, TValue>) {
  const [rowSelection, setRowSelection] = React.useState({});
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [, setSearchParams] = useSearchParams();

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
      ...(pinColumns
        ? {
            columnPinning: {
              left: pinColumns.left ?? [],
              right: pinColumns.right ?? [],
            },
          }
        : {}),
    },
    manualPagination, // true is turn off client-side pagination
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  const getCommonPinningStyles = React.useCallback(
    (column: Column<TData>): React.CSSProperties => {
      const isPinned = column.getIsPinned();
      const isLastLeftPinnedColumn =
        isPinned === 'left' && column.getIsLastColumn('left');
      const isFirstRightPinnedColumn =
        isPinned === 'right' && column.getIsFirstColumn('right');

      return {
        boxShadow: isLastLeftPinnedColumn
          ? '-4px 0 4px -4px gray inset'
          : isFirstRightPinnedColumn
            ? '4px 0 4px -4px gray inset'
            : undefined,
        left: isPinned === 'left' ? `${column.getStart('left')}px` : undefined,
        right:
          isPinned === 'right' ? `${column.getAfter('right')}px` : undefined,
        opacity: isPinned ? 0.95 : 1,
        position: isPinned ? 'sticky' : 'relative',
        width: isPinned ? column.columnDef.size : column.getSize(),
        zIndex: isPinned ? 1 : 0,
        backgroundColor: isPinned ? 'white' : 'inherit',
        minWidth: isPinned ? column.columnDef.size : undefined,
        maxWidth: isPinned ? column.columnDef.size : undefined,
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
      };
    },
    [pinColumns],
  );

  return (
    <div className="space-y-4">
      {searchInput || initialFilters ? (
        <TableToolbarForBasicPagination
          searchInput={searchInput}
          initialFilterDate={initialFilterDate}
          initialFilters={initialFilters}
          table={table}
          total={data?.length ?? 0}
          translationKey={translationKey}
          setSorting={setSorting}
          initialSearchParams={initialSearchParams}
          renderExtraToolbarComponent={renderExtraToolbarComponent}
        />
      ) : null}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow className="bg-gray-100" key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  const isEnableColumnFilter =
                    header.column.columnDef.enableColumnFilter;

                  return (
                    <TableHead
                      style={{
                        ...(pinColumns
                          ? getCommonPinningStyles(header.column)
                          : {}),
                      }}
                      key={header.id}
                      colSpan={header.colSpan}>
                      <div className="flex">
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                        {isEnableColumnFilter ? (
                          <TableFilterOnColumn
                            column={header.column}
                            setSearchParams={setSearchParams}
                          />
                        ) : null}
                      </div>
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow
                  key={row.id}
                  onClick={() => onClickRow?.(row.original)}
                  data-state={row.getIsSelected() && 'selected'}>
                  {row.getVisibleCells().map(cell => (
                    <TableCell
                      key={cell.id}
                      style={{
                        ...(pinColumns
                          ? getCommonPinningStyles(cell.column)
                          : {}),
                      }}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center">
                  <VectorEmptyDataTable className="inline py-12" />
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {extraContent}
      {!manualPagination ? (
        <DataTablePaginationBasic
          isManualCountSelectedRow={isManualCountSelectedRow}
          table={table}
        />
      ) : null}
    </div>
  );
}
