import { Button, buttonVariants } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { momentTz } from 'btaskee-utils';
import {
  CalendarIcon,
  CheckIcon,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import * as React from 'react';
import { useTranslation } from 'react-i18next';

const addMonths = (input: Date, months: number): Date => {
  return momentTz(input).add(months, 'months').toDate();
};

const getDaysInMonth = (year: number, month: number): number =>
  momentTz({ year, month: month - 1 }).daysInMonth();

type Month = {
  number: number;
  name: string;
  yearOffset: number;
};

const MONTHS: Month[][] = [
  [
    { number: 0, name: 'Jan', yearOffset: 0 },
    { number: 1, name: 'Feb', yearOffset: 0 },
    { number: 2, name: 'Mar', yearOffset: 0 },
    { number: 3, name: 'Apr', yearOffset: 0 },
    { number: 0, name: 'Jan', yearOffset: 1 },
    { number: 1, name: 'Feb', yearOffset: 1 },
    { number: 2, name: 'Mar', yearOffset: 1 },
    { number: 3, name: 'Apr', yearOffset: 1 },
  ],
  [
    { number: 4, name: 'May', yearOffset: 0 },
    { number: 5, name: 'Jun', yearOffset: 0 },
    { number: 6, name: 'Jul', yearOffset: 0 },
    { number: 7, name: 'Aug', yearOffset: 0 },
    { number: 4, name: 'May', yearOffset: 1 },
    { number: 5, name: 'Jun', yearOffset: 1 },
    { number: 6, name: 'Jul', yearOffset: 1 },
    { number: 7, name: 'Aug', yearOffset: 1 },
  ],
  [
    { number: 8, name: 'Sep', yearOffset: 0 },
    { number: 9, name: 'Oct', yearOffset: 0 },
    { number: 10, name: 'Nov', yearOffset: 0 },
    { number: 11, name: 'Dec', yearOffset: 0 },
    { number: 8, name: 'Sep', yearOffset: 1 },
    { number: 9, name: 'Oct', yearOffset: 1 },
    { number: 10, name: 'Nov', yearOffset: 1 },
    { number: 11, name: 'Dec', yearOffset: 1 },
  ],
];

type QuickSelector = {
  label: string;
  startMonth: Date;
  endMonth: Date;
  variant?: ButtonVariant;
  onClick?: (selector: QuickSelector) => void;
};

const QUICK_SELECTORS: QuickSelector[] = [
  {
    label: 'THIS_YEAR',
    startMonth: momentTz().startOf('year').toDate(),
    endMonth: momentTz().endOf('month').toDate(),
  },
  {
    label: 'LAST_YEAR',
    startMonth: momentTz().subtract(1, 'year').startOf('year').toDate(),
    endMonth: momentTz().subtract(1, 'year').endOf('year').toDate(),
  },
  {
    label: 'LAST_SIX_MONTHS',
    startMonth: momentTz().subtract(6, 'months').startOf('month').toDate(),
    endMonth: momentTz().endOf('month').toDate(),
  },
  {
    label: 'LAST_TWELVE_MONTHS',
    startMonth: momentTz().subtract(12, 'months').startOf('month').toDate(),
    endMonth: momentTz().endOf('month').toDate(),
  },
];

type MonthRangeCalProps = {
  selectedMonthRange?: { from: Date; to: Date };
  onStartMonthSelect?: (date: Date) => void;
  onMonthRangeSelect?: ({ from, to }: { from: Date; to: Date }) => void;
  callbacks?: {
    yearLabel?: (year: number) => string;
    monthLabel?: (month: Month) => string;
  };
  variant?: {
    calendar?: {
      main?: ButtonVariant;
      selected?: ButtonVariant;
    };
    chevrons?: ButtonVariant;
  };
  minDate?: Date;
  maxDate?: Date;
  quickSelectors?: QuickSelector[];
  showQuickSelectors?: boolean;
};

type ButtonVariant =
  | 'default'
  | 'outline'
  | 'ghost'
  | 'link'
  | 'destructive'
  | 'secondary'
  | null
  | undefined;

function MonthRangePicker({
  onMonthRangeSelect,
  onStartMonthSelect,
  callbacks,
  variant,
  minDate,
  maxDate,
  quickSelectors,
  showQuickSelectors,
  defaultRangeMonth,
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement> &
  Omit<MonthRangeCalProps, 'selectedMonthRange'> & {
    defaultRangeMonth?: { from: Date; to: Date };
  }) {
  const [selectedMonthRange, setSelectedMonthRange] =
    React.useState<MonthRangeCalProps['selectedMonthRange']>(defaultRangeMonth);
  const { t } = useTranslation('common');

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'max-h-8 justify-start px-6 text-left font-normal',
            !selectedMonthRange?.from || !selectedMonthRange.to
              ? 'text-muted-foreground'
              : '',
          )}>
          <CalendarIcon className="mr-2 h-4 w-4" />
          {selectedMonthRange?.from && selectedMonthRange?.to ? (
            `${momentTz(selectedMonthRange.from).format('MMM YYYY')} - ${momentTz(selectedMonthRange.to).format('MMM YYYY')}`
          ) : (
            <span>{t('PICK_RANGE_MONTH')}</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full">
        <div className={cn('p-3', className)} {...props}>
          <div className="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
            <div className="w-full">
              <MonthRangeCal
                onMonthRangeSelect={values => {
                  setSelectedMonthRange(values);
                  return onMonthRangeSelect?.(values);
                }}
                onStartMonthSelect={onStartMonthSelect}
                callbacks={callbacks}
                selectedMonthRange={selectedMonthRange}
                variant={variant}
                minDate={minDate}
                maxDate={maxDate}
                quickSelectors={quickSelectors}
                showQuickSelectors={showQuickSelectors}></MonthRangeCal>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

function MonthRangeCal({
  selectedMonthRange,
  onMonthRangeSelect,
  onStartMonthSelect,
  callbacks,
  variant,
  minDate,
  maxDate,
  quickSelectors = QUICK_SELECTORS,
  showQuickSelectors = true,
}: MonthRangeCalProps) {
  const { t } = useTranslation('common');
  const [selectedOption, setSelectedOption] = React.useState<string>(
    quickSelectors.find(
      quickSelector =>
        momentTz(quickSelector.startMonth).isSame(selectedMonthRange?.from) &&
        momentTz(quickSelector.endMonth).isSame(selectedMonthRange?.to),
    )?.label || '',
  );
  const [startYear, setStartYear] = React.useState<number>(
    momentTz(selectedMonthRange?.from ?? quickSelectors[0].startMonth).year(),
  );
  const [startMonth, setStartMonth] = React.useState<number>(
    momentTz(selectedMonthRange?.from ?? quickSelectors[0].startMonth).month(),
  );
  const [endYear, setEndYear] = React.useState<number>(
    momentTz(selectedMonthRange?.to ?? quickSelectors[0].endMonth).year(),
  );
  const [endMonth, setEndMonth] = React.useState<number>(
    momentTz(selectedMonthRange?.to ?? quickSelectors[0].endMonth).month(),
  );
  const [rangePending, setRangePending] = React.useState<boolean>(false);
  const [endLocked, setEndLocked] = React.useState<boolean>(true);
  const [menuYear, setMenuYear] = React.useState<number>(startYear);

  if (minDate && maxDate && minDate > maxDate) minDate = maxDate;

  return (
    <div className="flex gap-4">
      <div className="min-w-[400px] space-y-4">
        <div className="relative flex items-center justify-evenly pt-1">
          <div className="text-sm font-medium">
            {callbacks?.yearLabel ? callbacks?.yearLabel(menuYear) : menuYear}
          </div>
          <div className="flex items-center space-x-1">
            <button
              onClick={() => {
                setMenuYear(menuYear - 1);
              }}
              className={cn(
                buttonVariants({ variant: variant?.chevrons ?? 'outline' }),
                'absolute left-1 inline-flex h-7 w-7 items-center justify-center p-0',
              )}>
              <ChevronLeft className="h-4 w-4 opacity-50" />
            </button>
            <button
              onClick={() => {
                setMenuYear(menuYear + 1);
              }}
              className={cn(
                buttonVariants({ variant: variant?.chevrons ?? 'outline' }),
                'absolute right-1 inline-flex h-7 w-7 items-center justify-center p-0',
              )}>
              <ChevronRight className="h-4 w-4 opacity-50" />
            </button>
          </div>
          <div className="text-sm font-medium">
            {callbacks?.yearLabel
              ? callbacks?.yearLabel(menuYear + 1)
              : menuYear + 1}
          </div>
        </div>
        <table className="w-full border-collapse space-y-1">
          <tbody>
            {MONTHS.map((monthRow, a) => {
              return (
                <tr key={'row-' + a} className="mt-2 flex w-full">
                  {monthRow.map((m, i) => {
                    return (
                      <td
                        key={m.number + '-' + m.yearOffset}
                        className={cn(
                          cn(
                            cn(
                              cn(
                                '[&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent relative h-10 w-1/4 p-0 text-center text-sm focus-within:relative focus-within:z-20 first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md [&:has([aria-selected].day-range-end)]:rounded-r-md',
                                (menuYear + m.yearOffset > startYear ||
                                  (menuYear + m.yearOffset == startYear &&
                                    m.number > startMonth)) &&
                                  (menuYear + m.yearOffset < endYear ||
                                    (menuYear + m.yearOffset == endYear &&
                                      m.number < endMonth)) &&
                                  (rangePending || endLocked)
                                  ? 'text-accent-foreground bg-accent'
                                  : '',
                              ),
                              menuYear + m.yearOffset == startYear &&
                                m.number == startMonth &&
                                (rangePending || endLocked)
                                ? 'text-accent-foreground bg-accent rounded-l-md'
                                : '',
                            ),
                            menuYear + m.yearOffset == endYear &&
                              m.number == endMonth &&
                              (rangePending || endLocked) &&
                              menuYear + m.yearOffset >= startYear &&
                              m.number >= startMonth
                              ? 'text-accent-foreground bg-accent rounded-r-md'
                              : '',
                          ),
                          i == 3 ? 'mr-2' : i == 4 ? 'ml-2' : '',
                        )}
                        onMouseEnter={() => {
                          if (rangePending && !endLocked) {
                            setEndYear(menuYear + m.yearOffset);
                            setEndMonth(m.number);
                          }
                        }}>
                        <button
                          onClick={() => {
                            setSelectedOption('');
                            if (rangePending) {
                              if (
                                menuYear + m.yearOffset < startYear ||
                                (menuYear + m.yearOffset == startYear &&
                                  m.number < startMonth)
                              ) {
                                setRangePending(true);
                                setEndLocked(false);
                                setStartMonth(m.number);
                                setStartYear(menuYear + m.yearOffset);
                                setEndYear(menuYear + m.yearOffset);
                                setEndMonth(m.number);
                                if (onStartMonthSelect)
                                  onStartMonthSelect(
                                    momentTz({
                                      year: menuYear + m.yearOffset,
                                      month: m.number,
                                    }).toDate(),
                                  );
                              } else {
                                setRangePending(false);
                                setEndLocked(true);
                                if (onMonthRangeSelect)
                                  onMonthRangeSelect({
                                    from: momentTz({
                                      year: startYear,
                                      month: startMonth,
                                    }).toDate(),
                                    to: momentTz({
                                      year: menuYear + m.yearOffset,
                                      month: m.number,
                                    })
                                      .endOf('month')
                                      .toDate(),
                                  });
                              }
                            } else {
                              setRangePending(true);
                              setEndLocked(false);
                              setStartMonth(m.number);
                              setStartYear(menuYear + m.yearOffset);
                              setEndYear(menuYear + m.yearOffset);
                              setEndMonth(m.number);
                              if (onStartMonthSelect)
                                onStartMonthSelect(
                                  momentTz({
                                    year: menuYear + m.yearOffset,
                                    month: m.number,
                                  }).toDate(),
                                );
                            }
                          }}
                          disabled={
                            (maxDate
                              ? menuYear + m.yearOffset >
                                  momentTz(maxDate).year() ||
                                (menuYear + m.yearOffset ==
                                  momentTz(maxDate).year() &&
                                  m.number > momentTz(maxDate).month())
                              : false) ||
                            (minDate
                              ? menuYear + m.yearOffset <
                                  momentTz(minDate).year() ||
                                (menuYear + m.yearOffset ==
                                  momentTz(minDate).year() &&
                                  m.number < momentTz(minDate).month())
                              : false)
                          }
                          className={cn(
                            buttonVariants({
                              variant:
                                (startMonth == m.number &&
                                  menuYear + m.yearOffset == startYear) ||
                                (endMonth == m.number &&
                                  menuYear + m.yearOffset == endYear &&
                                  !rangePending)
                                  ? variant?.calendar?.selected ?? 'default'
                                  : variant?.calendar?.main ?? 'ghost',
                            }),
                            'h-full w-full p-0 font-normal aria-selected:opacity-100',
                          )}>
                          {callbacks?.monthLabel
                            ? callbacks.monthLabel(m)
                            : m.name}
                        </button>
                      </td>
                    );
                  })}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {showQuickSelectors ? (
        <div className=" flex flex-col justify-center gap-1">
          {quickSelectors.map(s => {
            return (
              <Button
                className="mr-auto"
                onClick={() => {
                  setSelectedOption(s.label || '');
                  setStartYear(momentTz(s.startMonth).year());
                  setStartMonth(momentTz(s.startMonth).month());
                  setEndYear(momentTz(s.endMonth).year());
                  setEndMonth(momentTz(s.endMonth).month());
                  setRangePending(false);
                  setEndLocked(true);
                  s.onClick?.(s);

                  if (onMonthRangeSelect)
                    onMonthRangeSelect({
                      from: s.startMonth,
                      to: s.endMonth,
                    });

                  if (
                    s.label === 'LAST_YEAR' ||
                    s.label === 'LAST_SIX_MONTHS' ||
                    s.label === 'LAST_TWELVE_MONTHS'
                  ) {
                    setMenuYear(momentTz().year() - 1);
                  }

                  if (s.label === 'THIS_YEAR') {
                    setMenuYear(momentTz().year());
                  }
                }}
                key={s.label}
                variant={s.variant ?? 'ghost'}>
                <span
                  className={cn(
                    'pr-2 opacity-0',
                    selectedOption === s.label && 'opacity-70',
                  )}>
                  <CheckIcon width={18} height={18} />
                </span>
                {t(s.label)}
              </Button>
            );
          })}
        </div>
      ) : null}
    </div>
  );
}

MonthRangePicker.displayName = 'MonthRangePicker';

function YearRangePicker({
  onYearRangeSelect,
  onStartYearSelect,
  variant,
  minDate,
  maxDate,
  defaultRangeYear,
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  onYearRangeSelect?: ({ from, to }: { from: Date; to: Date }) => void;
  onStartYearSelect?: (date: Date) => void;
  variant?: {
    calendar?: {
      main?: ButtonVariant;
      selected?: ButtonVariant;
    };
    chevrons?: ButtonVariant;
  };
  minDate?: Date;
  maxDate?: Date;
  defaultRangeYear?: { from: Date; to: Date };
}) {
  const [selectedYearRange, setSelectedYearRange] = React.useState<{
    from?: Date;
    to?: Date;
  }>(defaultRangeYear || {});
  const { t } = useTranslation('common');
  const [startYear, setStartYear] = React.useState<number>(
    momentTz().year() - 10,
  );

  const years = React.useMemo(() => {
    const yearsArray = [];
    for (let i = 0; i < 20; i++) {
      yearsArray.push(startYear + i);
    }
    return yearsArray;
  }, [startYear]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'max-h-8 justify-start px-6 text-left font-normal',
            !selectedYearRange?.from || !selectedYearRange.to
              ? 'text-muted-foreground'
              : '',
          )}>
          <CalendarIcon className="mr-2 h-4 w-4" />
          {selectedYearRange?.from && selectedYearRange?.to ? (
            `${momentTz(selectedYearRange.from).format('YYYY')} - ${momentTz(
              selectedYearRange.to,
            ).format('YYYY')}`
          ) : (
            <span>{t('PICK_RANGE_YEAR')}</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px]">
        <div className={cn('p-3', className)} {...props}>
          <div className="flex items-center justify-between mb-4">
            <Button
              variant={variant?.chevrons ?? 'outline'}
              size="icon"
              onClick={() => setStartYear(prev => prev - 20)}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="font-medium">
              {years[0]} - {years[years.length - 1]}
            </span>
            <Button
              variant={variant?.chevrons ?? 'outline'}
              size="icon"
              onClick={() => setStartYear(prev => prev + 20)}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
          <div className="grid grid-cols-4 gap-2">
            {years.map(year => (
              <Button
                key={year}
                variant={
                  selectedYearRange?.from?.getFullYear() === year ||
                  selectedYearRange?.to?.getFullYear() === year
                    ? variant?.calendar?.selected ?? 'default'
                    : variant?.calendar?.main ?? 'outline'
                }
                onClick={() => {
                  if (!selectedYearRange.from) {
                    const from = momentTz({ year }).startOf('year').toDate();
                    setSelectedYearRange({ from });
                    onStartYearSelect?.(from);
                  } else if (!selectedYearRange.to && selectedYearRange.from) {
                    if (year < selectedYearRange.from.getFullYear()) {
                      const from = momentTz({ year }).startOf('year').toDate();
                      setSelectedYearRange({ from });
                      onStartYearSelect?.(from);
                    } else {
                      const to = momentTz({ year }).endOf('year').toDate();
                      setSelectedYearRange(prev => ({ ...prev, to }));
                      onYearRangeSelect?.({
                        from: selectedYearRange.from,
                        to,
                      });
                    }
                  } else {
                    const from = momentTz({ year }).startOf('year').toDate();
                    setSelectedYearRange({ from });
                    onStartYearSelect?.(from);
                  }
                }}
                disabled={
                  (maxDate && year > momentTz(maxDate).year()) ||
                  (minDate && year < momentTz(minDate).year())
                }>
                {year}
              </Button>
            ))}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

YearRangePicker.displayName = 'YearRangePicker';

export { MonthRangePicker, YearRangePicker };
