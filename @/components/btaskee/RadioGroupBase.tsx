import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';
import { RadioGroupProps } from '@radix-ui/react-radio-group';

export interface RadioGroupsProps
  extends Omit<RadioGroupProps, 'onValueChange'> {
  onValueChange: RadioGroupProps['onValueChange'];
  defaultValue: string;
  options: OptionType[];
  className?: string;
  name?: string;
}

const RadioGroupsBase = ({
  onValueChange,
  defaultValue,
  options,
  className,
  name = 'radio-group',
  ...props
}: RadioGroupsProps) => (
  <RadioGroup
    {...props}
    onValueChange={onValueChange}
    defaultValue={defaultValue}
    className={cn('flex flex-col', className)}>
    {options.map((option, index) => {
      const uniqueId = `${name}-${option.value}-${index}`;

      return (
        <div key={uniqueId} className="flex gap-2">
          <RadioGroupItem value={option.value} id={uniqueId} className="mt-1" />
          <Label
            htmlFor={uniqueId}
            className="max-w-96 text-gray-700 leading-normal">
            {option.label}
          </Label>
        </div>
      );
    })}
  </RadioGroup>
);

export { RadioGroupsBase };
