import { cn } from '@/lib/utils';
import { CaretSortIcon, CheckIcon } from '@radix-ui/react-icons';
import React from 'react';
import { ControllerRenderProps, FieldValues, Path } from 'react-hook-form';

import { Button } from '../ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '../ui/command';
import { FormControl } from '../ui/form';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';

interface ControllerComboboxProps<T extends FieldValues> {
  options: { value: string; label: string }[];
  placeholder: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  field: ControllerRenderProps<T, Path<T>>;
}

function ControllerCombobox<T extends FieldValues>({
  options,
  placeholder,
  searchPlaceholder = 'Search...',
  emptyMessage = 'No item found.',
  field,
}: ControllerComboboxProps<T>) {
  const [open, setOpen] = React.useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <FormControl>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
            {...field}
            onClick={() => setOpen(!open)}
            type="button">
            {field.value ? (
              options.find(option => option.value === field.value)?.label
            ) : (
              <span className="font-normal text-gray-400">{placeholder}</span>
            )}
            <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </FormControl>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0">
        <Command>
          <CommandInput placeholder={searchPlaceholder} className="h-9" />
          <CommandList>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup>
              {options.map(option => (
                <CommandItem
                  key={option.value}
                  value={option.label}
                  onSelect={() => {
                    field.onChange(option.value);
                    setOpen(false);
                  }}>
                  {option.label}
                  <CheckIcon
                    className={cn(
                      'ml-auto h-4 w-4',
                      field.value === option.value
                        ? 'opacity-100'
                        : 'opacity-0',
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

export { ControllerCombobox };
