import {
  DateRangePicker,
  DateRangeProps,
} from '@/components/btaskee/DateRangePicker';
import { DebouncedInput } from '@/components/btaskee/DebounceInput';
import { DataTableFacetedFilter } from '@/components/btaskee/table-data/data-table-faceted-filter';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { CalendarIcon } from '@radix-ui/react-icons';
import { useSearchParams } from '@remix-run/react';
import type { Column } from '@tanstack/react-table';
import { momentTz } from 'btaskee-utils';
import type { InputHTMLAttributes } from 'react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { NavigateOptions } from 'react-router';

const TableFilterOnColumn = <TData, TValue>({
  column,
  setSearchParams,
}: {
  column: Column<TData, TValue>;
  setSearchParams: (
    params: (params: URLSearchParams) => URLSearchParams,
    navigateOpts?: NavigateOptions,
  ) => void;
}) => {
  const { t } = useTranslation('common');
  const { filterVariant, filterOptions, dateFilterOptions } =
    column.columnDef.meta ?? {};
  const [searchParams] = useSearchParams();
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  const updateSearchParams = (
    key: Column<TData, TValue>['id'],
    value: InputHTMLAttributes<HTMLInputElement>['value'],
  ) => {
    setSearchParams(
      params => {
        if (params.get(key) !== value?.toString()) {
          params.set(key, value?.toString() ?? '');
        }
        return params;
      },
      { replace: true, preventScrollReset: true },
    );
  };

  const handleDateRangeChange = (
    key: Column<TData, TValue>['id'],
    value: DateRangeProps,
  ) => {
    setSearchParams(
      params => {
        params.set(key, JSON.stringify(value));
        params.delete('pageIndex');
        return params;
      },
      { replace: true, preventScrollReset: true },
    );
    setIsDatePickerOpen(false);
  };

  // Get the current date range value from URL params if it exists
  const getDateRangeFromParams = (key: string) => {
    const dateParam = searchParams.get(key);
    if (dateParam) {
      try {
        return JSON.parse(dateParam) as DateRangeProps;
      } catch (e) {
        return undefined;
      }
    }
    return undefined;
  };

  switch (filterVariant) {
    case 'range':
      return (
        <div className="flex space-x-1">
          <DebouncedInput
            type="number"
            min={0}
            onChange={value => updateSearchParams(`${column.id}_min`, value)}
            placeholder={t('MIN')}
            className="w-[72px] h-8 focus-visible:ring-0 focus-visible:ring-offset-0"
          />
          <DebouncedInput
            type="number"
            min={0}
            onChange={value => updateSearchParams(`${column.id}_max`, value)}
            placeholder={t('MAX')}
            className="w-[72px] h-8 focus-visible:ring-0 focus-visible:ring-offset-0"
          />
        </div>
      );
    case 'select':
      return (
        <DataTableFacetedFilter
          title={t('SELECT')}
          options={filterOptions || []}
          keyFilter={column.id}
          defaultValues={''}
        />
      );
    case 'date':
      // Use params first, then column meta options, then no default selection
      const initialRangeDate =
        getDateRangeFromParams(column.id) ||
        dateFilterOptions?.defaultDateRange ||
        undefined;

      const dateFilterText =
        getDateRangeFromParams(column.id) && initialRangeDate
          ? `${momentTz(initialRangeDate.from).format('DD/MM/YYYY')} - ${momentTz(initialRangeDate.to).format('DD/MM/YYYY')}`
          : '';

      const isFilterActive = !!getDateRangeFromParams(column.id);

      // Determine if the date range matches any preset
      const getPresetFromDateRange = (range?: DateRangeProps) => {
        if (!range) return undefined;

        const from = momentTz(range.from);
        const to = momentTz(range.to);

        // Check if matches THIS_YEAR
        if (
          from.isSame(momentTz().startOf('year'), 'day') &&
          to.isSame(momentTz().endOf('year'), 'day')
        ) {
          return 'THIS_YEAR';
        }

        // Check if matches LAST_YEAR
        if (
          from.isSame(momentTz().subtract(1, 'year').startOf('year'), 'day') &&
          to.isSame(momentTz().subtract(1, 'year').endOf('year'), 'day')
        ) {
          return 'LAST_YEAR';
        }

        // Check if matches THIS_MONTH
        if (
          from.isSame(momentTz().startOf('month'), 'day') &&
          to.isSame(momentTz().endOf('month'), 'day')
        ) {
          return 'THIS_MONTH';
        }

        // Check if matches LAST_MONTH
        if (
          from.isSame(
            momentTz().subtract(1, 'month').startOf('month'),
            'day',
          ) &&
          to.isSame(momentTz().subtract(1, 'month').endOf('month'), 'day')
        ) {
          return 'LAST_MONTH';
        }

        // Check if matches TODAY
        if (
          from.isSame(momentTz().startOf('day'), 'day') &&
          to.isSame(momentTz().endOf('day'), 'day')
        ) {
          return 'TODAY';
        }

        // Check if matches YESTERDAY
        if (
          from.isSame(momentTz().subtract(1, 'day').startOf('day'), 'day') &&
          to.isSame(momentTz().subtract(1, 'day').endOf('day'), 'day')
        ) {
          return 'YESTERDAY';
        }

        // Check if matches LAST_7_DAYS
        if (
          from.isSame(momentTz().subtract(7, 'day').startOf('day'), 'day') &&
          to.isSame(momentTz().endOf('day'), 'day')
        ) {
          return 'LAST_7_DAYS';
        }

        // Check if matches LAST_30_DAYS
        if (
          from.isSame(momentTz().subtract(30, 'day').startOf('day'), 'day') &&
          to.isSame(momentTz().endOf('day'), 'day')
        ) {
          return 'LAST_30_DAYS';
        }

        // Check other presets as needed...

        return undefined;
      };

      const selectedPreset = getPresetFromDateRange(initialRangeDate);

      return (
        <Popover open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                'h-5 w-5 p-0 inline-flex items-center justify-center rounded-full transition-colors duration-200 hover:bg-gray-100',
                isFilterActive
                  ? 'text-blue-500 bg-blue-50 hover:bg-blue-100 shadow-sm'
                  : 'text-gray-400 hover:text-gray-600',
              )}
              onClick={() => setIsDatePickerOpen(true)}
              title={dateFilterText || t('SELECT_DATE_RANGE')}>
              <CalendarIcon
                className={cn(
                  'h-3.5 w-3.5 transition-transform duration-200',
                  isDatePickerOpen && 'scale-110',
                )}
              />
              {isFilterActive && (
                <span className="sr-only">Date filter is active</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="w-auto p-0 rounded-lg shadow-lg border border-gray-200 overflow-hidden"
            align="start"
            sideOffset={5}>
            <div className="bg-white">
              {isFilterActive && (
                <div className="bg-blue-50 px-3 py-2 text-xs text-blue-600 font-medium border-b border-gray-100 flex justify-between items-center">
                  <div className="flex items-center">
                    <CalendarIcon className="h-3.5 w-3.5 mr-2" />
                    {selectedPreset ? t(selectedPreset) : dateFilterText}
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-5 w-5 p-0 text-gray-400 hover:text-red-500 rounded-full"
                    onClick={() => {
                      setSearchParams(
                        params => {
                          params.delete(column.id);
                          params.delete('pageIndex');
                          return params;
                        },
                        { replace: true, preventScrollReset: true },
                      );
                      setIsDatePickerOpen(false);
                    }}
                    title={t('CLEAR_FILTER')}>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round">
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </Button>
                </div>
              )}
              <DateRangePicker
                className="w-full"
                initialRangeDate={initialRangeDate}
                onUpdate={value => handleDateRangeChange(column.id, value)}
                formatDateTriggerButtonText={
                  dateFilterOptions?.formatTriggerText || 'DD/MM/YYYY'
                }
                align="start"
                defaultRangeDateOptions={selectedPreset}
              />
            </div>
          </PopoverContent>
        </Popover>
      );
    default:
      return (
        <DebouncedInput
          type="search"
          className={'h-8'}
          onChange={value => updateSearchParams(column.id, value)}
          placeholder={t('SEARCH')}
        />
      );
  }
};

export default TableFilterOnColumn;
