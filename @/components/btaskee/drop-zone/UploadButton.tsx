import { But<PERSON> } from "btaskee-ui"
import { UploadCloud } from "lucide-react"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { BtaskeeDropzone, BtaskeeDropzoneProps } from "./BtaskeeDropzone"

type OutsideProps = Omit<BtaskeeDropzoneProps, 'isOpen' | 'handleToggle'>

type Props = OutsideProps & {
    isOpen?: boolean
    handleToggleUploadMedia?: () => void
}

const UploadButton = (props: Props) => {
    const { t } = useTranslation('common')

    const [isOpen, setIsOpen] = useState(false)

    const handleToggle = () => {
        setIsOpen(!isOpen)
        props.handleToggleUploadMedia?.()
    }

    return (
        <>
            <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleToggle}
                className="bg-white"
            >
                <UploadCloud className="mr-2 h-4 w-4" />
                {t('UPLOAD_FILE')}
            </Button>

            <BtaskeeDropzone
                {...props}
                maxFiles={props?.maxFiles || Infinity}
                isOpen={isOpen}
                handleToggle={handleToggle}
            />
        </>
    )
}

export {
    UploadButton
}