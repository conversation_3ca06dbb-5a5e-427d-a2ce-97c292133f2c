import { Slot } from "@/components/btaskee/drop-zone/BtaskeeDropzone";
import { FileActions } from "@/components/btaskee/drop-zone/FileActions";
import { FileIcon, FileTextIcon } from "lucide-react";

type OtherFilePreviewProps = {
  file: Slot;
  onDelete: (e: any) => void;
};

export const OtherFilePreview = ({ file, onDelete }: OtherFilePreviewProps) => {
  const getFileIcon = () => {
    if (file.type === 'pdf') {
      return <FileTextIcon className="h-10 w-10 text-red-500" />;
    }
    return <FileIcon className="h-10 w-10 text-blue-500" />;
  };

  return (
    <div className="relative w-full h-full group">
      <div className="w-full h-full bg-white rounded-lg border border-gray-200 p-4 flex flex-col items-center justify-center shadow-sm">
        {getFileIcon()}
        <span className="mt-3 text-sm font-medium text-gray-700 text-center line-clamp-2">{file.name}</span>
      </div>
      <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
        <FileActions onDelete={onDelete} />
      </div>
    </div>
  );
}; 