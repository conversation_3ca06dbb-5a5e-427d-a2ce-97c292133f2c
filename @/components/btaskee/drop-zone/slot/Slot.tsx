import { AlertCircle, Plus, UploadIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

type SlotFileEmptyProps = {
    onClick: () => void;
};

type SlotFileErrorProps = {
    error: string;
    onClick: (e: any) => void;
};

type SlotFilePlusProps = {
    onClick: () => void;
};

export const SlotFileEmpty = ({ onClick }: SlotFileEmptyProps) => {
    const { t: tCommon } = useTranslation("common");

    return (
        <div
            onClick={onClick}
            className="flex flex-col items-center justify-center h-full w-full p-4 bg-gray-50 pointer-events-none"
        >
            <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center mb-4 shadow-sm pointer-events-none">
                <UploadIcon className="h-6 w-6 text-gray-400 pointer-events-none" />
            </div>
            <p className="text-sm text-gray-500 text-center mb-3 pointer-events-none">
                {tCommon("CLICK_TO_UPLOAD")}
            </p>
            <p className="text-xs text-gray-400 text-center pointer-events-none">
                {tCommon("OR_DRAG_AND_DROP")}
            </p>
        </div>
    );
};

export const SlotFileError = ({ error, onClick }: SlotFileErrorProps) => {
    const { t: tCommon } = useTranslation("common");

    return (
        <div className="relative flex flex-col items-center justify-center h-full w-full bg-red-50 p-4">
            <AlertCircle className="h-8 w-8 text-red-500 mb-2" />
            <div className="text-sm text-red-600 text-center">
                {error}
            </div>
            <button
                onClick={(e) => {
                    e.stopPropagation();
                    onClick(e);
                }}
                className="mt-3 px-3 py-1 bg-red-100 hover:bg-red-200 text-red-700 rounded-full text-xs"
            >
                {tCommon("TRY_AGAIN")}
            </button>
        </div>
    );
};

export const SlotFilePlus = ({ onClick }: SlotFilePlusProps) => {
    const { t: tCommon } = useTranslation("common");

    return (
        <div
            onClick={onClick}
            className="aspect-square rounded-lg flex flex-col items-center justify-center cursor-pointer 
        transition-all duration-200 h-[180px] border-2 border-dashed border-orange-300 
        bg-orange-50/50 hover:border-orange-400 hover:bg-orange-50"
            style={{ boxSizing: "border-box" }} // Ensures border is included in dimensions
        >
            <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center mb-3 shadow-sm">
                <Plus className="h-6 w-6 text-orange-500" />
            </div>
            <span className="text-sm text-orange-600">{tCommon("ADD_ROW")}</span>
        </div>
    );
}; 