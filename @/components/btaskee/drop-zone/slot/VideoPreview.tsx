import {
  FileActions,
  FileActionsProps,
} from '@/components/btaskee/drop-zone/FileActions';
import { FileInfo } from '@/components/btaskee/drop-zone/FileInfo';
import { Play } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

import { MediaPreview } from './ImagePreview';

type OutSideProps = Required<
  Pick<FileActionsProps, 'onFullscreen' | 'onDelete'>
>;

type VideoFilePreviewProps = OutSideProps & {
  file: MediaPreview;
};

export const VideoFilePreview = (props: VideoFilePreviewProps) => {
  const { file, onFullscreen, onDelete } = props;

  const [videoThumbnail, setVideoThumbnail] = useState<string | null>(null);
  const [videoDuration, setVideoDuration] = useState<number | null>(null);
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [isHovered, setIsHovered] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Format seconds to MM:SS display format
  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Video metadata extraction and thumbnail generation pipeline
  // Technical challenge: extract frame from video without showing video player to user
  useEffect(() => {
    if (file) {
      setVideoUrl(file.url);

      // Create invisible video element for metadata extraction (not added to DOM)
      const video = document.createElement('video');
      video.preload = 'metadata'; // Load only metadata, not full video
      video.src = file.url;

      video.onloadedmetadata = () => {
        setVideoDuration(video.duration);

        // Seek to 1 second to avoid common black/loading frame at video start
        // This timing works for most video formats and provides a representative frame
        video.currentTime = 1;

        video.onseeked = () => {
          // Canvas-based thumbnail generation: capture video frame as static image
          const canvas = document.createElement('canvas');
          canvas.width = video.videoWidth; // Use video's native resolution
          canvas.height = video.videoHeight;
          const ctx = canvas.getContext('2d');

          if (ctx) {
            // Draw current video frame to canvas
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            // Convert canvas to base64 JPEG for immediate use as img src
            const thumbnailUrl = canvas.toDataURL('image/jpeg');
            setVideoThumbnail(thumbnailUrl);
          }
        };
      };

      // Cleanup: prevent memory leaks when component unmounts or file changes
      return () => {
        setVideoUrl(null);
        setVideoThumbnail(null);
        setVideoDuration(null);
      };
    }
  }, [file]);

  if (!videoUrl) {
    return null;
  }

  return (
    <div className="relative w-full h-full group">
      <div className="w-full h-full bg-white rounded-lg border border-gray-200 shadow-sm">
        <video
          ref={videoRef}
          src={videoUrl}
          className="w-full h-full object-cover rounded-lg"
          muted
        />
      </div>
      <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex flex-col justify-between">
        <FileActions onFullscreen={onFullscreen} onDelete={onDelete} />
        <div className="absolute bottom-0 left-0 right-0">
          <FileInfo fileName={file.name} fileSize={file.size} />
        </div>
      </div>
    </div>
  );
};
