import { FileActions, FileActionsProps } from "@/components/btaskee/drop-zone/FileActions";
import { FileInfo } from "@/components/btaskee/drop-zone/FileInfo";
import { Slot } from "../BtaskeeDropzone";

type OutSideProps = Required<Pick<FileActionsProps, 'onFullscreen' | 'onDelete'>> & Pick<FileActionsProps, 'onEdit'>

export type MediaPreview = Required<Omit<Slot, 'error' | 'file'>> & Pick<Slot, 'file'>

type ImageFilePreviewProps = OutSideProps & {
    file: MediaPreview
};

export const ImageFilePreview = (props: ImageFilePreviewProps) => {
    const { file, onFullscreen, onEdit, onDelete } = props;

    return (
        <div className="group relative w-full h-full">
            <div className="w-full h-full bg-white rounded-lg border border-gray-200 shadow-sm">
                <img
                    src={file.url}
                    alt={file.name}
                    className="w-full h-full object-cover rounded-lg"
                    loading="lazy"
                />
            </div>
            <div
                className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center"
            >
                <FileActions
                    onFullscreen={onFullscreen}
                    onEdit={onEdit}
                    onDelete={onDelete}
                />
                <div className="absolute bottom-0 left-0 right-0">
                    <FileInfo fileName={file.name} fileSize={file.size} />
                </div>
            </div>
        </div>
    );
}; 