import { Edit2, Maximize2, Trash2 } from "lucide-react";

export type FileActionsProps = {
    onFullscreen?: (e: any) => void;
    onEdit?: (e: any) => void;
    onDelete?: (e: any) => void;
};

export const FileActions = ({ onFullscreen, onEdit, onDelete }: FileActionsProps) => {
    return (
        <div className="p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 absolute top-1 right-1">
            <div className="flex gap-2">
                {onFullscreen && (
                    <button
                        type="button"
                        onClick={(e) => {
                            e.stopPropagation();
                            onFullscreen(e);
                        }}
                        className="p-1.5 bg-white/90 rounded-full hover:bg-white transition-colors"
                    >
                        <Maximize2 className="h-4 w-4 text-gray-700" />
                    </button>
                )}
                {onEdit && (
                    <button
                        type="button"
                        onClick={(e) => {
                            e.stopPropagation();
                            onEdit(e);
                        }}
                        className="p-1.5 bg-white/90 rounded-full hover:bg-white transition-colors"
                    >
                        <Edit2 className="h-4 w-4 text-gray-700" />
                    </button>
                )}
                {onDelete && (
                    <button
                        type="button"
                        onClick={(e) => {
                            e.stopPropagation();
                            onDelete(e);
                        }}
                        className="p-1.5 bg-white/90 rounded-full hover:bg-white transition-colors"
                    >
                        <Trash2 className="h-4 w-4 text-red-500" />
                    </button>
                )}
            </div>
        </div>
    );
}; 