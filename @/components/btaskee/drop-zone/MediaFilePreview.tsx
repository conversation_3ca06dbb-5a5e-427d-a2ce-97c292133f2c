import { Dialog, DialogContent, DialogDescription, Di<PERSON><PERSON>eader, Di<PERSON>Title, <PERSON><PERSON><PERSON> } from "btaskee-ui"
import { formatBytes } from "btaskee-utils"
import debounce from 'lodash/debounce.js'
import {
    ChevronLeft,
    ChevronRight,
    Crop,
    Download,
    File,
    FileText,
    Image,
    Info,
    Maximize2,
    Pause,
    Play,
    Share2,
    Video,
    Volume2,
    VolumeX,
    X
} from "lucide-react"
import { useCallback, useEffect, useMemo, useRef, useState } from "react"
import { MediaPreview } from "./slot/ImagePreview"
import { cn } from "@/lib/utils"
import { useTranslation } from "react-i18next"

export type MediaFilePreviewProps = {
    media: MediaPreview[]
    initialIndex?: number
    open: boolean
    onClose: () => void
    onCrop?: (index: number) => void
}

const MediaFilePreview = ({ media, initialIndex = 0, open, onClose, onCrop }: MediaFilePreviewProps) => {
    const { t: tCommon } = useTranslation("common");

    // Internal state
    const [currentIndex, setCurrentIndex] = useState(initialIndex)
    const [currentMedia, setCurrentMedia] = useState<MediaPreview | null>(null)
    const [showControls, setShowControls] = useState(true)
    const [showInfo, setShowInfo] = useState(false)
    const [isVideoLoading, setIsVideoLoading] = useState(false)
    const [isVideoReady, setIsVideoReady] = useState(false)
    const [isFullscreen, setIsFullscreen] = useState(false)
    const [error, setError] = useState<string | null>(null)

    // Video player state
    const [isPlaying, setIsPlaying] = useState(false)
    const [currentTime, setCurrentTime] = useState(0)
    const [duration, setDuration] = useState(0)
    const [isMuted, setIsMuted] = useState(false)
    const [volume, setVolume] = useState(1)

    // Refs
    const videoRef = useRef<HTMLVideoElement>(null)
    const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null)
    const containerRef = useRef<HTMLDivElement>(null)

    // Memoized values
    const validMedia = useMemo(() => media.filter(m => m.url), [media])
    const hasNext = useMemo(() => validMedia.length > 1 && currentIndex < validMedia.length - 1, [validMedia, currentIndex])
    const hasPrev = useMemo(() => validMedia.length > 1 && currentIndex > 0, [validMedia, currentIndex])

    // Circular search for next valid media item with URL
    const findValidMediaIndex = useCallback(
        (startIndex: number, direction: "next" | "prev"): number => {
            if (validMedia.length === 0) return -1

            let index = startIndex
            let count = 0

            // Prevent infinite loop by limiting iterations to array length
            while (count < validMedia.length) {
                if (direction === "next") {
                    index = (index + 1) % validMedia.length
                } else {
                    // Handle negative modulo for reverse direction
                    index = (index - 1 + validMedia.length) % validMedia.length
                }

                if (validMedia[index]?.url) {
                    return index
                }

                count++
            }

            return -1
        },
        [validMedia],
    )

    // Media initialization with fallback strategy for invalid indices
    // Business rule: always show a valid media item, even if initialIndex is invalid
    useEffect(() => {
        if (open && validMedia.length > 0) {
            // First attempt: use provided initialIndex if it points to valid media
            if (initialIndex >= 0 && initialIndex < validMedia.length && validMedia[initialIndex]?.url) {
                setCurrentIndex(initialIndex)
                setCurrentMedia(validMedia[initialIndex])
            } else {
                // Fallback: search for next valid media using circular algorithm
                const validIndex = findValidMediaIndex(initialIndex, "next")
                if (validIndex !== -1) {
                    setCurrentIndex(validIndex)
                    setCurrentMedia(validMedia[validIndex])
                } else {
                    // Edge case: no valid media found (shouldn't happen with validMedia filter)
                    setCurrentMedia(null)
                }
            }
        }

        // Reset all player state when dialog opens/closes or media changes
        setIsPlaying(false)
        setCurrentTime(0)
        setDuration(0)
        setShowControls(true)
        setShowInfo(false)
        setError(null)
    }, [open, validMedia, initialIndex, findValidMediaIndex])

    // Auto-hide video controls after 3 seconds of mouse inactivity
    useEffect(() => {
        if (!open || currentMedia?.type !== "video") return

        // Debounced mouse handler to prevent excessive timeout resets
        const handleMouseMove = debounce(() => {
            setShowControls(true)

            if (controlsTimeoutRef.current) {
                clearTimeout(controlsTimeoutRef.current)
            }

            // Hide controls only during playback to avoid UX issues
            controlsTimeoutRef.current = setTimeout(() => {
                if (isPlaying) {
                    setShowControls(false)
                }
            }, 3000)
        }, 100)

        document.addEventListener("mousemove", handleMouseMove)

        return () => {
            document.removeEventListener("mousemove", handleMouseMove)
            if (controlsTimeoutRef.current) {
                clearTimeout(controlsTimeoutRef.current)
            }
            handleMouseMove.cancel()
        }
    }, [open, isPlaying, currentMedia])

    // Media navigation with state cleanup and auto-play logic
    // Business rule: seamless navigation between media items with proper state transitions
    const navigateToMedia = useCallback(
        (direction: "next" | "prev") => {
            // Always pause current video before navigation to prevent audio overlap
            if (videoRef.current) {
                videoRef.current.pause()
                setIsPlaying(false)
            }

            const nextIndex = findValidMediaIndex(currentIndex, direction)
            if (nextIndex !== -1) {
                const nextMedia = validMedia[nextIndex]
                setCurrentIndex(nextIndex)
                setCurrentMedia(nextMedia)

                // Complete state reset for clean transition between different media types
                setCurrentTime(0)
                setDuration(0)
                setShowInfo(false)
                setIsVideoLoading(true)
                setIsVideoReady(false)
                setError(null)

                // UX enhancement: auto-play videos for continuous viewing experience
                // Handle async play() promise to gracefully handle autoplay restrictions
                if (nextMedia.type === 'video' && videoRef.current) {
                    videoRef.current.play().catch(err => {
                        setError('Failed to play video')
                        console.error('Video play error:', err)
                    })
                    setIsPlaying(true)
                }
            }
        },
        [currentIndex, validMedia, findValidMediaIndex],
    )

    // Handle video errors
    const handleVideoError = useCallback((e: any) => {
        setError('Failed to load video')
        console.error('Video error:', e)
        setIsVideoLoading(false)
        setIsVideoReady(false)
    }, [])

    // Handle image errors
    const handleImageError = useCallback((e: any) => {
        setError('Failed to load image')
        console.error('Image error:', e)
    }, [])

    // Toggle fullscreen
    const toggleFullscreen = useCallback(() => {
        if (!containerRef.current) return

        if (!document.fullscreenElement) {
            containerRef.current.requestFullscreen().catch(err => {
                setError('Failed to enter fullscreen mode')
                console.error('Fullscreen error:', err)
            })
            setIsFullscreen(true)
        } else {
            document.exitFullscreen().catch(err => {
                setError('Failed to exit fullscreen mode')
                console.error('Fullscreen error:', err)
            })
            setIsFullscreen(false)
        }
    }, [])

    // Handle fullscreen change
    useEffect(() => {
        const handleFullscreenChange = () => {
            setIsFullscreen(!!document.fullscreenElement)
        }

        document.addEventListener('fullscreenchange', handleFullscreenChange)
        return () => {
            document.removeEventListener('fullscreenchange', handleFullscreenChange)
        }
    }, [])

    // Toggle info panel
    const toggleInfo = useCallback((e: any) => {
        e.preventDefault()
        setShowInfo(prev => !prev)
    }, [])

    // Toggle play/pause
    const togglePlay = useCallback((e: any) => {
        e.preventDefault()
        if (!videoRef.current) return

        if (isPlaying) {
            videoRef.current.pause()
            setIsPlaying(false)
        } else {
            videoRef.current.play().catch(err => {
                setError('Failed to play video')
                console.error('Video play error:', err)
            })
            setIsPlaying(true)
        }
    }, [isPlaying])

    // Toggle mute
    const toggleMute = useCallback((e: any) => {
        e.preventDefault()
        if (!videoRef.current) return

        videoRef.current.muted = !isMuted
        setIsMuted(prev => !prev)
    }, [isMuted])

    // Handle volume change
    const handleVolumeChange = useCallback((value: number[]) => {
        if (!videoRef.current) return

        const newVolume = value[0]
        videoRef.current.volume = newVolume
        setVolume(newVolume)
        setIsMuted(newVolume === 0)
    }, [])

    // Handle time update
    const handleTimeUpdate = useCallback((e: any) => {
        if (!videoRef.current) return

        setCurrentTime(videoRef.current.currentTime)
    }, [])

    // Handle loaded metadata
    const handleLoadedMetadata = useCallback((e: any) => {
        if (!videoRef.current) return

        setDuration(videoRef.current.duration)
    }, [])

    // Handle can play
    const handleCanPlay = useCallback((e: any) => {
        setIsVideoReady(true)
        setIsVideoLoading(false)
    }, [])

    // Handle seek
    const handleSeek = useCallback((value: number[]) => {
        if (!videoRef.current) return

        videoRef.current.currentTime = value[0]
        setCurrentTime(value[0])
    }, [])

    // Format time
    const formatTime = useCallback((time: number): string => {
        const minutes = Math.floor(time / 60)
        const seconds = Math.floor(time % 60)
        return `${minutes}:${seconds.toString().padStart(2, '0')}`
    }, [])

    // Get file icon
    const getFileIcon = useCallback((type?: string) => {
        switch (type) {
            case "image":
                return <Image className="w-6 h-6" />
            case "video":
                return <Video className="w-6 h-6" />
            case "pdf":
                return <FileText className="w-6 h-6" />
            default:
                return <File className="w-6 h-6" />
        }
    }, [])

    // Programmatic file download using temporary anchor element technique
    // Handles both blob URLs and external URLs with proper security attributes
    const handleDownload = useCallback((e: any) => {
        e.preventDefault()
        if (!currentMedia?.url) return

        // Create temporary anchor element to trigger download
        const link = document.createElement('a')
        link.href = currentMedia.url
        link.download = currentMedia.name // Suggest filename to browser
        link.target = '_blank' // Fallback for URLs that can't be downloaded
        link.rel = 'noopener noreferrer' // Security: prevent window.opener access
        document.body.appendChild(link)
        link.click() // Programmatically trigger download
        document.body.removeChild(link) // Clean up DOM
    }, [currentMedia])

    // Memoized JSX
    const renderMedia = useMemo(() => {
        if (!currentMedia?.url) return null

        switch (currentMedia.type) {
            case "image":
                return (
                    <img
                        src={currentMedia.url}
                        alt={currentMedia.name}
                        className="max-w-full max-h-full object-contain"
                        onError={handleImageError}
                    />
                )
            case "video":
                return (
                    <video
                        ref={videoRef}
                        src={currentMedia.url}
                        className="max-w-full max-h-full"
                        onError={handleVideoError}
                        onTimeUpdate={handleTimeUpdate}
                        onLoadedMetadata={handleLoadedMetadata}
                        onCanPlay={handleCanPlay}
                    />
                )
            default:
                return (
                    <div className="flex flex-col items-center justify-center h-full">
                        {getFileIcon(currentMedia.type)}
                        <p className="mt-2 text-sm text-gray-500">{currentMedia.name}</p>
                    </div>
                )
        }
    }, [currentMedia, handleImageError, handleVideoError, handleTimeUpdate, handleLoadedMetadata, handleCanPlay, getFileIcon])

    // Count valid media
    const validMediaCount = validMedia.length

    // Calculate 1-based position for user display (e.g., "3 of 10")
    // Uses URL matching since media objects may be recreated but URLs remain stable
    const getCurrentMediaNumber = useCallback(() => {
        if (!currentMedia?.url) return 0

        // Find position by URL comparison (more reliable than object reference)
        const index = validMedia.findIndex((item) => item.url === currentMedia.url)
        return index + 1 // Convert to 1-based indexing for user display
    }, [validMedia, currentMedia])

    // If there are no valid media or the dialog is closed, don't render
    if (!open || !currentMedia?.url) return null

    const isVideo = currentMedia.type === "video"
    const isImage = currentMedia.type === 'image'

    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent
                className="max-w-6xl p-0 bg-white border-0 rounded-lg overflow-hidden shadow-xl [&>button]:hidden"
                onClick={(e) => e.stopPropagation()}
            >
                <DialogHeader className="hidden">
                    <DialogTitle />
                    <DialogDescription />
                </DialogHeader>

                <div className="relative w-full h-full flex flex-col">
                    {/* Top toolbar - clearly separated */}
                    <div className="bg-gray-100 border-b border-gray-200 p-3 flex items-center justify-between">
                        <div className="text-gray-700 text-sm font-medium hidden sm:block max-w-[300px] truncate items-center gap-2">
                            {currentMedia.name || "Unnamed file"}
                        </div>

                        <div className="flex items-center gap-2">
                            {validMediaCount > 1 && (
                                <div className="bg-white rounded-full px-3 py-1.5 text-gray-700 text-xs border border-gray-200">
                                    {getCurrentMediaNumber()} / {validMediaCount}
                                </div>
                            )}

                            <button
                                onClick={toggleFullscreen}
                                className="p-2 bg-white rounded-full hover:bg-gray-200 transition-colors border border-gray-200"
                                title="Fullscreen (F)"
                            >
                                <Maximize2 className="h-5 w-5 text-gray-700" />
                            </button>

                            <button
                                onClick={toggleInfo}
                                className={cn(
                                    "p-2 rounded-full transition-colors",
                                    showInfo
                                        ? "bg-gray-200 text-gray-800"
                                        : "bg-white hover:bg-gray-200 text-gray-700 border border-gray-200",
                                )}
                                title="File Info (I)"
                            >
                                <Info className="h-5 w-5" />
                            </button>

                            {onCrop && currentMedia.type === "image" && (
                                <button
                                    onClick={() => onCrop(currentIndex)}
                                    className="p-2 bg-white rounded-full hover:bg-gray-200 transition-colors border border-gray-200"
                                    title="Crop (C)"
                                >
                                    <Crop className="h-5 w-5 text-gray-700" />
                                </button>
                            )}

                            <button
                                onClick={handleDownload}
                                className="p-2 bg-white rounded-full hover:bg-gray-200 transition-colors border border-gray-200"
                                title="Download (D)"
                            >
                                <Download className="h-5 w-5 text-gray-700" />
                            </button>

                            <button
                                onClick={onClose}
                                className="p-2 bg-white rounded-full hover:bg-gray-200 transition-colors"
                                title="Close (Esc)"
                            >
                                <X className="h-5 w-5 text-gray-700" />
                            </button>

                        </div>
                    </div>

                    {/* Media container with light background */}
                    <div
                        ref={containerRef}
                        className={cn("relative flex items-center justify-center bg-gray-50 flex-1", {
                            'min-h-[60vh]': !isVideo,
                            'min-h-[50vh]': isVideo,
                            'bg-black': isFullscreen,
                        })}
                    >
                        {isVideo ? (
                            // Video player
                            <div className="relative w-full h-full flex flex-col items-center justify-center">
                                <video
                                    ref={videoRef}
                                    src={currentMedia.url || undefined}
                                    className={cn(
                                        "max-h-[80vh] max-w-full object-contain transition-opacity duration-300",
                                        isVideoReady ? "opacity-100" : "opacity-0",
                                        isFullscreen && "max-h-none max-w-none w-full h-full"
                                    )}
                                    onClick={togglePlay}
                                    onTimeUpdate={handleTimeUpdate}
                                    onLoadedMetadata={handleLoadedMetadata}
                                    onCanPlay={handleCanPlay}
                                    playsInline
                                    preload="auto"
                                />

                                {!isVideoReady && (
                                    <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
                                        <div className="w-16 h-16 rounded-full border-4 border-gray-200 border-t-gray-600 animate-spin" />
                                    </div>
                                )}

                                {/* Video controls overlay - at bottom */}
                                <div
                                    className={cn(
                                        "absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4 transition-opacity duration-300",
                                        showControls ? "opacity-100" : "opacity-0 pointer-events-none",
                                    )}
                                >
                                    {/* Progress bar */}
                                    <div className="mb-2">
                                        <Slider
                                            value={[currentTime]}
                                            min={0}
                                            max={duration || 100}
                                            step={0.1}
                                            onValueChange={handleSeek}
                                            className="cursor-pointer"
                                        />
                                    </div>

                                    {/* Controls */}
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                            <button
                                                onClick={togglePlay}
                                                className="p-2 bg-white/20 rounded-full hover:bg-white/30 transition-colors"
                                                title={isPlaying ? "Pause (Space)" : "Play (Space)"}
                                            >
                                                {isPlaying ? (
                                                    <Pause className="h-5 w-5 text-white" />
                                                ) : (
                                                    <Play className="h-5 w-5 text-white ml-0.5" />
                                                )}
                                            </button>

                                            <div className="text-white text-xs">
                                                {formatTime(currentTime)} / {formatTime(duration)}
                                            </div>
                                        </div>

                                        <div className="flex items-center gap-3">
                                            <div className="flex items-center gap-2 w-24">
                                                <button
                                                    onClick={toggleMute}
                                                    className="p-1 rounded-full hover:bg-white/20 transition-colors"
                                                    title="Mute (M)"
                                                >
                                                    {isMuted ? (
                                                        <VolumeX className="h-4 w-4 text-white" />
                                                    ) : (
                                                        <Volume2 className="h-4 w-4 text-white" />
                                                    )}
                                                </button>
                                                <Slider
                                                    value={[isMuted ? 0 : volume]}
                                                    min={0}
                                                    max={1}
                                                    step={0.1}
                                                    onValueChange={handleVolumeChange}
                                                    className="cursor-pointer"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Play button overlay (when paused) */}
                                {!isPlaying && (
                                    <div
                                        className="absolute inset-0 flex items-center justify-center cursor-pointer"
                                        onClick={togglePlay}
                                    >
                                        <div className="w-16 h-16 rounded-full bg-black/30 flex items-center justify-center">
                                            <Play className="h-8 w-8 text-white ml-1" />
                                        </div>
                                    </div>
                                )}
                            </div>
                        ) : (
                            // Image or other file types
                            <div className="relative w-full h-full flex flex-col items-center justify-center">
                                {isImage ? (
                                    // Image display
                                    <img
                                        src={currentMedia.url || "/placeholder.svg"}
                                        alt={currentMedia.name || "Image preview"}
                                        className={cn(
                                            "max-h-[80vh] max-w-full object-contain",
                                            isFullscreen && "max-h-none max-w-none w-full h-full"
                                        )}
                                    />
                                ) : (
                                    // Other file types
                                    <div className="flex flex-col items-center justify-center gap-4">
                                        {getFileIcon(currentMedia.type)}
                                        <div className="text-gray-700 text-sm font-medium text-center max-w-[300px] break-words">
                                            {currentMedia.name || "Unnamed file"}
                                        </div>
                                        {currentMedia.size && (
                                            <div className="text-gray-500 text-xs">
                                                {formatBytes(currentMedia.size)}
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        )}

                        {/* Navigation buttons */}
                        {validMediaCount > 1 && (
                            <>
                                <button
                                    onClick={() => navigateToMedia("prev")}
                                    className={cn(
                                        "absolute left-4 p-3 bg-white rounded-full hover:bg-gray-200 transition-all border border-gray-200 shadow-sm",
                                        isVideo && !showControls ? "opacity-0 pointer-events-none" : "opacity-100",
                                    )}
                                    aria-label="Previous media"
                                >
                                    <ChevronLeft className="h-6 w-6 text-gray-700" />
                                </button>
                                <button
                                    onClick={() => navigateToMedia("next")}
                                    className={cn(
                                        "absolute right-4 p-3 bg-white rounded-full hover:bg-gray-200 transition-all border border-gray-200 shadow-sm",
                                        isVideo && !showControls ? "opacity-0 pointer-events-none" : "opacity-100",
                                    )}
                                    aria-label="Next media"
                                >
                                    <ChevronRight className="h-6 w-6 text-gray-700" />
                                </button>
                            </>
                        )}
                    </div>

                    {/* File info panel - slides in from right */}
                    {showInfo && (
                        <div className="absolute top-[60px] right-0 bottom-0 w-72 bg-white border-l border-gray-200 p-4 animate-in slide-in-from-right duration-300 shadow-md">
                            <h3 className="text-gray-800 font-medium mb-4 flex items-center gap-2">
                                <Info className="h-4 w-4" /> {tCommon("FILE_INFORMATION")}
                            </h3>

                            <div className="space-y-4 text-sm">
                                <div>
                                    <p className="text-gray-500 mb-1">{tCommon("NAME")}</p>
                                    <p className="text-gray-900 break-words">{currentMedia.name || tCommon("UNNAMED_FILE")}</p>
                                </div>

                                {currentMedia.size && (
                                    <div>
                                        <p className="text-gray-500 mb-1">{tCommon("SIZE")}</p>
                                        <p className="text-gray-900">{formatBytes(currentMedia.size)}</p>
                                    </div>
                                )}

                                <div>
                                    <p className="text-gray-500 mb-1">{tCommon("TYPE")}</p>
                                    <p className="text-gray-900 capitalize">{currentMedia.type || "Unknown"}</p>
                                </div>

                                {isVideo && duration > 0 && (
                                    <div>
                                        <p className="text-gray-500 mb-1">{tCommon("DURATION")}</p>
                                        <p className="text-gray-900">{formatTime(duration)}</p>
                                    </div>
                                )}

                                <div className="pt-4 flex gap-2">
                                    <button
                                        onClick={handleDownload}
                                        className="flex items-center gap-1 px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded-md text-gray-700 text-xs transition-colors"
                                    >
                                        <Download className="h-3.5 w-3.5" /> {tCommon("DOWNLOAD")}
                                    </button>

                                    <button className="flex items-center gap-1 px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded-md text-gray-700 text-xs transition-colors">
                                        <Share2 className="h-3.5 w-3.5" /> {tCommon("SHARE")}
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    )
}

export { MediaFilePreview }
