import { But<PERSON>, cn, Dialog, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, ScrollArea } from 'btaskee-ui';
import { getFileType, getPropertiesFileFromTheLastPartOfS3Url, getTheLastPartOfUrl } from 'btaskee-utils';
import flattenDeep from "lodash/flattenDeep.js";
import isNull from "lodash/isNull.js";
import isNumber from "lodash/isNumber.js";
import uniqueId from 'lodash/uniqueId.js';
import { useEffect, useRef, useState } from "react";
import { DropzoneOptions, FileRejection, useDropzone } from "react-dropzone";
import { useTranslation } from "react-i18next";
import { MediaFile, MediaType } from '../MediaUpload';
import { ImageCropper } from './ImageCropper';
import { ImageFilePreview, MediaPreview } from './slot/ImagePreview';
import { OtherFilePreview } from './slot/OtherPreview';
import { <PERSON>lotFileEmpty, SlotFileError, SlotFilePlus } from './slot/Slot';
import { VideoFilePreview } from './slot/VideoPreview';
import { MediaFilePreview } from './MediaFilePreview';

const IMAGE_PREFIX = 'image/'
const UNKNOWN_TYPE = 'unknown'

export type Slot = {
    id: string,
    file?: File,
    error?: string,
    isOld?: boolean,
    url?: string,
    type: ReturnType<typeof getFileType>,
    name: string,
    size: number
}

type MediaFileExtended = MediaFile & {
    size: number
    name: string
    isOld: boolean
}

export type Slots = Array<Slot | null>

type SelectedFile = Required<Pick<Slot, 'url' | 'name'>> & {
    mode: 'fullscreen' | 'edit' | 'delete' | 'play',
} | null

export type BtaskeeDropzoneProps = Pick<DropzoneOptions, 'accept' | 'maxFiles' | 'maxSize' | 'minSize' | 'multiple'> & {
    initSlots?: Array<Required<Pick<Slot, 'url'>> & Partial<Slot>>
    handleUploadFile: (
        payload: {
            mediaFiles: MediaFileExtended[],
            mediaFilesByType: Record<ReturnType<typeof getFileType>, MediaFileExtended[]>
        }) => void
    isOpen: boolean
    handleToggle: () => void
    messageNotes?: Partial<{
        fileType: string,
        fileSize: string,
        maxFiles: string,
    }>
    handleRemoveSlot?: (slot: Slot) => void
}

const BtaskeeDropzone = (props: BtaskeeDropzoneProps) => {
    const { handleUploadFile, maxFiles = Infinity, initSlots = [], isOpen, handleToggle, messageNotes, handleRemoveSlot, ...dropzoneProps } = props

    const { t: tCommon } = useTranslation("common")

    const [selectedSlotIndex, setSelectedSlotIndex] = useState<number | null>(null)
    const [slots, setSlots] = useState<Slots>(() => {

        // Convert initial URLs to slot objects with extracted metadata
        const convertedInitSlot = initSlots.map<Required<Omit<Slot, 'error' | 'file'>>>(slot => {
            const { extension, fileName } = getPropertiesFileFromTheLastPartOfS3Url(getTheLastPartOfUrl(slot.url))
            return {
                ...slot,
                id: uniqueId('slot-'),
                isOld: true,
                name: slot?.name ?? fileName,
                type: slot?.type ?? extension as ReturnType<typeof getFileType>,
                size: slot?.size ?? 0,
                url: slot.url
            }
        })

        // Pad with null slots to maintain 3-column grid alignment
        // Formula: if we have 1 file, add 2 nulls; if 2 files, add 1 null; if 3+ files, add 0 nulls
        return [...convertedInitSlot, ...Array(2 - (initSlots.length % 3)).fill(null)]
    })
    const [selectedFile, setSelectedFile] = useState<SelectedFile>(null)

    // Track object URLs to prevent memory leaks
    const objectUrlsRef = useRef<Set<string>>(new Set());

    // Cleanup all object URLs on component unmount
    useEffect(() => {
        return () => {
            objectUrlsRef.current.forEach(removeObjectUrl);
            objectUrlsRef.current.clear();
        };
    }, []);

    // Create and track object URL for file preview
    const createObjectUrl = (file: File): string => {
        const url = URL.createObjectURL(file);
        objectUrlsRef.current.add(url);
        return url;
    };

    // Remove object URL and clean up memory
    const removeObjectUrl = (url: string) => {
        objectUrlsRef.current.delete(url);
        URL.revokeObjectURL(url);
    }

    const handleOpenCropper = (selectedFile: SelectedFile, slotIndex: number) => {
        setSelectedFile(selectedFile)
        setSelectedSlotIndex(slotIndex)
    }

    const getPropertyFileFromFile = (file: File) => {
        return {
            name: file.name,
            type: getFileType(file),
            size: file.size,
        }
    }

    const { getRootProps, getInputProps, open, isDragActive } = useDropzone({
        ...dropzoneProps,
        noClick: true,
        noKeyboard: true,
        maxFiles,
        onDragEnter: (event) => {
            const { slotIndex } = handleGetSlotSpace(event)
            if (!isNull(slotIndex)) setSelectedSlotIndex(+slotIndex)
        },
        onDragLeave: () => {
            setSelectedSlotIndex(null)
        },
        onDrop: (acceptedFiles: File[], rejectedFiles: FileRejection[], event: any) => {
            if (!acceptedFiles.length && !rejectedFiles.length) return;

            // Convert dropped files to slot objects with preview URLs
            let targetFiles = [
                ...acceptedFiles.map<Slot>(file => ({
                    id: uniqueId('slot-'),
                    file,
                    url: createObjectUrl(file),
                    ...getPropertyFileFromFile(file)
                })),
                ...rejectedFiles.map<Slot>(file => ({
                    id: uniqueId('slot-'),
                    error: file.errors[0].message,
                    ...getPropertyFileFromFile(file.file)
                }))
            ]

            let amountOfHasFileInSlots = slots.filter(Boolean).length

            let updated: any[] = slots.slice(0, amountOfHasFileInSlots);

            const { slotIndex } = handleGetSlotSpace(event)
            const isHasFileInSlot = !!updated[slotIndex]

            // Place files in target slot or append to end
            updated[isHasFileInSlot ? slotIndex : amountOfHasFileInSlots] = targetFiles.map(file => ({
                ...file,
                isOld: false
            }))
            updated = flattenDeep(updated)

            // Auto-open cropper for single non-GIF images
            if (
                targetFiles.length === 1 &&
                !targetFiles[0]?.error &&
                (targetFiles[0]?.file as File)?.type.startsWith(IMAGE_PREFIX) &&
                !(targetFiles[0]?.file as File)?.type.endsWith('gif')
            ) {
                handleOpenCropper(
                    {
                        url: URL.createObjectURL(targetFiles[0].file as File),
                        name: (targetFiles[0].file as File).name,
                        mode: 'edit',
                    },
                    isHasFileInSlot ? slotIndex : updated.length - 1
                )
            }

            if (updated.length < slots.length) {
                updated.push(...Array(slots.length - updated.length).fill(null))
            }

            let currentSizeSlot = updated.length;

            // Complex grid layout algorithm: ensure we always have complete rows of 3
            // When currentSizeSlot is divisible by 3, we need to add slots for the next row
            if (currentSizeSlot % 3 === 0) {
                let remainder = (currentSizeSlot + 1) % 3; // Position in next row after adding one slot
                let restOfSlotsInRow = 3 - remainder; // How many more slots needed to complete the row
                let maxSlotsShouldHas = maxFiles - Math.min(maxFiles, remainder + restOfSlotsInRow);
                updated.push(...Array(Math.min(maxSlotsShouldHas, restOfSlotsInRow)).fill(null));
            }

            setSlots(updated.slice(0, Math.min(maxFiles, updated.length)));
        },
    })

    // Extract slot index from drag event target or closest drag space element
    const handleGetSlotSpace = (event: any) => {
        let slotIndex = null
        let slotSpace = null
        const isDragSpaceElement = event?.target?.dataset?.isDragSpace
        if (isDragSpaceElement) {
            slotIndex = event?.target?.dataset?.slotIndex
            slotSpace = event?.target
        } else {
            slotIndex = event?.target?.closest('[data-is-drag-space="true"]')?.dataset?.slotIndex
            slotSpace = event?.target?.closest('[data-is-drag-space="true"]')
        }
        return { slotIndex: +slotIndex, slotSpace }
    }

    const handleSlotClick = (index: number) => {
        setSelectedSlotIndex(index)
        open()
    }

    // Dynamic row expansion: calculate how many slots to add based on grid math
    // Business rule: always maintain 3-column layout, never exceed maxFiles limit
    const addNewRow = () => {
        let currentSlots = slots?.length;
        let maxSlots = maxFiles - currentSlots; // Available slots before hitting limit
        let remainder = (currentSlots + 1) % 3; // Position after adding one slot
        let restOfSlotsInRow = 3 - remainder; // Slots needed to complete current row
        setSlots([...slots, ...Array(Math.min(maxSlots, restOfSlotsInRow)).fill(null)])
    }

    const handleActionFile = (mode: 'fullscreen' | 'edit' | 'delete' | 'play', position: { id: string, index: number }) => (e: any) => {
        e.stopPropagation();

        const { id, index } = position;

        const slot = slots[index];
        const file = slot?.file;
        const isFile = !!file;
        const url = slot?.url;
        const isUrl = !!url;
        const name = slot?.name;

        if (!isUrl || !name) return;

        if (mode === 'edit' && !isFile) return;

        if (mode === 'delete') {
            removeObjectUrl(url)
            setSlots(pre => pre.filter(slot => slot?.id !== id))
            handleRemoveSlot?.(slot)
            return;
        }

        setSelectedSlotIndex(index);
        setSelectedFile({
            url,
            name,
            mode,
        });
    }

    // Type guard to ensure slot has no errors and is valid for upload
    const isMakeSureFileNotError = (slot: Slot): slot is Required<Omit<Slot, 'error' | 'file'>> & Pick<Slot, 'file'> => {
        return !slot.error
    }

    // Upload pipeline: transform slot data into API-ready format with type categorization
    // Business logic: group files by type for backend processing, handle both File objects and URLs
    const onUpload = () => {
        // Map internal file types to API expected categories
        const matchingMediaType: Record<string, string> = {
            image: 'images',
            video: 'videos',
        }

        const mediaFilesByType: Record<string, MediaFileExtended[]> = {
            images: [],
            videos: [],
        };
        const mediaFiles: MediaFileExtended[] = [];
        const markSlotsAsOld: Slots = []

        // Transform valid slots into upload-ready MediaFile objects
        slots.forEach(slot => {
            if (!slot) return;
            if (!isMakeSureFileNotError(slot)) return;

            // Handle unknown file types by falling back to UNKNOWN_TYPE category
            let typeKey = matchingMediaType?.[slot.type] ?? UNKNOWN_TYPE;

            // Dynamically create type categories for unknown file types
            if (!mediaFilesByType[typeKey]) mediaFilesByType[typeKey] = []

            // Create MediaFile with either File object (new uploads) or URL string (existing files)
            const mediaFile = {
                file: slot?.file || slot.url, // Polymorphic: File for new uploads, string URL for existing
                preview: slot.url,
                type: slot.type as MediaType,
                size: slot.size,
                name: slot.name,
                isOld: slot.isOld,
            };

            mediaFilesByType[typeKey].push(mediaFile);
            mediaFiles.push(mediaFile);

            // Mark slot as "old" to prevent duplicate uploads in future operations
            markSlotsAsOld.push({ ...slot, isOld: true })
        });

        // State transition: mark all files as uploaded to prevent re-upload
        setSlots(markSlotsAsOld);

        handleUploadFile({
            mediaFiles,
            mediaFilesByType,
        });
        handleToggle()
    };

    const onClosePreviewAndCropper = () => {
        setSelectedFile(null)
        setSelectedSlotIndex(null)
    }

    const onCloseUploadDialogAndRevertToOldFile = () => {
        handleToggle()
        onRevertToOldFile()
    }

    // Revert to previously uploaded files and clean up new file URLs
    const onRevertToOldFile = () => {
        // Clean up object URLs for new files to prevent memory leaks
        slots.forEach(slot => {
            if (slot?.file && !slot.isOld) {
                const url = URL.createObjectURL(slot.file);
                URL.revokeObjectURL(url);
                objectUrlsRef.current.delete(url);
            }
        });

        // Revert state: filter out new uploads, keep only previously uploaded files
        // Apply same grid padding logic to maintain 3-column layout consistency
        setSlots(prevSlots => {
            const oldSlots = prevSlots.filter(slot => slot?.isOld);

            // Recalculate grid padding after filtering - same formula as initialization
            return [...oldSlots, ...Array(2 - (oldSlots.length % 3)).fill(null)];
        });
    };

    return (
        <>
            <Dialog open={isOpen} onOpenChange={onCloseUploadDialogAndRevertToOldFile}>
                <DialogContent
                    className="sm:max-w-2xl max-h-[90vh] overflow-y-auto"
                    onInteractOutside={(e) => {
                        e.preventDefault();
                    }}
                >
                    <DialogHeader>
                        <DialogTitle className="text-xl font-semibold">{tCommon("UPLOAD_FILE")}</DialogTitle>
                    </DialogHeader>

                    <div className="space-y-4">
                        {/* Simple Instructions */}
                        <p className="text-sm text-gray-600">
                            {tCommon("UPLOAD_FILE_INSTRUCTION")}
                        </p>
                        <div className="text-xs text-gray-500 space-y-1">
                            {messageNotes?.maxFiles && <div>• {tCommon("MAX_FILES", { maxFiles: messageNotes.maxFiles })}</div>}
                            {messageNotes?.fileSize && <div>• {tCommon("MAX_FILE_SIZE", { maxFileSize: messageNotes.fileSize })}</div>}
                            {messageNotes?.fileType && <div>• {tCommon("ALLOWED_FILE_TYPES", { fileTypes: messageNotes.fileType })}</div>}
                        </div>

                        {/* Dropzone area */}
                        <div {...getRootProps()} className="outline-none">
                            <ScrollArea
                                className={cn('h-[200px] ', {
                                    'h-[400px]': slots.length > 3,
                                })}
                            >
                                <div className="grid grid-cols-3 gap-4 mx-0">
                                    {slots.map((slot, index) => (
                                        <div
                                            key={index}
                                            onClick={() => handleSlotClick(index)}
                                            className={cn(
                                                "aspect-square rounded-lg overflow-hidden",
                                                "flex flex-col items-center justify-center cursor-pointer",
                                                "transition-all duration-200 h-[180px]",
                                                {
                                                    'shadow-sm hover:shadow': slot?.file,
                                                    'border-2 border-dashed border-gray-300 hover:border-orange-400': !slot?.file,
                                                    'border-2 border-red-500': slot?.error,
                                                    'border-2 border-orange-500 border-dashed': isDragActive && selectedSlotIndex === index
                                                }
                                            )}
                                            style={{ boxSizing: "border-box" }}
                                            data-is-drag-space='true'
                                            data-slot-index={index}
                                            title='Click to upload file'
                                        >
                                            {!slot ? (
                                                <SlotFileEmpty onClick={() => handleSlotClick(index)} />
                                            ) : (
                                                isMakeSureFileNotError(slot) ? (
                                                    slot.type === 'image' ? (
                                                        <ImageFilePreview
                                                            file={slot}
                                                            onFullscreen={handleActionFile('fullscreen', { id: slot.id, index })}
                                                            onEdit={slot.file ? handleActionFile('edit', { id: slot.id, index }) : undefined}
                                                            onDelete={handleActionFile('delete', { id: slot.id, index })}
                                                        />
                                                    ) : slot.type === 'video' ? (
                                                        <VideoFilePreview
                                                            file={slot}
                                                            onFullscreen={handleActionFile('play', { id: slot.id, index })}
                                                            onDelete={handleActionFile('delete', { id: slot.id, index })}
                                                        />
                                                    ) : (
                                                        <OtherFilePreview file={slot} onDelete={handleActionFile('delete', { id: slot.id, index })} />
                                                    )
                                                ) : (
                                                    <SlotFileError
                                                        error={slot.error as string}
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            handleSlotClick(index);
                                                        }}
                                                    />
                                                )
                                            )}
                                        </div>
                                    ))}

                                    {/* Add Row button */}
                                    {slots?.length < maxFiles && (
                                        <SlotFilePlus onClick={addNewRow} />
                                    )}
                                </div>
                            </ScrollArea>

                            {/* Hidden input */}
                            <input
                                {...getInputProps()}
                                data-is-drag-space='true'
                                data-slot-index={selectedSlotIndex}
                            />
                        </div>
                    </div>

                    <DialogFooter className="flex items-center justify-between sm:justify-between mt-4 pt-4 border-t">
                        <div className="text-sm text-gray-500">
                            {tCommon("SLOTS_FILLED", { filled: slots.filter(slot => slot && isMakeSureFileNotError(slot)).length, max: slots.length })}
                        </div>
                        <div className="flex gap-2">
                            <Button variant="outline" onClick={onCloseUploadDialogAndRevertToOldFile}>
                                {tCommon("CANCEL")}
                            </Button>
                            <Button
                                className="bg-orange-400 hover:bg-orange-500"
                                onClick={onUpload}
                            >
                                {tCommon("CONFIRM_UPLOAD")}
                            </Button>
                        </div>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {selectedFile && selectedFile.mode === 'edit' && isNumber(selectedSlotIndex) &&
                <ImageCropper
                    imageName={selectedFile.name}
                    imageUrl={selectedFile.url}
                    onCropComplete={({ file, url }) => {
                        setSlots(pre => {
                            const updated = [...pre]
                            updated[selectedSlotIndex] = {
                                ...updated[selectedSlotIndex] as Slot,
                                file,
                                url,
                                ...getPropertyFileFromFile(file),
                            }
                            return updated
                        })
                        onClosePreviewAndCropper()
                    }}
                    onCancel={onClosePreviewAndCropper}
                    open={true}
                />}

            {selectedSlotIndex !== null &&
                <MediaFilePreview
                    initialIndex={selectedSlotIndex}
                    media={slots.filter(media => !!media && isMakeSureFileNotError(media)) as MediaPreview[]}
                    open={!!selectedFile && selectedFile?.mode === 'fullscreen'}
                    onClose={onClosePreviewAndCropper}
                />}
        </>
    )
}

export {
    BtaskeeDropzone
};

