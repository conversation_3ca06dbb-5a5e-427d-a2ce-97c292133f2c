import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle, Select, SelectContent, SelectItem, SelectTrigger, SelectValue, Slider } from "btaskee-ui"
import { getCroppedImg } from "btaskee-utils"
import { t } from "i18next"
import { Check, CropIcon, RotateCw, Undo2, X, ZoomIn } from "lucide-react"
import type React from "react"
import { useCallback, useEffect, useMemo, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import type { Crop } from "react-image-crop"
import ReactCrop, { centerCrop, makeAspectCrop } from "react-image-crop"

export type ImageCropperProps = {
    imageName: string
    imageUrl: string
    onCropComplete: (payload: { file: File; url: string }) => void
    onCancel: () => void
    open: boolean
}

const NO_ASPECT_RATIO = 'no-aspect-ratio'

// Calculate centered crop area with specific aspect ratio
function centerAspectCrop(mediaWidth: number, mediaHeight: number, aspect: number) {
    return centerCrop(
        makeAspectCrop(
            {
                unit: "%",
                width: 90,
            },
            aspect,
            mediaWidth,
            mediaHeight,
        ),
        mediaWidth,
        mediaHeight,
    )
}

function ImageCropper({ imageName, imageUrl, onCropComplete, onCancel, open }: ImageCropperProps) {
    const { t: tCommon } = useTranslation("common");

    const ASPECT_RATIOS = [
        { label: tCommon("NO_ASPECT_RATIO"), value: NO_ASPECT_RATIO },
        { label: `1:1 ${tCommon("SQUARE")}`, value: 1 },
        { label: "4:3", value: 4 / 3 },
        { label: "16:9", value: 16 / 9 },
        { label: "2:3", value: 2 / 3 },
        { label: "3:4", value: 3 / 4 },
    ]

    const [crop, setCrop] = useState<Crop>()
    const [rotation, setRotation] = useState(0)
    const [scale, setScale] = useState(1)
    const [completedCrop, setCompletedCrop] = useState<Crop | null>(null)
    const [selectedAspectRatio, setSelectedAspectRatio] = useState<typeof ASPECT_RATIOS[number]["value"]>(NO_ASPECT_RATIO)
    const imgRef = useRef<HTMLImageElement>(null)

    // Check if free-form cropping is enabled (no aspect ratio constraint)
    const isInValidAspectRatio = useMemo(() => {
        return selectedAspectRatio === NO_ASPECT_RATIO || !selectedAspectRatio
    }, [selectedAspectRatio])

    // Reactive crop recalculation when image loads or aspect ratio changes
    // Business rule: maintain crop area proportions when switching between aspect ratios
    useEffect(() => {
        if (imgRef.current) {
            const { width, height } = imgRef.current
            setCrop(
                isInValidAspectRatio
                    ? { unit: 'px', x: 0, y: 0, width, height } // Free-form: select entire image
                    : centerAspectCrop(width, height, selectedAspectRatio as number) // Constrained: center crop with ratio
            )
            // Reset transformations to baseline when image/ratio changes
            setRotation(0)
            setScale(1)
        }
    }, [imageUrl, selectedAspectRatio])

    // Image load handler: initialize crop area based on actual image dimensions
    // Critical for responsive cropping - must wait for image to load to get real dimensions
    const onImageLoad = useCallback(
        (e: React.SyntheticEvent<HTMLImageElement>) => {
            const { width, height } = e.currentTarget // Get actual rendered dimensions
            setCrop(
                isInValidAspectRatio
                    ? { unit: 'px', x: 0, y: 0, width, height } // Full image selection
                    : centerAspectCrop(width, height, selectedAspectRatio as number) // Centered with aspect ratio
            )
        },
        [selectedAspectRatio],
    )

    const handleRotationChange = (value: number[]) => {
        setRotation(value[0])
    }

    const handleScaleChange = (value: number[]) => {
        setScale(value[0] / 100 + 0.5) // Convert 0-100 slider to 0.5-1.5 scale range
    }

    const resetCrop = () => {
        if (imgRef.current) {
            const { width, height } = imgRef.current
            setCrop(isInValidAspectRatio
                ? { unit: 'px', x: 0, y: 0, width, height }
                : centerAspectCrop(width, height, selectedAspectRatio as number)
            )
            setRotation(0)
            setScale(1)
        }
    }

    // Process crop area and generate final cropped image file
    const handleCropComplete = async () => {
        if (!imgRef.current || !completedCrop) return

        const croppedFile = await getCroppedImg(imgRef.current, completedCrop, rotation, scale, imageName)

        if (croppedFile) {
            onCropComplete(croppedFile)
        }
    }

    // Keyboard shortcuts for crop operations
    const handleKeyDown = useCallback(
        (e: React.KeyboardEvent) => {
            if (e.key === "Escape") {
                onCancel()
            } else if (e.key === "Enter") {
                handleCropComplete()
            } else if (e.key === "r" || e.key === "R") {
                resetCrop()
            }
        },
        [onCancel],
    )

    if (!open) return null

    return (
        <Dialog open={open} onOpenChange={() => onCancel()}>
            <DialogContent
                className="sm:max-w-4xl max-h-[90vh] overflow-hidden p-0 gap-0 bg-gray-50"
                onKeyDown={handleKeyDown}
                onInteractOutside={(e) => {
                    e.preventDefault(); // Prevent closing on outside click
                }}
            >
                <DialogHeader className="p-4 border-b bg-white">
                    <DialogTitle className="text-xl font-semibold flex items-center">
                        <CropIcon className="h-5 w-5 mr-2" /> {tCommon("CROP_IMAGE")}
                    </DialogTitle>
                </DialogHeader>

                <div className="flex flex-col md:flex-row h-full">
                    <div className="flex-1 p-4 flex items-center justify-center bg-[#1a1a1a] overflow-hidden">
                        <ReactCrop
                            crop={crop}
                            onChange={(c) => setCrop(c)}
                            onComplete={(c) => setCompletedCrop(c)}
                            aspect={isInValidAspectRatio ? undefined : selectedAspectRatio as number}
                            className="max-h-[60vh] mx-auto"
                        >
                            <img
                                ref={imgRef}
                                src={imageUrl || "/placeholder.svg"}
                                alt="Crop preview"
                                style={{
                                    transform: `scale(${scale}) rotate(${rotation}deg)`,
                                    transformOrigin: "center",
                                    maxHeight: "60vh",
                                }}
                                onLoad={onImageLoad}
                                className="max-w-full"
                            />
                        </ReactCrop>
                    </div>

                    <div className="w-full md:w-64 p-4 border-t md:border-t-0 md:border-l bg-white">
                        <div className="space-y-6">
                            <div>
                                <h3 className="text-sm font-medium mb-2">{tCommon("ADJUSTMENTS")}</h3>
                                <div className="space-y-4">
                                    <div className="space-y-2">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-2">
                                                <RotateCw className="h-4 w-4 text-gray-500" />
                                                <span className="text-sm">{tCommon("ROTATION")}</span>
                                            </div>
                                            <span className="text-xs text-gray-500">{rotation}°</span>
                                        </div>
                                        <Slider value={[rotation]} min={0} max={360} step={1} onValueChange={handleRotationChange} />
                                    </div>

                                    <div className="space-y-2">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center space-x-2">
                                                <ZoomIn className="h-4 w-4 text-gray-500" />
                                                <span className="text-sm">{tCommon("ZOOM")}</span>
                                            </div>
                                            {/* Display zoom as percentage relative to base scale */}
                                            <span className="text-xs text-gray-500">{Math.round((scale - 0.5) * 100)}%</span>
                                        </div>
                                        <Slider
                                            value={[(scale - 0.5) * 100]}
                                            min={0}
                                            max={100}
                                            step={1}
                                            onValueChange={handleScaleChange}
                                        />
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h3 className="text-sm font-medium mb-2">{tCommon("ASPECT_RATIO")}</h3>
                                <Select
                                    value={selectedAspectRatio.toString()}
                                    onValueChange={(value) => setSelectedAspectRatio(value as typeof ASPECT_RATIOS[number]["value"])}
                                >
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder={tCommon("CHOOSE_ASPECT_RATIO")} />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {ASPECT_RATIOS.map((ratio) => (
                                            <SelectItem key={ratio.label} value={ratio.value.toString()}>
                                                {ratio.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div>
                                <h3 className="text-sm font-medium mb-2">{tCommon("ACTIONS")}</h3>
                                <div className="grid grid-cols-2 gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={resetCrop}
                                        className="flex items-center justify-center"
                                        title={tCommon("RESET")}
                                    >
                                        <Undo2 className="h-4 w-4 mr-1" /> {tCommon("RESET")}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <DialogFooter className="p-4 border-t bg-white">
                    <Button variant="outline" onClick={onCancel} className="gap-1" title={tCommon("CANCEL")}>
                        <X className="h-4 w-4" /> {tCommon("CANCEL")}
                    </Button>
                    <Button
                        onClick={handleCropComplete}
                        className="bg-orange-500 hover:bg-orange-600 gap-1"
                        title={tCommon("APPLY_CROP")}
                    >
                        <Check className="h-4 w-4" /> {tCommon("APPLY_CROP")}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

export {
    ImageCropper
}
