import { formatBytes } from "btaskee-utils";

type FileInfoProps = {
    fileName: string;
    fileSize: number;
};

export const FileInfo = ({ fileName, fileSize }: FileInfoProps) => {
    return (
        <div className="p-2 transform translate-y-full group-hover:translate-y-0 transition-transform duration-200 bg-black/50">
            <div className="text-xs text-white truncate">{fileName}</div>
            <div className="text-[10px] text-white/80">{formatBytes(fileSize)}</div>
        </div>
    );
}; 