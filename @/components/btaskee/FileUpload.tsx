import { Typography } from '@/components/btaskee/Typography';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { UploadCloud } from 'lucide-react';
import React from 'react';
import { useTranslation } from 'react-i18next';

export const ALLOWABLE_IMAGE_SIZE = 64; // Image size allow 64Kb;
export interface ImageUploadProps {
  onFileChange: (
    file: File | null,
    previewUrl: string | null,
    fileName: string,
  ) => void;
  title?: string;
  maxSize?: number;
  ratio?: number;
  imageUrl?: string;
  disableActionUpload?: Boolean;
}

/**
 * This component just handle the image upload and preview
 */
const ImageUpload: React.FC<ImageUploadProps> = ({
  onFileChange,
  title,
  maxSize = ALLOWABLE_IMAGE_SIZE,
  ratio,
  imageUrl,
  disableActionUpload = true,
}) => {
  const { t } = useTranslation('common');
  const [imagePreview, setImagePreview] = React.useState<string | null>(null);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const imageFile = event.target.files?.[0];

    if (imageFile) {
      const image = new Image();
      image.src = URL.createObjectURL(imageFile);

      image.onload = async () => {
        if (imageFile.size / 1024 > maxSize) {
          toast({
            description: t('ERROR_BY_MAX_FILE_SIZE'),
          });
          if (fileInputRef.current) fileInputRef.current.value = '';
          return;
        }

        if (ratio && image.width / image.height !== ratio) {
          toast({
            description: t('ERROR_INVALID_ASPECT_RATIO'),
          });
          if (fileInputRef.current) fileInputRef.current.value = '';
          return;
        }
        setImagePreview(image.src);
        onFileChange(imageFile, image.src, imageFile.name);
      };
    } else {
      setImagePreview(null);
      onFileChange(null, null, ''); // Reset the callback if no file is selected
    }
  };

  return (
    <Card className="flex max-h-[448px] flex-col items-center gap-4 border-dashed px-12 py-6">
      <Label className="text-gray-700">{title}</Label>
      <input
        type="file"
        accept="image/*"
        ref={fileInputRef}
        onChange={handleFileChange}
        className="hidden"
      />
      {imagePreview ? (
        <img className="max-h-44" src={imagePreview} alt="preview" />
      ) : imageUrl ? (
        <img className="max-h-44" src={imageUrl} alt="default" />
      ) : (
        <svg
          width="80"
          height="80"
          viewBox="0 0 80 80"
          fill="none"
          xmlns="http://www.w3.org/2000/svg">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M21.5867 48.7543C25.5367 46.901 28.73 48.0476 31.82 49.161C34.15 50.001 36.5633 50.8676 39.9 50.8676C42.4933 50.8676 44.0233 48.5776 46.2333 44.7976C48.4333 41.041 51.17 36.3643 56.6967 36.3643C64.3767 36.3643 69.1367 41.2076 72.3633 44.7776C72.4467 43.251 72.5 41.671 72.5 40.001C72.5 16.0043 63.9933 7.50098 40 7.50098C16.0067 7.50098 7.5 16.0043 7.5 40.001C7.5 48.951 8.68667 55.7443 11.42 60.7476C13.14 57.3576 16.88 50.9643 21.5867 48.7543Z"
            fill="#F5F5F5"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M70.7728 50.4399C70.2828 49.9366 69.7628 49.3599 69.2062 48.7366C66.2595 45.4533 62.5895 41.3633 56.6962 41.3633C54.2162 41.3633 52.7195 43.6166 50.5528 47.3233C48.3228 51.1333 45.5528 55.8699 39.8995 55.8699C35.6895 55.8699 32.6062 54.7599 30.1262 53.8633C27.2062 52.8099 25.6995 52.3433 23.7162 53.2799C19.9428 55.0533 16.1562 62.2066 15.0162 64.8133C14.9495 64.9733 14.8462 65.1033 14.7495 65.2433C19.8728 70.3733 27.9995 72.4999 40.0028 72.4999C59.8662 72.4999 69.0928 66.6399 71.6895 51.0199C71.3528 50.8933 71.0395 50.7133 70.7728 50.4399Z"
            fill="#E5E5E5"
          />
          <path
            d="M36.9972 29.2808C36.9972 25.1275 33.6138 21.7441 29.4605 21.7441C25.3072 21.7441 21.9238 25.1275 21.9238 29.2808C21.9238 33.4341 25.3072 36.8141 29.4605 36.8141C33.6138 36.8141 36.9972 33.4341 36.9972 29.2808Z"
            fill="#E5E5E5"
          />
        </svg>
      )}
      <Typography
        className="text-center text-gray-400"
        variant="p"
        affects="removePMargin">
        {t('IMAGE_SHOULD_BE_MORE_5KB_AND_LESS_THAN_64KB')}
        <br />
        {ratio ? t('RATIO', { ratio }) : null}
      </Typography>
      {disableActionUpload ? (
        <Button
          color="primary"
          className="text- flex items-center gap-2 rounded-lg"
          variant="outline"
          type="button"
          onClick={handleButtonClick}>
          <UploadCloud className="h-4 w-4" />
          {t('UPLOAD')}
        </Button>
      ) : null}
    </Card>
  );
};

export { ImageUpload };
