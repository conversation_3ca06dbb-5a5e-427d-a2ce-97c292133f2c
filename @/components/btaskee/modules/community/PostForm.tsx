import { MultiSelectAdvance } from '@/components/btaskee/MultiSelectAdvance';
import { RichTextComponent } from '@/components/btaskee/RichTextComponent';
import { Typography } from '@/components/btaskee/Typography';
import { SingleDateTimePicker as SingleDateTimePickerField } from '@/components/btaskee/single-date-time-picker/single-time-picker';
import type { ConfirmationParams } from '@/components/context/dialog-confirmation';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { zodResolver } from '@hookform/resolvers/zod';
import { SerializeFrom } from '@remix-run/node';
import { useNavigate } from '@remix-run/react';
import { POST_STATUS } from 'btaskee-constants';
import { momentTz, usePostSchema } from 'btaskee-utils';
import type { TFunction } from 'i18next';
import { ComponentProps, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { MediaUploaderPreview, type MediaType } from '@/components/btaskee/MediaUpload';
import { UploadButton } from '@/components/btaskee/drop-zone/UploadButton';
import flatMapDeep from 'lodash/flatMapDeep.js';

type PostFormData = z.infer<ReturnType<typeof usePostSchema>>;

interface PostFormProps extends Omit<ComponentProps<typeof UploadButton>, 'handleToggleUploadMedia' | 'isOpen' | 'handleUploadFile' | 'handleToggle'> {
  originalData?: SerializeFrom<
    Pick<
      CommunityPost,
      | '_id'
      | 'content'
      | 'images'
      | 'videos'
      | 'tagIds'
      | 'scheduleTime'
      | 'status'
    >
  >;
  tagNameList: SerializeFrom<Array<CommunityTag>>;
  onSubmit: (data: PostFormData) => void;
  t: TFunction;
  isConfirm: (params: ConfirmationParams<'confirm'>) => Promise<boolean>;
}

const PostForm = ({
  originalData,
  tagNameList,
  onSubmit,
  t,
  isConfirm,
  ...restProps
}: PostFormProps) => {
  const navigate = useNavigate();

  const form = useForm<PostFormData>({
    resolver: zodResolver(
      usePostSchema(
        t,
        originalData
          ? momentTz().isBefore(momentTz(originalData?.scheduleTime))
          : true,
      ),
    ),
    defaultValues: originalData
      ? {
          tags: originalData.tagIds
            ?.map(tagId => {
              const tag = tagNameList.find(tag => tag._id === tagId);
              return tag ? { _id: tag._id, name: tag.text.en } : null;
            })
            .filter(
              (tag): tag is { _id: string; name: string } => tag !== null,
            ),
          isSchedule: Boolean(originalData.scheduleTime),
          scheduleDate: originalData.scheduleTime
            ? momentTz(originalData.scheduleTime).toDate()
            : momentTz().add(1, 'hour').minutes(0).seconds(0).toDate(),
          content: originalData.content || '',
          media: {
            images:
              originalData.images?.map(image => ({
                type: 'image' as MediaType,
                preview: image.thumbnailUrl || '',
                file: image.imageUrl || '',
                _id: image._id,
              })) || [],
            videos:
              originalData.videos?.map(video => ({
                type: 'video' as MediaType,
                preview: video.thumbnailUrl || '',
                file: video.videoUrl || '',
                _id: video._id,
              })) || [],
          },
        }
      : {
        tags: [],
        isSchedule: false,
        scheduleDate: momentTz()
          .add(1, 'hour')
          .minutes(0)
          .seconds(0)
          .toDate(),
        content: '',
        media: {
          images: [],
          videos: [],
        },
      },
  });

  const { control, handleSubmit, watch, setValue, formState: { errors } } = form;

  console.log('errors', errors)

  const handleFormSubmit = async (data: PostFormData) => {
    if (
      await isConfirm({
        title: t(originalData ? 'SAVE_CHANGE_DIALOG_TITLE' : 'SUBMIT_POST'),
        body: t(
          originalData ? 'SAVE_CHANGE_DIALOG_BODY' : 'SUBMIT_POST_CONFIRMATION',
        ),
        actionButton: t(originalData ? 'SAVE_CHANGE' : 'SUBMIT'),
        cancelButton: t('CANCEL'),
      })
    ) {
      onSubmit(data);
    }
  };

  const handleCancel = async () => {
    if (
      await isConfirm({
        title: t(originalData ? 'CANCEL_EDIT_POST' : 'CANCEL_POST'),
        body: t(
          originalData
            ? 'CANCEL_EDIT_POST_CONFIRMATION'
            : 'CANCEL_POST_CONFIRMATION',
        ),
        actionButton: t('OK'),
        cancelButton: t('CANCEL'),
      })
    ) {
      navigate(-1);
    }
  };

  const tags = watch('tags');
  const content = watch('content');
  const media = watch('media');

  // draft
  const isFillContentOrVideoOrImage = useMemo(() => {
    return Boolean(
      content ||
      (media?.images?.length && media?.images?.length > 0) ||
      (media?.videos?.length && media?.videos?.length > 0),
    ); // have 1 in 3
  }, [content, media?.images, media?.videos]);

  const isDisabledSubmitAction = useMemo(() => {
    const isFormChanged = form.formState.isDirty;
    return !isFormChanged || tags?.length === 0 || !isFillContentOrVideoOrImage;
  }, [isFillContentOrVideoOrImage, tags, form]);

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <div className="flex flex-col">
          <div className="flex flex-col gap-6 mb-8">
            <Typography variant="h4">{t('POST_DETAIL')}</Typography>
            <FormField
              name="content"
              control={control}
              render={({ field: { value, ref } }) => (
                <FormItem>
                  <FormControl>
                    <RichTextComponent
                      markdown={value || ''}
                      formFieldRefCallback={ref}
                      onChange={contentValue =>
                        setValue('content', contentValue, {
                          shouldValidate: true,
                          shouldDirty: true,
                        })
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          {/* Preview media uploaded here */}
          <FormField
            control={control}
            name="media"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormControl>
                  <MediaUploaderPreview
                    accept="image/*" // ,video/* later
                    maxFiles={10}
                    maxImageSize={1000} // in KB
                    value={field.value}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex flex-col gap-6">
            <Separator />
            <div className="flex items-start justify-between">
              <div className="flex flex-col gap-6">
                <div className="flex items-center gap-6">
                  <FormField
                    name="tags"
                    control={control}
                    render={({ field: { onChange, value } }) => (
                      <FormItem className="w-[500px]">
                        <FormControl>
                          <MultiSelectAdvance
                            placeholder={t('SELECT_TAG')}
                            onValueChange={selectedValues => {
                              const selectedTags = tagNameList
                                .filter(tagName =>
                                  selectedValues.includes(tagName._id),
                                )
                                .map(tag => ({
                                  _id: tag._id,
                                  name:
                                    tag.text?.en ||
                                    "DB error: tag don't have text",
                                }));
                              onChange(selectedTags);
                            }}
                            defaultValue={
                              Array.isArray(value)
                                ? value.map(tag => tag._id)
                                : []
                            }
                            options={tagNameList.map(tag => ({
                              label:
                                tag.text?.en || "DB error: tag don't have text",
                              value: tag._id,
                            }))}
                            variant="blue"
                            maxCount={2}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Button upload here */}
                  <FormField
                    control={control}
                    name="media"
                    render={({ field }) => (
                      <UploadButton
                        handleUploadFile={(payload) => field.onChange(payload.mediaFilesByType)}
                        initSlots={flatMapDeep([media?.images, media?.videos]).map(item => ({ url: item?.file as string }))}
                        {...restProps}
                      />
                    )}
                  />
                </div>
                <div className="flex flex-col gap-4 items-start">
                  <FormField
                    control={control}
                    name="isSchedule"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <div className="flex items-center gap-2">
                            <Switch
                              disabled={
                                originalData &&
                                originalData?.status !==
                                POST_STATUS.NOT_POSTED.label
                              }
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                            <Label htmlFor="schedule">
                              {t('POST_SCHEDULING')}
                            </Label>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {form.watch('isSchedule') ? (
                    <SingleDateTimePickerField
                      triggerProps={{
                        disabled:
                          originalData &&
                          originalData?.status !== POST_STATUS.NOT_POSTED.label,
                      }}
                      form={form}
                      name="scheduleDate"
                      label={t('SCHEDULE_DATE')}
                      format="HH:mm:ss dd/MM/yyyy"
                      disableTimePickers={['minutes', 'seconds']}
                    />
                  ) : null}
                </div>
              </div>

              <div className="flex flex-col gap-3">
                {tags?.length === 0 ? (
                  <Typography
                    variant="p"
                    className="text-yellow-500 font-medium leading-6 text-sm"
                    affects={'removePMargin'}>
                    {t('SELECT_TAG_BEFORE_SUBMIT')}
                  </Typography>
                ) : !isFillContentOrVideoOrImage ? (
                  <Typography
                    variant="p"
                    className="text-yellow-500 font-medium leading-6 text-sm"
                    affects={'removePMargin'}>
                    {t(
                      'AT_LEAST_ONE_OF_CONTENT_VIDEO_OR_IMAGE_MUST_HAVE_A_VALUE',
                    )}
                  </Typography>
                ) : null}
                <div className="flex justify-end gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    className="border-primary text-primary hover:text-primary"
                    onClick={handleCancel}>
                    {t('CANCEL')}
                  </Button>

                  <Button
                    type="submit"
                    variant={'default'}
                    disabled={isDisabledSubmitAction}>
                    {t(originalData ? 'SAVE_CHANGE' : 'SUBMIT')}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </Form>
  );
};

export { PostForm };
