import { BlockDescription } from '@/components/btaskee/CardInformation';
import { Typography } from '@/components/btaskee/Typography';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import type { SerializeFrom } from '@remix-run/node';
import {
  COMMUNITY_ACTION_KEY_IN_CHANGE_HISTORY,
  COMMUNITY_POST_STATUS,
  POST_STATUS,
} from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

interface GeneralInformationProps {
  postData: SerializeFrom<CommunityPostDetail>;
}

const GeneralInformationComponent: FC<GeneralInformationProps> = ({
  postData,
}) => {
  const { t } = useTranslation('community-component');
  return (
    <Card className="bg-gray-50 h-fit rounded-2xl">
      <CardHeader>
        <Typography
          variant="h4"
          className="w-1/5 pb-3 pr-4 border-b min-w-fit border-b-gray-200">
          {t('GENERAL_INFORMATION')}
        </Typography>
      </CardHeader>
      <CardContent className="grid grid-cols-2 gap-6">
        <BlockDescription
          desc={{
            label: t('POST_ID'),
            value: postData._id,
          }}
        />
        <BlockDescription
          desc={{
            label: t('POST_STATUS'),
            customValueNode: (
              <span
                className={`w-fit text-sm py-1.5 px-3 inline rounded-md leading-tight font-normal ${Object.values(POST_STATUS).find(status => status.label === postData.status)?.className || ''}`}>
                {t(postData.status || '')}
              </span>
            ),
          }}
        />
        <BlockDescription
          desc={{
            label: t('POST_TYPE'),
            value: t(postData.userType),
          }}
        />
        <BlockDescription
          desc={{
            label: t('PINNED_POST'),
            customValueNode: (
              <span className={`w-fit text-sm py-1.5 px-3 inline rounded-md leading-tight font-normal ${
                postData.isPinned 
                ? 'bg-green-100 text-green-600' 
                : 'bg-gray-100 text-gray-600'
              }`}>
                {t(postData.isPinned ? 'True' : 'False')}
              </span>
            ),
          }}
        />
        <BlockDescription
          desc={{
            label: t('POST_CREATED_AT'),
            value: momentTz(postData.createdAt).format('HH:mm - DD/MM/YYYY'),
          }}
        />
        <BlockDescription
          desc={{
            label: t('POST_UPDATED_BY'),
            value:
              postData.updatedBy || postData.createdBy || postData.userName,
          }}
        />
        <BlockDescription
          desc={{
            label: t('POST_UPDATED_AT'),
            value: postData.updatedAt
              ? momentTz(postData.updatedAt).format('HH:mm - DD/MM/YYYY')
              : momentTz(postData.createdAt).format('HH:mm - DD/MM/YYYY'),
          }}
        />
        {postData.scheduleTime ? (
          <BlockDescription
            desc={{
              label: t('POST_SCHEDULING'),
              value: momentTz(postData.scheduleTime).format(
                'HH:mm - DD/MM/YYYY',
              ),
            }}
          />
        ) : null}
        {postData.status === POST_STATUS.INACTIVE.label && (
          <>
            <BlockDescription
              desc={{
                label: t('POST_HIDDEN_BY'),
                value: postData.updatedBy,
              }}
            />
            <BlockDescription
              desc={{
                label: t('POST_HIDDEN_AT'),
                value: momentTz(postData.updatedAt).format(
                  'HH:mm - DD/MM/YYYY',
                ),
              }}
            />
          </>
        )}
        {postData.status === COMMUNITY_POST_STATUS.INACTIVE ? (
          <BlockDescription
            desc={{
              label: t('REASON'),
              value: t(
                postData?.changeHistories
                  ?.filter(
                    history =>
                      history.key ===
                      COMMUNITY_ACTION_KEY_IN_CHANGE_HISTORY.HIDE_POST_FROM_BACKEND,
                  )
                  .pop()?.reason ?? '',
              ),
            }}
          />
        ) : null}
      </CardContent>
    </Card>
  );
};

export { GeneralInformationComponent };
