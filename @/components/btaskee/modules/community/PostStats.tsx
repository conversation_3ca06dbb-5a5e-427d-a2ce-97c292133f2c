import { Button } from '@/components/ui/button';
import {
  HeartIcon,
  HeartReactedIcon,
  MessageIcon,
  ShareIcon,
} from '@/components/svg/community';
import type { SerializeFrom } from '@remix-run/node';
import { Share2 } from 'lucide-react';
import type { FC } from 'react';

interface PostStatsProps {
  post: SerializeFrom<CommunityPostDetail>;
  isLiked: boolean;
  onClickLike: (e: React.MouseEvent<Element>) => void;
  onClickSharePost: (e: React.MouseEvent<Element>) => void;
  onClickComment: (e: React.MouseEvent<Element>) => void;
  canSharePost: boolean;
}

const PostStats: FC<PostStatsProps> = ({
  post,
  isLiked,
  onClickLike,
  onClickSharePost,
  onClickComment,
  canSharePost,
}) => {
  return (
    <div className="flex items-center border-t border-b border-gray-200 py-3 gap-4">
      <Button
        type="button"
        variant="ghost"
        size="sm"
        className="h-fit gap-2 text-[#383838] leading-tight px-0"
        onClick={e => onClickLike(e)}>
        {/* TODO: Transition for better UX */}
        {isLiked ? <HeartReactedIcon /> : <HeartIcon />}
        {post.numberOfLikes || 0}
      </Button>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        className="h-fit gap-2 text-[#383838] leading-tight px-0"
        onClick={e => onClickComment(e)}>
        <MessageIcon />
        {post.numberOfComments || 0}
      </Button>
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={onClickSharePost}
        disabled={!canSharePost}
        className="h-fit gap-2 px-0 leading-tight text-[#383838]">
        <Share2 className="h-5 w-5" />
        {post.numberOfShares || 0}
      </Button>
    </div>
  );
};

export { PostStats };
