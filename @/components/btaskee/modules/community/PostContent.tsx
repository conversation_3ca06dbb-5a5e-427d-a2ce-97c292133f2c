import { RichTextComponent } from '@/components/btaskee/RichTextComponent';
import { Typography } from '@/components/btaskee/Typography';
import { MediaGallery } from '@/components/btaskee/modules/community/MediaGallery';
import { TagIcon } from '@/components/svg/community';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import type { SerializeFrom } from '@remix-run/node';
import { Link } from '@remix-run/react';
import { ROUTE_NAME } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { PinIcon, UserIcon } from 'lucide-react';
import { type FC, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

interface PostContentProps {
  post: SerializeFrom<
    CommunityPostDetail & { user: Pick<CommunityUser, 'isAdmin'> }
  >;
  repost?: SerializeFrom<
    Pick<
      CommunityPost,
      | '_id'
      | 'content'
      | 'images'
      | 'videos'
      | 'tagIds'
      | 'scheduleTime'
      | 'status'
      | 'createdAt'
    > & {
      tags: Array<CommunityTag>;
      userName: string;
      userAvatar: string;
      userType: string;
    }
  >;
}

const PostContent: FC<PostContentProps> = ({ post, repost }) => {
  
  const linkNavigateToUser = useMemo(
    () =>
      post?.user?.isAdmin
        ? ROUTE_NAME.CONFIGURATION_PERSONAL_PAGE_MANAGEMENT
        : `${ROUTE_NAME.USER_MANAGEMENT_IN_COMMUNITY}/${post?.posterId}`,
    [post?.user],
  );

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <Link to={linkNavigateToUser} className="flex items-center gap-4">
          <Avatar className="w-16 h-16">
            <AvatarImage
              alt={post?.userName}
              src={post?.userAvatar}
              className="object-cover w-full h-full"
            />
            <AvatarFallback className="object-cover w-full h-full rounded-full">
              <UserIcon />
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col gap-1">
            <Typography
              variant="p"
              affects="removePMargin"
              className="font-semibold text-lg text-[#383838]">
              {post?.userName}
            </Typography>
            <time
              className="text-sm leading-tight text-[#878787]"
              title={momentTz(post?.createdAt).format('HH:mm - DD/MM/YYYY')}>
              {momentTz(post?.createdAt).isBefore(momentTz().subtract(7, 'days'))
                ? momentTz(post?.createdAt).format('HH:mm - DD/MM/YYYY')
                : momentTz(post?.createdAt).fromNow()}
            </time>
          </div>
        </Link>
        {post?.isPinned && (
          <div className="flex items-center gap-1 bg-green-100 text-green-600 px-3 py-1.5 rounded-md">
            <PinIcon className="w-4 h-4" />
            <span className="text-sm font-medium">{('PINNED')}</span>
          </div>
        )}
      </div>
      <div className="text-base leading-tight text-gray-600">
        <RichTextComponent markdown={post?.content || ''} readOnly />
      </div>
      {/* TODO: Enable this section later when we have image */}
      {/* <div className={post?.images?.length > 1 ? 'grid grid-cols-2 gap-4' : ''}>
        {post?.images &&
          post?.images?.map(image => (
            <img
              src={image.imageUrl}
              alt={post?.content}
              className="object-cover w-full h-auto"
            />
          ))}
      </div> */}

      <div className="flex flex-col gap-4">
        {post?.images?.length ? (
          <MediaGallery images={post.images} alt="Post images" />
        ) : null}
      </div>

      {repost ? (
        <div className="flex flex-col p-6 gap-2.5 bg-[#FAFAFA] rounded-md w-full">
          <div className="flex items-center gap-4">
            <Avatar className="w-16 h-16">
              <AvatarImage
                alt={repost?.userName}
                src={repost?.userAvatar}
                className="object-cover w-full h-full"
              />
              <AvatarFallback className="object-cover w-full h-full rounded-full">
                <UserIcon />
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col gap-1">
              <Typography
                variant="p"
                affects="removePMargin"
                className="font-semibold text-lg text-[#383838]">
                {repost?.userName}
              </Typography>
              <time
                className="text-sm leading-tight text-[#878787]"
                title={momentTz(repost?.createdAt).format(
                  'HH:mm - DD/MM/YYYY',
                )}>
                {momentTz(repost?.createdAt).isBefore(
                  momentTz().subtract(7, 'days'),
                )
                  ? momentTz(repost?.createdAt).format('HH:mm - DD/MM/YYYY')
                  : momentTz(repost?.createdAt).fromNow()}
              </time>
            </div>
          </div>
          <div className="text-base leading-tight text-gray-600">
            <RichTextComponent markdown={repost?.content || ''} readOnly />
          </div>

          <div className="flex flex-col gap-4">
            {repost?.images?.length ? (
              <MediaGallery images={repost.images} alt="Repost images" />
            ) : null}
          </div>

          {Array.isArray(repost?.tags) && repost?.tags?.length > 0 ? (
            <div className="flex items-center gap-2">
              <div className="min-w-4 min-h-4 max-w-4 max-h-4">
                <TagIcon />
              </div>
              <div className="flex flex-wrap items-center gap-4">
                {repost?.tags?.map(tag => (
                  <span className="text-sm text-primary" key={tag._id}>
                    {tag.text?.en}
                  </span>
                ))}
              </div>
            </div>
          ) : null}
        </div>
      ) : null}

      {Array.isArray(post?.tags) && post?.tags?.length > 0 ? (
        <div className="flex items-center gap-2">
          <div className="min-w-4 min-h-4 max-w-4 max-h-4">
            <TagIcon />
          </div>
          <div className="flex flex-wrap items-center gap-4">
            {post?.tags?.map(tag => (
              <span className="text-sm text-primary" key={tag._id}>
                {tag.text?.en}
              </span>
            ))}
          </div>
        </div>
      ) : null}
    </div>
  );
};

export { PostContent };
