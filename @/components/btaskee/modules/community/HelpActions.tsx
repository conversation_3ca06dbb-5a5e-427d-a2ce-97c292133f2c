import { Button } from '@/components/ui/button';
import { AssignIcon, HelpResolvedIcon } from '@/components/svg/community';
import { useTranslation } from 'react-i18next';

const HelpActions = ({
  openAssignDialog,
  openResolveDialog,
  isDisableAssignButton,
  isDisableResolveButton,
}: {
  openAssignDialog: () => void;
  openResolveDialog: () => void;
  isDisableAssignButton: boolean;
  isDisableResolveButton: boolean;
}) => {
  const { t } = useTranslation('community-component');
  return (
    <div className="flex items-center w-1/2 justify-end gap-4">
      <Button
        type="button"
        disabled={isDisableAssignButton}
        onClick={openAssignDialog}
        variant="outline"
        className="gap-2 text-primary border-primary hover:text-primary h-fit leading-tight">
        <AssignIcon />
        {t('ASSIGN')}
      </Button>
      <Button
        type="button"
        variant="outline"
        disabled={isDisableResolveButton}
        onClick={openResolveDialog}
        className="gap-2 text-primary border-primary hover:text-primary h-fit leading-tight">
        <HelpResolvedIcon />
        {t('RESOLVED')}
      </Button>
    </div>
  );
};

export { HelpActions };
