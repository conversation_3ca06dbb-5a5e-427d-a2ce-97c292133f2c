import type { <PERSON>ousel<PERSON><PERSON> } from '@/components/ui/carousel';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import {
  DetailedHTMLProps,
  ImgHTMLAttributes,
  useEffect,
  useState,
} from 'react';

interface ZoomableImageProps
  extends DetailedHTMLProps<
    ImgHTMLAttributes<HTMLImageElement>,
    HTMLImageElement
  > {
  images?: Array<{ imageUrl: string }>;
  currentIndex?: number;
}

function getGridClassName(imageCount: number) {
  switch (imageCount) {
    case 1:
      return 'w-full aspect-[4/3]';
    case 2:
      return 'grid grid-cols-2 gap-2 aspect-[4/3]';
    case 3:
      return 'grid grid-rows-[1fr_1fr] gap-2 aspect-[4/3]';
    case 4:
      return 'grid grid-cols-2 grid-rows-2 gap-2 aspect-[4/3]';
    default:
      return 'grid grid-rows-[1fr_1fr] gap-2 aspect-[4/3]';
  }
}

function getImageClassName(imageCount: number, index: number) {
  if (imageCount === 1) return 'w-full h-full object-cover rounded-md';

  if (imageCount === 3) {
    if (index === 0) {
      return 'w-full h-full object-cover rounded-md';
    }
    return 'w-full h-full object-cover rounded-md';
  }

  if (imageCount >= 5) {
    if (index < 2) {
      return 'w-full h-full object-cover rounded-md';
    } else {
      return 'w-full h-full object-cover rounded-md';
    }
  }

  return 'w-full h-full object-cover rounded-md';
}

const MediaGallery = ({ src, alt, className, images }: ZoomableImageProps) => {
  const [startIndex, setStartIndex] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [api, setApi] = useState<CarouselApi>();

  useEffect(() => {
    if (api && isOpen) {
      api.scrollTo(startIndex);
    }
  }, [api, isOpen, startIndex]);

  // If no images array provided, treat as single image
  if (!images && src) {
    return (
      <Dialog>
        <DialogTrigger asChild>
          <img
            src={src}
            alt={alt || ''}
            sizes="100vw"
            className={className}
            style={{
              width: '100%',
              height: 'auto',
            }}
            width={500}
            height={100}
          />
        </DialogTrigger>
        <DialogContent className="max-w-7xl border-0 bg-transparent p-0">
          <div className="relative h-[calc(100vh-220px)] w-full overflow-clip rounded-md bg-transparent">
            <img
              src={src}
              alt={alt || ''}
              className="h-full w-full object-contain"
            />
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (!images?.length) return null;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div className={getGridClassName(images.length)}>
          {images.length === 3 ? (
            <>
              <div
                className="w-full"
                onClick={() => {
                  setStartIndex(0);
                  setIsOpen(true);
                }}>
                <img
                  src={images[0].imageUrl}
                  alt={alt || ''}
                  className="w-full h-full object-cover rounded-md"
                />
              </div>
              <div className="grid grid-cols-2 gap-2">
                {images.slice(1, 3).map((image, index) => (
                  <div
                    key={image.imageUrl}
                    className="relative"
                    onClick={() => {
                      setStartIndex(index + 1);
                      setIsOpen(true);
                    }}>
                    <img
                      src={image.imageUrl}
                      alt={alt || ''}
                      className="w-full h-full object-cover rounded-md"
                    />
                  </div>
                ))}
              </div>
            </>
          ) : images.length >= 5 ? (
            <>
              <div className="grid grid-cols-2 gap-2">
                {images.slice(0, 2).map((image, index) => (
                  <div
                    key={image.imageUrl}
                    className="relative"
                    onClick={() => {
                      setStartIndex(index);
                      setIsOpen(true);
                    }}>
                    <img
                      src={image.imageUrl}
                      alt={alt || ''}
                      className={getImageClassName(images.length, index)}
                    />
                  </div>
                ))}
              </div>
              <div className="grid grid-cols-3 gap-2">
                {images.slice(2, 5).map((image, index) => (
                  <div
                    key={image.imageUrl}
                    className="relative"
                    onClick={() => {
                      setStartIndex(index + 2);
                      setIsOpen(true);
                    }}>
                    <img
                      src={image.imageUrl}
                      alt={alt || ''}
                      className={getImageClassName(images.length, index + 2)}
                    />
                    {images.length > 5 && index === 2 && (
                      <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-md">
                        <span className="text-white text-xl font-semibold">
                          +{images.length - 5}
                        </span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </>
          ) : (
            images.slice(0, 4).map((image, index) => (
              <div
                key={image.imageUrl}
                className="relative"
                onClick={() => {
                  setStartIndex(index);
                  setIsOpen(true);
                }}>
                <img
                  src={image.imageUrl}
                  alt={alt || ''}
                  className={getImageClassName(images.length, index)}
                />
              </div>
            ))
          )}
        </div>
      </DialogTrigger>
      <DialogContent className="max-w-7xl border-0 bg-transparent p-0">
        <Carousel className="w-full" setApi={setApi}>
          <CarouselContent>
            {images.map(image => (
              <CarouselItem key={image.imageUrl}>
                <div className="relative h-[calc(100vh-220px)] w-full overflow-clip rounded-md bg-transparent">
                  <img
                    src={image.imageUrl}
                    alt={alt || ''}
                    className="h-full w-full object-contain"
                  />
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="left-4 bg-white/30 hover:bg-white/50" />
          <CarouselNext className="right-4 bg-white/30 hover:bg-white/50" />
        </Carousel>
      </DialogContent>
    </Dialog>
  );
};

export { MediaGallery };
