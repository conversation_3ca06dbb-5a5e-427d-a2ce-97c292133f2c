import { BlockDescription } from '@/components/btaskee/CardInformation';
import { Typography } from '@/components/btaskee/Typography';
import { HelpActions } from '@/components/btaskee/modules/community/HelpActions';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import type { SerializeFrom } from '@remix-run/node';
import { POST_STATUS } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';

interface HelpComponentProps {
  postData: SerializeFrom<CommunityPostDetail>;
  openAssignDialog: () => void;
  openResolveDialog: () => void;
  isDisableAssignButton: boolean;
  isDisableResolveButton: boolean;
  isNeedHelp: boolean;
}

const HelpComponent: FC<HelpComponentProps> = ({
  postData,
  openAssignDialog,
  openResolveDialog,
  isDisableAssignButton,
  isDisableResolveButton,
  isNeedHelp,
}) => {
  const { t } = useTranslation('community-component');
  return (
    <Card className="bg-gray-50 h-fit rounded-2xl w-full mx-auto p-6 space-y-6">
      <div className="w-full">
        <div className="flex justify-between items-center mb-3">
          <div className="w-1/2 flex items-center gap-2">
            <Typography variant="h4">{t('HELP_INFORMATION')}</Typography>
            <span
              className={cn(
                'w-fit text-sm py-1.5 px-3 inline rounded-md leading-tight font-normal',
                isNeedHelp
                  ? Object.values(POST_STATUS).find(
                      status => status.label === postData.status,
                    )?.className || ''
                  : 'text-secondary-foreground bg-secondary',
              )}>
              {isNeedHelp ? t('NEED_HELP') : t('RESOLVED')}
            </span>
          </div>
          {isNeedHelp ? (
            <HelpActions
              openAssignDialog={openAssignDialog}
              openResolveDialog={openResolveDialog}
              isDisableAssignButton={isDisableAssignButton}
              isDisableResolveButton={isDisableResolveButton}
            />
          ) : null}
        </div>
        <Separator className="w-1/3 mt-3" />
      </div>
      <div className="grid grid-cols-2 gap-6">
        <BlockDescription
          desc={{
            label: t('HELP_CREATED_AT'),
            value: postData?.createdAt
              ? momentTz(postData.createdAt).format('HH:mm - DD/MM/YYYY')
              : '',
          }}
        />
        <BlockDescription
          desc={{
            label: t('HELP_SUPPORTER'),
            value: postData.support?.username || t('NONE'),
          }}
        />
      </div>
    </Card>
  );
};

export { HelpComponent };
