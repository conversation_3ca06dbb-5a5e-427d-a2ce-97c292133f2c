import { Typography } from '@/components/btaskee/Typography';
import {
  HeartIcon,
  HeartReactedIcon,
  HideCommentIcon,
  ReplyIcon,
  UnHideCommentIcon,
} from '@/components/svg/community';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { SerializeFrom } from '@remix-run/node';
import { COMMUNITY_COMMENT_STATUS } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { UserIcon } from 'lucide-react';
import type { FC } from 'react';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

interface CommentComponentProps {
  comment: SerializeFrom<Comment>;
  isReply?: boolean;
  isLastReply?: boolean;
  isHidden?: boolean;
  adminId?: CommunityUser['_id'];
  onClickReply: (comment: SerializeFrom<Comment>, rootCommentId: SerializeFrom<Comment>['_id']) => void;
  onClickLike: (comment: SerializeFrom<Comment>) => void;
  onClickHide: (comment: SerializeFrom<Comment>) => void;
  rootCommentId: SerializeFrom<Comment>['_id'];
}

const CommentComponent: FC<CommentComponentProps> = ({
  comment,
  onClickReply,
  isReply = false,
  isLastReply = false,
  isHidden = false,
  onClickLike,
  onClickHide,
  adminId,
  rootCommentId,
}) => {
  const { t } = useTranslation('community-component');
  const replies = useMemo(() => comment.subComments ?? [], [comment]);
  const isLiked = useMemo(() => adminId && comment.likeUserIds?.includes(adminId), [adminId, comment]);

  const handleReply = useCallback(
    () => onClickReply(comment, rootCommentId),
    [comment, onClickReply, rootCommentId],
  );
  const handleLike = useCallback(
    () => onClickLike(comment),
    [comment, onClickLike],
  );
  const handleHide = useCallback(
    () => onClickHide(comment),
    [comment, onClickHide],
  );

  const renderReplyLine = useMemo(() => {
    if (isReply) {
      return (
        <div className="absolute top-0 left-4 h-7 w-6 border-b-2 border-b-gray-200 border-l-2 border-l-gray-200 rounded-bl-2xl" />
      );
    }
    return null;
  }, [isReply]);

  const renderCommentContent = useMemo(
    () => (
      <div
        className={`bg-gray-100 p-4 rounded-lg flex flex-col gap-2 ${isHidden ? 'opacity-50' : ''
          }`}>
        <div className="flex items-center justify-between">
          <Typography
            variant="p"
            affects="removePMargin"
            className="font-bold text-base leading-tight text-gray-600">
            {comment.user.name}
          </Typography>
          <time
            className="text-sm leading-tight text-[#878787] font-normal"
            title={momentTz(comment.createdAt).format('HH:mm - DD/MM/YYYY')}>
            {momentTz(comment.createdAt).isBefore(momentTz().subtract(7, 'days'))
              ? momentTz(comment.createdAt).format('HH:mm - DD/MM/YYYY')
              : momentTz(comment.createdAt).fromNow()}
          </time>
        </div>
        <Typography
          variant="p"
          affects="removePMargin"
          className="text-sm leading-tight text-gray-600">
          {comment.mentions?.map(mention => (
            <span className="text-blue-600" key={mention._id}>
              @{mention.name}
            </span>
          ))}{' '}
          {comment.content}
        </Typography>
        {/* {comment.content.image && (
        <img
          src={comment.content.image}
          alt={comment.content.text}
          className="w-fit h-auto max-h-[200px] object-contain object-left rounded-lg"
        />
      )} */}
      </div>
    ),
    [comment, isHidden],
  );

  const renderCommentActions = useMemo(
    () => (
      <div
        className={`flex items-center mt-1.5 gap-4 text-sm text-gray-500 ${isHidden ? 'justify-end' : 'justify-between'}`}>
        {!isHidden ? (
          <div className="flex items-center gap-6">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="h-fit gap-2 text-[#383838] leading-tight px-0"
              onClick={handleLike}>
              {isLiked ? <HeartReactedIcon /> : <HeartIcon />}
              {comment.likeUserIds?.length || 0}
            </Button>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="h-fit gap-2 text-[#383838] leading-tight px-0"
              onClick={handleReply}>
              <ReplyIcon />
              {t('REPLY')}
            </Button>
          </div>
        ) : null}

        <Button
          variant="ghost"
          size="sm"
          className="h-fit gap-2 text-[#383838] leading-tight px-0"
          onClick={handleHide}>
          {isHidden ? <UnHideCommentIcon /> : <HideCommentIcon />}
          {t(isHidden ? 'UN_HIDE' : 'HIDE')}
        </Button>
      </div>
    ),
    [comment, isHidden, isLiked, isReply, t],
  );

  const renderRepliesLine = useMemo(() => {
    if (!isReply && replies && replies.length > 0) {
      return (
        <div className="absolute top-12 left-4 h-[calc(100%-36px)] w-0.5 bg-gray-200" />
      );
    }
    if (isReply && !isLastReply) {
      return <div className="absolute top-0 left-4 h-full w-0.5 bg-gray-200" />;
    }
    return null;
  }, [isReply, isLastReply]);

  return (
    <>
      <div
        className={`relative flex items-start pt-3 ${isReply ? 'pl-11' : ''}`}>
        {renderReplyLine}
        <div className={`absolute top-3 ${isReply ? 'left-11' : 'left-0'}`}>
          <Avatar className="h-8 w-8">
            <AvatarImage
              alt={comment.user?.name}
              src={comment.user?.avatar}
              className="object-cover w-full h-full"
            />
            <AvatarFallback className="object-cover w-full h-full rounded-full">
              <UserIcon />
            </AvatarFallback>
          </Avatar>
        </div>
        <div className="pl-11 w-full">
          {renderCommentContent}
          {renderCommentActions}
          {renderRepliesLine}
        </div>
      </div>

      {/* Recursive comment */}
      {replies.map((reply, index) => (
        <CommentComponent
          key={reply._id}
          onClickReply={onClickReply}
          onClickLike={onClickLike}
          onClickHide={onClickHide}
          comment={reply}
          isReply
          isLastReply={index === replies.length - 1}
          adminId={adminId}
          isHidden={reply.status === COMMUNITY_COMMENT_STATUS.HIDDEN}
          rootCommentId={rootCommentId}
        />
      ))}
    </>
  );
};

export { CommentComponent };
