import {
  Form
} from '@';
import { SingleDateTimePicker as SingleDateTimePickerField } from '@/components/btaskee/single-date-time-picker/single-time-picker';
import { BasilEditIcon, CalendarClockIcon ,PinIcon,UnpinIcon } from '@/components/svg/community';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { zodResolver } from '@hookform/resolvers/zod';
import { SerializeFrom } from '@remix-run/node';
import { Link } from '@remix-run/react';
import { POST_STATUS } from 'btaskee-constants';
import { momentTz } from 'btaskee-utils';
import { CircleArrowOutUpRight, CircleCheck, CircleOff, MoreHorizontal, X } from 'lucide-react';
import { useCallback, type FC } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

export const zodSchedulePostSchema = z.object({
  isSchedule: z.boolean().optional(),
    scheduleDate: z
      .date()
      .refine(
        date => {
          return momentTz(date).isAfter(momentTz());
        },
        {
          message: 'Invalid date',
        },
      )
      .optional()
});

interface PostActionsProps {
  isAskerPost: boolean;
  isPosted: boolean;
  canEdit: boolean;
  isPinned?: boolean;
  editUrlDestination: string;
  handleHidePost: () => void;
  handleSubmitSchedulePost: (data: z.infer<typeof zodSchedulePostSchema>) => void;
  canHidePost: boolean;
  initialData: SerializeFrom<Pick<CommunityPostDetail, 'scheduleTime' | 'status'>>;
  isCanSchedule?: boolean;
  isScheduleDialogOpen?: boolean;
  onScheduleDialogOpenChange?: (open: boolean) => void;
  isCanUnhidePost: boolean;
  handleUnhidePost: () => void;
  handlePinPost?: () => void;
  handleUnpinPost?: () => void;
}

const PostActions: FC<PostActionsProps> = ({
  isAskerPost,
  isPosted,
  isCanSchedule,
  canEdit,
  editUrlDestination,
  handleHidePost,
  handleSubmitSchedulePost,
  canHidePost,
  initialData,
  isScheduleDialogOpen = false,
  onScheduleDialogOpenChange,
  isCanUnhidePost = false,
  handleUnhidePost,
  handlePinPost,
  handleUnpinPost,
  isPinned,
}) => {
  const { t } = useTranslation('community-component');
  const handleDialogChange = useCallback((open: boolean) => {
    onScheduleDialogOpenChange?.(open);
  }, [onScheduleDialogOpenChange]);

  const schedulePostForm = useForm<z.infer<typeof zodSchedulePostSchema>>({
    resolver: zodResolver(zodSchedulePostSchema),
    defaultValues: {
      isSchedule: initialData?.scheduleTime ? true : false,
      scheduleDate: initialData?.scheduleTime
        ? momentTz(initialData?.scheduleTime).toDate()
        : undefined,
    },
  });
  const {
    handleSubmit,
  } = schedulePostForm;

  return (
    <div className="flex items-center justify-end w-1/2 gap-4">
      {(isPosted || isAskerPost) && canHidePost ? (
        <Button
          type="button"
          variant="outline"
          onClick={handleHidePost}
          className="gap-2 leading-tight h-fit border-primary text-primary hover:text-primary">
          <CircleOff 
            className="w-4 h-4" 
            color={"#F97316"} 
            strokeWidth={2} 
          />
          {t('HIDE_POST')}
        </Button>
      ) : null}

      {isCanSchedule ? (
        <Dialog open={isScheduleDialogOpen} onOpenChange={handleDialogChange}>
          <DialogTrigger asChild>
            <Button
              type="button"
              variant="outline"
              className="gap-2 leading-tight text-primary border-primary hover:text-primary h-fit">
              <CalendarClockIcon props={{ className: "w-4 h-4" }} />
              {t('POST_SCHEDULING')}
            </Button>
          </DialogTrigger>
          
          <DialogContent className="max-h-[90vh] max-w-[548px] overflow-y-auto">
            <DialogHeader className="space-y-2">
              <DialogTitle className="text-xl font-semibold tracking-tighter">
                {t('POST_SCHEDULING')}
              </DialogTitle>
            </DialogHeader>
            <Form {...schedulePostForm}>
              <form
                onSubmit={handleSubmit(handleSubmitSchedulePost)}
                className="space-y-2">
                <div className="flex flex-col items-start gap-4">
                  <SingleDateTimePickerField
                      triggerProps={{
                        disabled:
                          initialData?.status !== POST_STATUS.NOT_POSTED.label,
                      }}
                      form={schedulePostForm}
                      name="scheduleDate"
                      label={t('POST_SCHEDULING')}
                      format="HH:mm:ss dd/MM/yyyy"
                      disableTimePickers={['minutes', 'seconds']}
                      triggerClassName='w-full'
                      fieldWrapperClassName='w-full'
                    />
                </div>
                <div className="flex justify-end gap-4 !mt-4">
                  <DialogClose className="rounded-sm cursor-default">
                    <Button
                      type="button"
                      variant="outline"
                      className="border-primary text-primary hover:bg-primary-foreground hover:text-primary">
                      {t('CANCEL')}
                    </Button>
                  </DialogClose>
                  <Button type="submit" >
                    {t('CONFIRM')}
                  </Button>
                </div>
              </form>
            </Form>
            <DialogClose className="absolute z-10 p-1 bg-white rounded-full cursor-default right-4 top-4">
              <X className="w-4 h-4" strokeWidth={2} />
            </DialogClose>
          </DialogContent>
        </Dialog>
      ) : null}

      {isCanUnhidePost ? (
        <Button
          type="button"
          variant="outline"
          className="gap-2 leading-tight h-fit border-primary text-primary hover:text-primary"
          onClick={handleUnhidePost}
          >
          <CircleArrowOutUpRight 
            className="w-4 h-4" 
            color={"#F97316"} 
            strokeWidth={2} 
          />
          {t('UNHIDE_POST')}
        </Button>
      ) : null}

      {!isAskerPost && canEdit ? (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              type="button"
              variant="outline"
              className="gap-2 leading-tight whitespace-normal text-primary border-primary hover:text-primary h-fit">
              <MoreHorizontal 
                className="w-4 h-4" 
                color={"#F97316"} 
                strokeWidth={2} 
              />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[160px]">
            <Link to={editUrlDestination}>
              <DropdownMenuItem className="gap-2 cursor-pointer">
                <BasilEditIcon props={{ className: "w-4 h-4" }} />
                {t('EDIT')}
              </DropdownMenuItem>
            </Link>
            <DropdownMenuItem className="gap-2 cursor-pointer">
              <CalendarClockIcon props={{ className: "w-4 h-4" }} />
              {t('POST_SCHEDULING')}
            </DropdownMenuItem>
            {isPinned ? (
              <DropdownMenuItem 
                className="gap-2 cursor-pointer"
                onClick={handleUnpinPost}
              >
                <UnpinIcon
                  props={{
                    className: "w-4 h-4",
                    color: "#F97316",
                    fill: "#F97316",
                    strokeWidth: 2
                  }}
                />
                {t('Unpin post')}
              </DropdownMenuItem>
            ) : (
              <DropdownMenuItem 
                className="gap-2 cursor-pointer"
                onClick={handlePinPost}
              >
                <PinIcon 
                  props={{
                    className: "w-4 h-4",
                    color: "#F97316",
                    fill: "#F97316",
                    strokeWidth: 2
                  }}
                />
                {t('PIN_POST')}
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      ) : null}
    </div>
  );
};

export { PostActions };
