import { Button } from '@/components/ui/button';
import { DismissIcon } from '@/components/svg/community';
import { useTranslation } from 'react-i18next';

interface ReportActionProps {
  openDismissAllReportDialog: () => void;
  isDisableDismissAllPostButton: boolean;
}

const ReportAction = ({
  openDismissAllReportDialog,
  isDisableDismissAllPostButton,
}: ReportActionProps) => {
  const { t } = useTranslation('community-component');
  return (
    <div className="flex items-center w-1/2 justify-end gap-4">
      <Button
        type="button"
        variant="outline"
        onClick={openDismissAllReportDialog}
        disabled={isDisableDismissAllPostButton}
        className="gap-2 text-primary border-primary hover:text-primary h-fit leading-tight">
        <DismissIcon /> {t('DISMISS_ALL')}
      </Button>
    </div>
  );
};

export { ReportAction };
