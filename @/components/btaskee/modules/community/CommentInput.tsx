import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { EmojiIcon, PictureIcon } from '@/components/svg/community';
import { X } from 'lucide-react';
import { Dispatch, SetStateAction, forwardRef } from 'react';
import { useTranslation } from 'react-i18next';

interface CommentInputProps {
  value: string;
  setValue: Dispatch<SetStateAction<string>>;
  mention: string;
  onClickRemoveMention: (
    e: React.MouseEvent<SVGSVGElement, MouseEvent>,
  ) => void;
  onKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
}

const CommentInput = forwardRef<HTMLTextAreaElement, CommentInputProps>(({
  value,
  setValue,
  mention,
  onClickRemoveMention,
  onKeyDown,
}, ref) => {
  const { t } = useTranslation('community-component');

  return (
    <div className="flex items-center gap-4 bg-gray-100 rounded-2xl px-6">
      <div className="flex gap-4">
        <Button type="button" variant="ghost" size="icon" className="h-8 w-8">
          <PictureIcon />
        </Button>
        <Button type="button" variant="ghost" size="icon" className="h-8 w-8">
          <EmojiIcon />
        </Button>
      </div>
      <div
        className={`w-full relative py-6 flex flex-col gap-0.5 justify-start ${mention ? 'pt-8' : ''}`}>
        {mention ? (
          <div className="absolute top-1.5 left-0 flex items-center gap-2">
            <span className="text-gray-800 text-sm">{t('REPLYING_TO')}</span>
            <span className="text-blue-600 text-sm">{`@${mention} `}</span>
            <X
              className="cursor-pointer w-3 h-3"
              onClick={e => onClickRemoveMention(e)}
            />
          </div>
        ) : null}
        {/* TODO: In the future, this input will  */}
        <Textarea
          placeholder={t('INPUT_COMMENT')}
          rows={1}
          className="resize-none min-h-[unset] py-3 px-4 h-fit leading-tight border-0 outline-0"
          value={value}
          onKeyDown={onKeyDown}
          onChange={e => setValue(e.currentTarget.value)}
          ref={ref}
        />
      </div>
    </div>
  );
});

CommentInput.displayName = 'CommentInput';

export { CommentInput };
