import { DataTableProps } from '@/components/btaskee/TableBase';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { DropdownMenuTrigger } from '@radix-ui/react-dropdown-menu';
import { MixerHorizontalIcon } from '@radix-ui/react-icons';
import { Table } from '@tanstack/react-table';
import { useTranslation } from 'react-i18next';

interface DataTableViewOptionsProps<TData> {
  table: Table<TData>;
  localeAddress?: DataTableProps<TData, any>['localeAddress'];
}

export function DataTableViewOptions<TData>({
  table,
  localeAddress,
}: DataTableViewOptionsProps<TData>) {
  const { t:tLocaleAddresOutSide } = useTranslation(localeAddress || 'common');
  const { t: tCommon } = useTranslation('common');

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="hidden h-8 lg:flex text-gray-600 font-normal">
          <MixerHorizontalIcon className="mr-2 h-4 w-4" />
          {tCommon('VIEW_TOGGLE_COLLUMN_BUTTON_ON_BTASKEE_BTASKEE_TABLE')}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{tCommon('LIST_TOGGLE_COLUMNS_ON_BTASKEE_TABLE')}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {table
          .getAllColumns()
          .filter(
            column =>
              typeof column.accessorFn !== 'undefined' && column.getCanHide(),
          )
          .map(column => {
            return (
              <DropdownMenuCheckboxItem
                key={column.id}
                className="capitalize whitespace-nowrap"
                checked={column.getIsVisible()}
                onCheckedChange={value => column.toggleVisibility(!!value)}
                onSelect={e => e.preventDefault()}// Keep the dropdown open
              >
                {/* This will be a string where each uppercase character is prefixed with an underscore,
                and the entire string is converted to uppercase*/}
                {localeAddress
                  ? tLocaleAddresOutSide(column.id.replace(/([A-Z])/g, '_$1').toUpperCase())
                  : column.id}
              </DropdownMenuCheckboxItem>
            );
          })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
