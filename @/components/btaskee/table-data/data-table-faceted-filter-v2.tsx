import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { CheckIcon, PlusCircledIcon } from '@radix-ui/react-icons';
import { useSearchParams } from '@remix-run/react';
import {
  ComponentType,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';

interface OptionType {
  label: string;
  value: string;
  icon?: ComponentType<{ className?: string }>;
}

interface DataTableFacetedFilterPropsV2 {
  title?: string;
  options: OptionType[];
  keyFilter: string;
  defaultValues?: string;
  resetSignal?: number;
}

export function DataTableFacetedFilterV2({
  title,
  options,
  keyFilter,
  defaultValues,
  resetSignal,
}: DataTableFacetedFilterPropsV2) {
  const { t } = useTranslation('common');
  const [_, setSearchParams] = useSearchParams();

  useEffect(() => {
    if (resetSignal && resetSignal > 0) {
      setSelectedValues(
        new Set(defaultValues ? defaultValues.split(',').filter(Boolean) : []),
      );
    }
  }, [resetSignal, defaultValues]);

  // Local state for selected values
  const [selectedValues, setSelectedValues] = useState<Set<string>>(
    () =>
      new Set(defaultValues ? defaultValues.split(',').filter(Boolean) : []),
  );

  // Handle selection changes
  const handleSelectionChange = useCallback(
    (value: string) => {
      setSelectedValues(prev => {
        const newSet = new Set(prev);
        if (newSet.has(value)) {
          newSet.delete(value);
        } else {
          newSet.add(value);
        }

        const filterValues = Array.from(newSet);
        setSearchParams(
          params => {
            if (filterValues.length > 0) {
              params.set(keyFilter, filterValues.join(','));
            } else {
              params.delete(keyFilter);
            }
            params.delete('pageIndex');
            return params;
          },
          {
            replace: true,
            preventScrollReset: true,
          },
        );

        return newSet;
      });
    },
    [keyFilter, setSearchParams],
  );

  // Handle clear filters
  const handleClearFilters = useCallback(() => {
    const defaultValueSet = new Set(
      defaultValues ? defaultValues.split(',').filter(Boolean) : [],
    );
    setSelectedValues(defaultValueSet);
    setSearchParams(
      prevParams => {
        const newParams = new URLSearchParams(prevParams);
        newParams.delete(keyFilter);
        newParams.delete('pageIndex');
        // Always set the default values in URL if they exist
        if (defaultValues) {
          newParams.set(keyFilter, defaultValues);
        }
        return newParams;
      },
      {
        replace: true,
        preventScrollReset: true,
      },
    );
  }, [keyFilter, setSearchParams, defaultValues]);

  // Add this effect to sync URL params with selected values when component mounts
  useEffect(() => {
    if (defaultValues) {
      setSearchParams(
        prevParams => {
          const newParams = new URLSearchParams(prevParams);
          newParams.set(keyFilter, defaultValues);
          return newParams;
        },
        {
          replace: true,
          preventScrollReset: true,
        },
      );
    }
  }, []); // Run only once on mount

  // Memoize the selected badges display
  const selectedBadges = useMemo(() => {
    if (selectedValues.size === 0) return null;

    if (selectedValues.size > 2) {
      return (
        <Badge
          variant="secondary"
          className="rounded-md px-1 font-medium bg-blue-50 text-blue-500">
          {selectedValues.size} {t('SELECTED')}
        </Badge>
      );
    }

    return options
      .filter(option => selectedValues.has(option.value))
      .map(option => (
        <Badge
          variant="secondary"
          key={option.value}
          className="rounded-md px-1 font-medium bg-blue-50 text-blue-500">
          {option.label}
        </Badge>
      ));
  }, [selectedValues, options, t]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-8 border-dashed text-gray-600 font-normal">
          <PlusCircledIcon className="mr-2 h-4 w-4" />
          {title}
          {selectedValues.size > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <Badge
                variant="secondary"
                className="rounded-sm px-1 font-normal lg:hidden">
                {selectedValues.size}
              </Badge>
              <div className="hidden space-x-1 lg:flex">{selectedBadges}</div>
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0" align="start">
        <Command>
          <CommandInput placeholder={title} />
          <CommandList>
            <CommandEmpty>{t('NO_RESULTS_FOUND')}</CommandEmpty>
            <CommandGroup>
              {options.map(option => {
                const isSelected = selectedValues.has(option.value);
                return (
                  <CommandItem
                    key={option.value}
                    onSelect={() => handleSelectionChange(option.value)}>
                    <div
                      className={cn(
                        'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                        isSelected
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50 [&_svg]:invisible',
                      )}>
                      <CheckIcon className={cn('h-4 w-4')} />
                    </div>
                    {option.icon && (
                      <option.icon className="mr-2 h-4 w-4 text-muted-foreground" />
                    )}
                    <span>{option.label}</span>
                  </CommandItem>
                );
              })}
            </CommandGroup>
            {selectedValues.size > 0 && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={handleClearFilters}
                    className="justify-center text-center">
                    {t('CLEAR_FILTERS')}
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
