import { DateRangePicker } from '@/components/btaskee/DateRangePicker';
import { MonthPicker } from '@/components/btaskee/MonthPicker';
import { MonthRangePicker } from '@/components/btaskee/MonthRangePicker';
import { MonthYearPicker } from '@/components/btaskee/MonthYearPicker';
import { DataTableProps } from '@/components/btaskee/TableBase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MagnifyingGlassIcon } from '@radix-ui/react-icons';
import { useSearchParams } from '@remix-run/react';
import type { SortingState, Table } from '@tanstack/react-table';
import { momentTz } from 'btaskee-utils';
import debounce from 'lodash/debounce.js';
import { CircleX } from 'lucide-react';
import React, {
  Dispatch,
  ReactElement,
  SetStateAction,
  useCallback,
  useEffect,
} from 'react';
import { useTranslation } from 'react-i18next';

import { DataTableFacetedFilter } from './data-table-faceted-filter';
import { DataTableViewOptions } from './data-table-view-options';
import { cn } from '@/lib/utils';

interface DataTableToolbarProps<TData, TValue> {
  table: Table<TData>;
  search?: DataTableProps<TData, TValue>['search'];
  total: number;
  filterDate: DataTableProps<TData, TValue>['filterDate'];
  filters?: DataTableProps<TData, TValue>['filters'];
  setSorting: Dispatch<SetStateAction<SortingState>>;
  localeAddress?: DataTableProps<TData, TValue>['localeAddress'];
  isShowClearButton?: boolean;
  defaultSearchParams?: DataTableProps<TData, TValue>['defaultSearchParams'];
  toolbarAction?: ReactElement;
  disableViewOptions?: boolean;
  componentAfterFilter?: DataTableProps<TData, TValue>['componentAfterFilter'];
  isShowRecords?: boolean;
}

export function DataTableToolbar<TData, TValue>({
  table,
  search,
  total,
  filterDate,
  filters,
  setSorting,
  componentAfterFilter,
  localeAddress,
  defaultSearchParams = {},
  isShowClearButton = false,
  toolbarAction,
  disableViewOptions = false,
  isShowRecords = true,
}: DataTableToolbarProps<TData, TValue>) {
  const { t } = useTranslation('common');
  const [searchParams, setSearchParams] = useSearchParams();

  useEffect(() => {
    if (search) {
      const searchField = document.querySelector(
        `input[name="${search.name}"]`,
      );
      if (searchField instanceof HTMLInputElement) {
        searchField.value = searchParams.get(search.name) || '';
      }
    }
  }, [searchParams]);

  const handleKeyEnterDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const target = e.target as HTMLInputElement;

      setSearchParams(
        params => {
          params.set(search?.name || '', target.value || '');
          params.delete('pageIndex');
          return params;
        },
        {
          replace: true,
          preventScrollReset: true,
        },
      );
    }
  };

  const DateRangePickerBySearchParams = useCallback(() => {
    if (!filterDate) return null;

    switch (filterDate.mode) {
      case 'month-year':
        return (
          <MonthYearPicker
            mode={filterDate.selectMode}
            initialDateFrom={filterDate.defaultValue?.from}
            initialDateTo={filterDate.defaultValue?.to}
            variant={filterDate.variant}
            {...(filterDate.minDate && { minDate: filterDate.minDate })}
            {...(filterDate.maxDate && { maxDate: filterDate.maxDate })}
            onSelectMonth={rangeMonth => {
              setSearchParams(params => {
                params.set(
                  'month',
                  JSON.stringify(momentTz(rangeMonth).month() + 1),
                );
                params.set('year', JSON.stringify(momentTz(rangeMonth).year()));
                params.delete('pageIndex');

                return params;
              });
            }}
            onWholeYearSelect={rangeMonth => {
              setSearchParams(params => {
                params.set('year', JSON.stringify(momentTz(rangeMonth).year()));
                params.set('month', '');
                params.delete('pageIndex');

                return params;
              });
            }}
          />
        );
      case 'month':
        return (
          <MonthPicker
            onSelectMonth={rangeMonth => {
              setSearchParams(params => {
                params.set(filterDate?.name, JSON.stringify(rangeMonth));
                params.delete('pageIndex');

                return params;
              });
            }}
            initialMonth={filterDate?.defaultValue?.from}
          />
        );
      case 'range-month':
        return (
          <MonthRangePicker
            onMonthRangeSelect={monthRange => {
              setSearchParams(params => {
                params.set(filterDate?.name, JSON.stringify(monthRange));
                params.delete('pageIndex');

                return params;
              });
            }}
            defaultRangeMonth={filterDate?.defaultValue}
          />
        );
      default:
        return (
          <DateRangePicker
            onUpdate={value => {
              setSearchParams(
                params => {
                  params.set(filterDate?.name, JSON.stringify(value));
                  params.delete('pageIndex');
                  return params;
                },
                {
                  replace: true,
                  preventScrollReset: true,
                },
              );
            }}
            defaultRangeDateOptions={filterDate?.defaultRangeDateOptions}
            initialRangeDate={filterDate?.defaultValue || undefined}
            align="start"
          />
        );
    }
  }, [filterDate]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('search', value);
  };

  const handleDebouncedSearchChange = debounce(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearchParams(
        params => {
          params.set(search?.name || '', event.target.value || '');
          params.delete('pageIndex');
          return params;
        },
        { replace: true, preventScrollReset: true },
      );
    },
    500,
  );
  return (
    <div className="w-full flex gap-4 items-center sm:pb-2 align-middle">
      {isShowRecords ? (
        total !== null && total !== undefined ? (
          <span className="my-auto whitespace-nowrap rounded-md bg-blue-50 px-3 py-1.5 text-center text-sm text-blue">
            {t('TOTAL_RECORDS', { total })}
          </span>
        ) : (
          t('TOTAL_RECORDS', { total: 0 })
        )
      ) : null}
      {search ? (
        <div className="relative flex items-center">
          <MagnifyingGlassIcon className="absolute left-2 text-base text-gray-400" />
          {search.setValue ? (
            <Input
              type="search"
              value={search.value}
              placeholder={search.placeholder || t('SEARCH_PLACEHOLDER')}
              onChange={event => search.setValue?.(event.target.value)}
              onKeyDown={handleKeyEnterDown}
              className={cn('block h-8 w-[150px] pl-8 lg:w-[250px]', search?.className ?? '')}
            />
          ) : (
            <Input
              type="search"
              defaultValue={search.defaultValue}
              placeholder={search.placeholder || t('SEARCH_PLACEHOLDER')}
              onChange={
                search.searchByEnter
                  ? handleSearchChange
                  : handleDebouncedSearchChange
              }
              name={search.name}
              onKeyDown={search.searchByEnter ? handleKeyEnterDown : undefined}
              className={cn('block h-8 w-[150px] pl-8 lg:w-[250px]', search?.className ?? '')}
            />
          )}
        </div>
      ) : null}
      <DateRangePickerBySearchParams />
      <div
        className="flex gap-4 items-stretch overflow-x-auto p-1 -m-1"
        style={{ scrollbarWidth: 'thin' }}>
        {filters &&
          filters.map(filterItem => (
            <div key={filterItem.name} className="flex items-center">
              <DataTableFacetedFilter
                title={filterItem.placeholder}
                options={filterItem.options}
                keyFilter={filterItem.name}
                defaultValues={filterItem.value}
              />
            </div>
          ))}
      </div>
      {componentAfterFilter ?? null}
      <div className="ml-auto flex gap-4">
        {toolbarAction}

        {isShowClearButton &&
        searchParams?.size &&
        Array.from(searchParams.entries()).some(([_, value]) => value) ? (
          <Button
            variant="outline"
            onClick={() => {
              searchParams?.size &&
                setSearchParams(defaultSearchParams, {
                  replace: true,
                  preventScrollReset: true,
                });
              setSorting([]);
            }}
            className="gap-2 pl-2 pr-3 py-1.5 h-8 text-sm leading-tight font-normal text-gray-600">
            <CircleX className={'w-4 h-4'} /> {t('RESET')}
          </Button>
        ) : null}
      </div>

      {disableViewOptions ? null : (
        <DataTableViewOptions localeAddress={localeAddress} table={table} />
      )}
    </div>
  );
}
