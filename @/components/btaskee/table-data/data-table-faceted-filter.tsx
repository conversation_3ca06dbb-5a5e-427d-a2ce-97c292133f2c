import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { CheckIcon, PlusCircledIcon } from '@radix-ui/react-icons';
import { useSearchParams } from '@remix-run/react';
import * as React from 'react';
import { ComponentType, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface DataTableFacetedFilterProps<TData, TValue> {
  title?: string;
  options: {
    label: string;
    value: string;
    icon?: ComponentType<{ className?: string }>;
  }[];
  keyFilter: string;
  defaultValues?: string;
}

export function DataTableFacetedFilter<TData, TValue>({
  title,
  options,
  keyFilter,
  defaultValues,
}: DataTableFacetedFilterProps<TData, TValue>) {
  const { t } = useTranslation('common');
  const [selectedValues, setSelectedValues] = useState(
    new Set(defaultValues ? defaultValues.split(',') : []),
  );
  
  useEffect(() => {
    if (!defaultValues) return setSelectedValues(new Set());

    if (defaultValues)
      return setSelectedValues(new Set(defaultValues.split(',')));
  }, [defaultValues]);

  const [, setSearchParams] = useSearchParams();

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="h-8 border-dashed text-gray-600 font-normal">
          <PlusCircledIcon className="mr-2 h-4 w-4" />
          {title}
          {selectedValues?.size > 0 && (
            <>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <Badge
                variant="secondary"
                className="rounded-sm px-1 font-normal lg:hidden">
                {selectedValues.size}
              </Badge>
              <div className="hidden space-x-1 lg:flex">
                {selectedValues.size > 2 ? (
                  <Badge
                    variant="secondary"
                    className="rounded-md px-1 font-medium bg-blue-50 text-blue-500">
                    {selectedValues.size} {t('SELECTED')}
                  </Badge>
                ) : (
                  options
                    .filter(option => selectedValues.has(option.value))
                    .map(option => (
                      <Badge
                        variant="secondary"
                        key={option.value}
                        className="rounded-md px-1 font-medium bg-blue-50 text-blue-500">
                        {option.label}
                      </Badge>
                    ))
                )}
              </div>
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0" align="start">
        <Command>
          <CommandInput placeholder={title} />
          <CommandList>
            <CommandEmpty>{t('NO_RESULTS_FOUND')}</CommandEmpty>
            <CommandGroup>
              {options.map(option => {
                const isSelected = selectedValues.has(option.value);
                return (
                  <CommandItem
                    key={option.value}
                    onSelect={() => {
                      if (isSelected) {
                        selectedValues.delete(option.value);
                      } else {
                        selectedValues.add(option.value);
                      }
                      const filterValues = Array.from(selectedValues);

                      setSearchParams(params => {
                        params.set(keyFilter, filterValues.join(','));
                        params.delete('pageIndex');
                        return params;
                      }, {
                        replace: true,
                        preventScrollReset: true,
                      });
                    }}>
                    <div
                      className={cn(
                        'mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary',
                        isSelected
                          ? 'bg-primary text-primary-foreground'
                          : 'opacity-50 [&_svg]:invisible',
                      )}>
                      <CheckIcon className={cn('h-4 w-4')} />
                    </div>
                    {option.icon && (
                      <option.icon className="mr-2 h-4 w-4 text-muted-foreground" />
                    )}
                    <span>{option.label}</span>
                  </CommandItem>
                );
              })}
            </CommandGroup>
            {selectedValues.size > 0 && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={() => {
                      setSearchParams(prevParams => {
                        const newParams = new URLSearchParams(prevParams);
                        newParams.delete(keyFilter);
                        prevParams.delete('pageIndex');
                        return newParams;
                      }, {
                        replace: true,
                        preventScrollReset: true,
                      });
                      setSelectedValues(new Set());
                    }}
                    className="justify-center text-center">
                    {t('CLEAR_FILTERS')}
                  </CommandItem>
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
