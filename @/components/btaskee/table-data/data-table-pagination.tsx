import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DoubleArrowLeftIcon,
  DoubleArrowRightIcon,
} from '@radix-ui/react-icons';
import { useSearchParams } from '@remix-run/react';
import type { Table } from '@tanstack/react-table';
import { useTranslation } from 'react-i18next';

interface DataTablePaginationProps<TData> {
  table: Table<TData>;
}

export const defaultPageSize = [10, 20, 30, 40, 50] as const;
export type PageSizeClient = (typeof defaultPageSize)[number];

export function DataTablePagination<TData>({
  table,
}: DataTablePaginationProps<TData>) {
  const { t: tCommon } = useTranslation('common');
  const [, setSearchParams] = useSearchParams();

  return (
    <div className="px-2 flex items-center justify-end space-x-6 lg:space-x-8">
      <div className="flex items-center space-x-2">
        <p className="text-sm font-medium">{tCommon('ROW_PER_PAGES_ON_BTASKEE_TABLE')}</p>
        <Select
          value={`${table.getState().pagination.pageSize}`}
          onValueChange={value => {
            setSearchParams(params => {
              params.set('pageSize', value);
              return params;
            }, {
              replace: true,
              preventScrollReset: true,
            });
            table.setPageSize(Number(value));
          }}>
          <SelectTrigger className="h-8 w-[70px]">
            <SelectValue placeholder={table.getState().pagination.pageSize} />
          </SelectTrigger>
          <SelectContent side="top">
            {defaultPageSize.map(pageSize => (
              <SelectItem key={pageSize} value={`${pageSize}`}>
                {pageSize}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="flex w-[100px] items-center justify-center text-sm font-medium">
        Page {table.getState().pagination.pageIndex + 1} of{' '}
        {table.getPageCount() || 1}
      </div>
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          className="hidden h-8 w-8 p-0 lg:flex"
          onClick={() => {
            table.setPageIndex(0);
            setSearchParams(params => {
              params.set('pageIndex', '0');
              return params;
            }, {
              replace: true,
              preventScrollReset: true,
            });
          }}
          disabled={!table.getCanPreviousPage()}>
          <span className="sr-only">Go to first page</span>
          <DoubleArrowLeftIcon className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          className="h-8 w-8 p-0"
          onClick={() => {
            setSearchParams(params => {
              const old = Number(params.get('pageIndex'));
              params.set('pageIndex', (old - 1).toString());
              return params;
            }, {
              replace: true,
              preventScrollReset: true,
            });
            table.previousPage();
          }}
          disabled={!table.getCanPreviousPage()}>
          <span className="sr-only">Go to previous page</span>
          <ChevronLeftIcon className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          className="h-8 w-8 p-0"
          onClick={() => {
            table.nextPage();
            setSearchParams(params => {
              const old = Number(params.get('pageIndex'));
              params.set('pageIndex', (old + 1).toString());
              return params;
            }, {
              replace: true,
              preventScrollReset: true,
            });
          }}
          disabled={!table.getCanNextPage()}>
          <span className="sr-only">Go to next page</span>
          <ChevronRightIcon className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          className="hidden h-8 w-8 p-0 lg:flex"
          onClick={() => {
            table.setPageIndex(table.getPageCount() - 1);
            setSearchParams(params => {
              params.set('pageIndex', (table.getPageCount() - 1).toString());
              return params;
            }, {
              replace: true,
              preventScrollReset: true,
            });
          }}
          disabled={!table.getCanNextPage()}>
          <span className="sr-only">Go to last page</span>
          <DoubleArrowRightIcon className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
