import { DataTablePropsV2 } from '@/components/btaskee/BTaskeeTable';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MagnifyingGlassIcon } from '@radix-ui/react-icons';
import { useSearchParams } from '@remix-run/react';
import type { SortingState, Table } from '@tanstack/react-table';
import debounce from 'lodash/debounce.js';
import throttle from 'lodash/throttle.js';
import { CircleX } from 'lucide-react';
import React, {
  Dispatch,
  ReactElement,
  SetStateAction,
  useCallback,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';

import { DateRangePickerV2 } from '../DateRangePickerV2';
import SingleMonthYearPicker from '../MonthPickerV2';
import { DataTableFacetedFilterV2 } from './data-table-faceted-filter-v2';
import { DataTableViewOptionsV2 } from './data-table-view-options-v2';
import { cn } from '@/lib/utils';

interface DataTableToolbarProps<TData, TValue> {
  table: Table<TData>;
  searchInput?: DataTablePropsV2<TData, TValue>['searchInput'];
  total: number;
  initialFilterDate?: DataTablePropsV2<TData, TValue>['initialFilterDate'];
  initialFilters?: DataTablePropsV2<TData, TValue>['initialFilters'];
  setSorting: Dispatch<SetStateAction<SortingState>>;
  translationKey?: DataTablePropsV2<TData, TValue>['translationKey'];
  initialSearchParams?: DataTablePropsV2<TData, TValue>['initialSearchParams'];
  toolbarAction?: ReactElement;
  extraComponent?: ReactElement;
  disableViewOptions?: boolean;
  renderExtraToolbarComponent?: DataTablePropsV2<TData, TValue>['renderExtraToolbarComponent'];
}

export function DataTableToolbarV2<TData, TValue>({
  table,
  searchInput,
  total,
  initialFilterDate,
  initialFilters,
  setSorting,
  extraComponent = undefined,
  translationKey,
  initialSearchParams = {},
  toolbarAction,
  disableViewOptions = false,
  renderExtraToolbarComponent,
}: DataTableToolbarProps<TData, TValue>) {
  const { t } = useTranslation('common');
  const [searchParams, setSearchParams] = useSearchParams();

  const [inputValue, setInputValue] = useState(
    searchInput?.defaultValue?.toString() || '',
  );
  const [resetSignal, setResetSignal] = useState(0);

  // Render filters
  const renderFilters = useMemo(() => {
    if (!initialFilters?.length) return null;

    return (
      <div
        className="flex gap-4 items-stretch overflow-x-auto p-1 -m-1"
        style={{ scrollbarWidth: 'thin' }}>
        {initialFilters.map(filterItem => (
          <div key={filterItem.name} className="flex items-center">
            <DataTableFacetedFilterV2
              title={filterItem.placeholder}
              options={filterItem.options}
              keyFilter={filterItem.name}
              defaultValues={filterItem.value}
              resetSignal={resetSignal}
            />
          </div>
        ))}
      </div>
    );
  }, [initialFilters, resetSignal]);

  // Handle date range changes
  const handleDateRangeChange = useCallback(
    (value: any) => {
      setSearchParams(
        params => {
          params.set(initialFilterDate?.name || '', JSON.stringify(value));
          params.delete('pageIndex');
          return params;
        },
        {
          replace: true,
          preventScrollReset: true,
        },
      );
    },
    [initialFilterDate?.name, setSearchParams],
  );

  const handleMonthYearChange = useCallback(
    (value: any) => {
      setSearchParams(
        params => {
          params.set(initialFilterDate?.name || '', JSON.stringify(value));
          params.delete('pageIndex');

        return params;
      });
    },
    [],
  );

  const getDisbaledRangeDate = useCallback((date: Date) => {
    // Disable all dates before minDate and after maxDate in the calendar.
    // This ensures that only dates within the [minDate, maxDate] range are selectable.
    // The boundary dates (minDate and maxDate) themselves are enabled, so today is selectable if it matches.
    if (initialFilterDate?.minDate && date < initialFilterDate.minDate) return true;
    if (initialFilterDate?.maxDate && date > initialFilterDate.maxDate) return true;

    return false;
  }, [initialFilterDate?.minDate, initialFilterDate?.maxDate]);

  // Memoized DateRangePicker component
  const renderDateRangePicker = useMemo(() => {
    if (!initialFilterDate) return null;

    switch (initialFilterDate.mode) {
      case 'range-date':
        return (
          <DateRangePickerV2
            onUpdate={handleDateRangeChange}
            initialRangeDate={initialFilterDate?.defaultValue}
            calendarProps={{
              disabled: getDisbaledRangeDate,
            }}
            resetSignal={resetSignal}
            align="start"
          />
        );
      case 'month-year':
        return (
          <SingleMonthYearPicker
            mode="month-year"
            onChange={handleMonthYearChange}
            minDate={initialFilterDate?.minDate}
            maxDate={initialFilterDate?.maxDate}
            initialValue={initialFilterDate?.defaultValue}
            resetSignal={resetSignal}
          />
        );
      case 'month':
        return (
          <SingleMonthYearPicker
            mode="month"
            onChange={handleMonthYearChange}
            minDate={initialFilterDate?.minDate}
            maxDate={initialFilterDate?.maxDate}
            initialValue={initialFilterDate?.defaultValue}
            resetSignal={resetSignal}
          />
        );
      default:
        return (
          <DateRangePickerV2
            onUpdate={handleDateRangeChange}
            initialRangeDate={initialFilterDate?.defaultValue}
            resetSignal={resetSignal}
            calendarProps={{
              disabled: getDisbaledRangeDate,
            }}
            align="start"
          />
        );
    }
  }, [initialFilterDate?.mode, handleDateRangeChange]);

  const updateSearchParams = useCallback(
    (value: string) => {
      setSearchParams(
        params => {
          if (value) {
            params.set(searchInput?.name || '', value);
          } else {
            params.delete(searchInput?.name || '');
          }
          params.delete('pageIndex');
          return params;
        },
        { replace: true, preventScrollReset: true },
      );
    },
    [searchInput?.name, setSearchParams],
  );

  const handleKeyEnterDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && searchInput?.triggerMode === 'onEnter') {
      updateSearchParams(inputValue);
    }
  };

  const debouncedSearch = useMemo(
    () =>
      debounce(
        (value: string) => updateSearchParams(value),
        searchInput?.delayTime || 500,
      ),
    [updateSearchParams, searchInput?.delayTime],
  );

  const throttledSearchRef = useRef(
    throttle(
      (value: string) => updateSearchParams(value),
      searchInput?.delayTime || 500,
      { leading: true },
    ),
  );

  const handleSearchChangeFromOutside = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      // Update external state if callback provided
      searchInput?.setSearchInputValue?.(event.target.value);

      if (searchInput?.triggerMode === 'custom' && searchInput.customTrigger) {
        searchInput.customTrigger(event.target.value);
        return;
      }
    },
    [searchInput, setSearchParams],
  );

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    searchInput?.onChange?.(event);

    const value = event.target.value;
    setInputValue(value);

    if (searchInput?.triggerMode === 'custom' && searchInput.customTrigger) {
      // From outside of Table, we can trigger by any condition we want,
      // like at least 3 characters, regex as phone number, etc.
      searchInput.customTrigger(value);
      return;
    }

    switch (searchInput?.triggerMode) {
      case 'onChange':
        updateSearchParams(value);
        break;
      case 'onChangeDebounce':
        debouncedSearch(value);
        break;
      case 'onChangeThrottle':
        throttledSearchRef.current(value);
        break;
      default:
        debouncedSearch(value);
        break;
    }
  };

  const renderSearchWithSetSearchInputValue = useMemo(() => {
    if (!searchInput?.setSearchInputValue) return null;

    return (
      <Input
        type="search"
        defaultValue={searchInput.defaultValue}
        value={searchInput.defaultValue}
        placeholder={searchInput.placeholder || t('SEARCH_PLACEHOLDER')}
        onChange={handleSearchChangeFromOutside}
        onKeyDown={handleKeyEnterDown}
        className={cn(
          'block h-8 w-[150px] pl-8 lg:w-[250px]',
          searchInput.inputClassName,
        )}
      />
    );
  }, [searchInput?.setSearchInputValue, handleSearchChangeFromOutside]);

  return (
    <div className="w-full flex gap-4 items-center sm:pb-2 align-middle">
      {renderExtraToolbarComponent?.find(item => item.position === 'left')?.component}
      {total !== null && total !== undefined ? (
        <span className="my-auto rounded-md bg-blue-50 px-3 py-1.5 text-center text-sm text-blue">
          {t('TOTAL_RECORDS', { total })}
        </span>
      ) : null}

      {searchInput ? (
        <div className="relative flex items-center">
          <MagnifyingGlassIcon className="absolute left-2 text-base text-gray-400" />
          {!searchInput?.setSearchInputValue ? (
            <Input
              type="search"
              defaultValue={searchInput.defaultValue}
              value={inputValue}
              placeholder={searchInput.placeholder || t('SEARCH_PLACEHOLDER')}
              onChange={handleSearchChange}
              onKeyDown={handleKeyEnterDown}
              className={cn(
                'block h-8 w-[150px] pl-8 lg:w-[250px]',
                searchInput.inputClassName,
              )}
            />
          ) : (
            renderSearchWithSetSearchInputValue
          )}
        </div>
      ) : null}

      {renderDateRangePicker}

      {renderFilters}

      {extraComponent}

      <div className="ml-auto">
        {searchParams?.size &&
        Array.from(searchParams.entries()).some(([_, value]) => value) ? (
          <Button
            variant="outline"
            onClick={() => {
              searchParams?.size &&
                setSearchParams(initialSearchParams, {
                  replace: true,
                  preventScrollReset: true,
                });
              setSorting([]);
              setInputValue('');
              setResetSignal(prev => prev + 1);
            }}
            className="gap-2 pl-2 pr-3 py-1.5 h-8 text-sm leading-tight font-normal text-gray-600">
            <CircleX className={'w-4 h-4'} /> {t('RESET')}
          </Button>
        ) : null}

        {toolbarAction}
        {renderExtraToolbarComponent?.find(item => item.position === 'right')?.component}
      </div>

      {disableViewOptions ? null : (
        <DataTableViewOptionsV2 translationKey={translationKey} table={table} />
      )}
    </div>
  );
}
