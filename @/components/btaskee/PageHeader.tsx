import { Breadcrumbs } from "@/components/btaskee/Breadcrumbs";
import { Grid } from "@/components/btaskee/Grid";
import { Typography } from "@/components/btaskee/Typography";
import { FC } from "react";

interface PageHeaderProps {
    title: string;
}

const PageHeader = ({ title }: PageHeaderProps) => {
    return (
      <div className="flex bg-secondary p-4 justify-between items-center min-h-24 rounded-md mb-6">
        <Grid className="gap-3">
          <Typography className="capitalize" variant="h2">
            {title}
          </Typography>
          <Breadcrumbs />
        </Grid>
        {/* TODO: Add actions button for header here, or asChild prop */}
      </div>
    );
  }

export { PageHeader };