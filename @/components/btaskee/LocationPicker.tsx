import { LoadingSpinner } from '@/components/btaskee/LoadingSpinner';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import type { Libraries } from '@react-google-maps/api';
import {
  Autocomplete,
  CircleF,
  GoogleMap,
  MarkerF,
  useJsApiLoader,
} from '@react-google-maps/api';
import { DEFAULT_CENTER_LOCATION } from 'btaskee-constants';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface LocationBase {
  localizeKey: string;
  defaultValue?: CustomLocation;
}

interface ReadOnlyProps extends LocationBase {
  onlyRead: true;
  onLocationSelect?: (location: CustomLocation) => void;
}

interface EditableProps extends LocationBase {
  onlyRead?: false;
  onLocationSelect: (location: CustomLocation) => void;
}

type LocationPickerProps = ReadOnlyProps | EditableProps;

/**
 * LocationPicker is a component that provides an interactive interface for selecting and visualizing locations using Google Maps.
 * It includes address autocomplete, radius control, and map visualization features.
 *
 * @param {Object} props - Component props
 * @param {Function} props.onLocationSelect - Callback function that receives the selected location data
 * in the format: { address: string, lat: number, lng: number, radius: number }
 * @param {string} props.localizeKey - Translation namespace key for i18n
 * @param {Object} [props.defaultValue] - Initial location data
 * @param {string} props.defaultValue.address - Default address string
 * @param {number} props.defaultValue.lat - Default latitude
 * @param {number} props.defaultValue.lng - Default longitude
 * @param {number} props.defaultValue.radius - Default radius in meters
 * @param {boolean} [props.onlyRead=false] - Enable read-only mode
 *
 * @requires @react-google-maps/api
 * @requires btaskee-ui
 * @requires react-i18next
 * @requires window.GOOGLE_API_KEY
 *
 * @returns {JSX.Element} A form with address input, radius control, and map visualization
 */
const LocationPicker = ({
  onLocationSelect,
  localizeKey,
  defaultValue,
  onlyRead = false,
}: LocationPickerProps) => {
  const { t } = useTranslation(localizeKey);
  const [address, setAddress] = useState<string>(defaultValue?.address || '');
  const [radius, setRadius] = useState<number>(defaultValue?.radius || 0);
  const [error, setError] = useState<{ address?: string; radius?: string }>({});
  const [center, setCenter] = useState(
    defaultValue
      ? {
          lat: defaultValue.lat,
          lng: defaultValue.lng,
        }
      : DEFAULT_CENTER_LOCATION,
  );
  const [shouldShowMarker, setShouldShowMarker] = useState(!!defaultValue);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [autocomplete, setAutocomplete] =
    useState<google.maps.places.Autocomplete | null>(null);
  const circleRef = useRef<google.maps.Circle | null>(null);

  const libraries: Libraries = ['places'];

  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey:
      typeof window !== 'undefined' ? window.GOOGLE_API_KEY : '',
    libraries,
  });

  useEffect(() => {
    if (defaultValue) {
      setAddress(defaultValue.address);
      setRadius(defaultValue.radius);
      setCenter({
        lat: defaultValue.lat,
        lng: defaultValue.lng,
      });
      setShouldShowMarker(true);
    }
  }, [defaultValue]);

  const onLoad = useCallback(
    (autocomplete: google.maps.places.Autocomplete) => {
      setAutocomplete(autocomplete);
    },
    [],
  );

  const onPlaceChanged = useCallback(() => {
    if (autocomplete) {
      const place = autocomplete.getPlace();

      if (place.geometry?.location) {
        const lat = place.geometry.location.lat();
        const lng = place.geometry.location.lng();
        const selectedAddress = place.formatted_address || '';

        setAddress(selectedAddress);
        setCenter({ lat, lng });
        setShouldShowMarker(true);

        if (map) {
          map.setCenter({ lat, lng });
          map.setZoom(15);
        }

        onLocationSelect?.({
          address: selectedAddress,
          lat,
          lng,
          radius,
        });
      }
    }
  }, [autocomplete, radius, onLocationSelect, map]);

  const onMapLoad = useCallback(
    (map: google.maps.Map) => {
      setMap(map);
      if (defaultValue) {
        map.setCenter({
          lat: defaultValue.lat,
          lng: defaultValue.lng,
        });
        map.setZoom(15);
      }
    },
    [defaultValue],
  );

  const handleRadiusChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newRadius = Number(e.target.value);
      setRadius(newRadius);
      setError(prev => ({ ...prev, radius: undefined }));
      if (circleRef.current) {
        circleRef.current.setRadius(newRadius);
      }
      if (map && shouldShowMarker) {
        onLocationSelect?.({
          address,
          lat: center.lat,
          lng: center.lng,
          radius: newRadius,
        });
      }
    },
    [address, center, onLocationSelect, map, shouldShowMarker],
  );

  const onCircleLoad = useCallback(
    (circle: google.maps.Circle) => {
      circleRef.current = circle;
      if (defaultValue?.radius) {
        circle.setRadius(defaultValue.radius);
      } else if (radius) {
        circle.setRadius(radius);
      }
    },
    [defaultValue?.radius, radius],
  );

  const onCircleUnmount = useCallback(() => {
    if (circleRef.current) {
      circleRef.current.setMap(null);
      circleRef.current = null;
    }
  }, []);

  const onMarkerLoad = useCallback(() => {
    setShouldShowMarker(true);
  }, []);

  if (!isLoaded) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label>{t('ADDRESS')}</Label>
        <Autocomplete onLoad={onLoad} onPlaceChanged={onPlaceChanged}>
          <Input
            disabled={onlyRead}
            type="text"
            id="address"
            value={address}
            onChange={e => {
              setAddress(e.target.value);
              setError(prev => ({ ...prev, address: undefined }));
            }}
            placeholder={t('ENTER_ADDRESS')}
            required
          />
        </Autocomplete>
        {error.address && (
          <p className="text-sm text-red-500">{error.address}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label>{t('RADIUS')}</Label>
        <Input
          disabled={onlyRead}
          type="number"
          id="radius"
          value={radius}
          onChange={handleRadiusChange}
          min="100"
          max="50000"
          required
        />
        {error.radius && <p className="text-sm text-red-500">{error.radius}</p>}
      </div>

      <div className="h-[400px] w-full overflow-hidden rounded-lg">
        <GoogleMap
          mapContainerStyle={{ width: '100%', height: '100%' }}
          center={center}
          zoom={15}
          onLoad={onMapLoad}>
          {shouldShowMarker && (
            <>
              <MarkerF position={center} onLoad={onMarkerLoad} />
              <CircleF
                center={center}
                radius={radius}
                onLoad={onCircleLoad}
                onUnmount={onCircleUnmount}
                options={{
                  fillColor: '#3b82f6',
                  fillOpacity: 0.2,
                  strokeColor: '#3b82f6',
                  strokeOpacity: 0.8,
                  strokeWeight: 2,
                }}
              />
            </>
          )}
        </GoogleMap>
      </div>
    </div>
  );
};

export { LocationPicker };
