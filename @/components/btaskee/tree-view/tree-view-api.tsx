import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import * as AccordionPrimitive from "@radix-ui/react-accordion";
import { Link } from "@remix-run/react";
import { ChevronDown } from "lucide-react";
import React, {
  createContext,
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useState
} from "react";

type TreeViewElement = {
  id: string;
  name: string;
  isSelectable?: boolean;
  children?: TreeViewElement[];
};

type TreeContextProps = {
  selectedId: string | undefined;
  expendedItems: string[] | undefined;
  indicator: boolean;
  handleExpand: (id: string) => void;
  selectItem: (id: string) => void;
  setExpendedItems?: React.Dispatch<React.SetStateAction<string[] | undefined>>;
  openIcon?: React.ReactNode;
  closeIcon?: React.ReactNode;
  direction: "rtl" | "ltr";
};

const TreeContext = createContext<TreeContextProps | null>(null);

const useTree = () => {
  const context = useContext(TreeContext);
  if (!context) {
    throw new Error("useTree must be used within a TreeProvider");
  }
  return context;
};

interface TreeViewComponentProps extends React.HTMLAttributes<HTMLDivElement> { }

type Direction = "rtl" | "ltr" | undefined;

type TreeViewProps = {
  initialSelectedId?: string;
  indicator?: boolean;
  elements?: TreeViewElement[];
  initialExpendedItems?: string[];
  openIcon?: React.ReactNode;
  closeIcon?: React.ReactNode;
} & TreeViewComponentProps;

const Tree = forwardRef<HTMLDivElement, TreeViewProps>(
  (
    {
      className,
      elements,
      initialSelectedId,
      initialExpendedItems,
      children,
      indicator = true,
      openIcon,
      closeIcon,
      dir,
      ...props
    },
    ref
  ) => {
    const [selectedId, setSelectedId] = useState<string | undefined>(
      initialSelectedId
    );
    const [expendedItems, setExpendedItems] = useState<string[] | undefined>(
      initialExpendedItems
    );

    const selectItem = useCallback((id: string) => {
      setSelectedId(id);
    }, []);

    const handleExpand = useCallback((id: string) => {
      setExpendedItems((prev) => {
        if (prev?.includes(id)) {
          return prev.filter((item) => item !== id);
        }
        return [...(prev ?? []), id];
      });
    }, []);

    const expandSpecificTargetedElements = useCallback(
      (elements?: TreeViewElement[], selectId?: string) => {
        if (!elements || !selectId) return;
        const findParent = (
          currentElement: TreeViewElement,
          currentPath: string[] = []
        ) => {
          const isSelectable = currentElement.isSelectable ?? true;
          const newPath = [...currentPath, currentElement.id];
          if (currentElement.id === selectId) {
            if (isSelectable) {
              setExpendedItems((prev) => [...(prev ?? []), ...newPath]);
            } else {
              if (newPath.includes(currentElement.id)) {
                newPath.pop();
                setExpendedItems((prev) => [...(prev ?? []), ...newPath]);
              }
            }
            return;
          }
          if (
            isSelectable &&
            currentElement.children &&
            currentElement.children.length > 0
          ) {
            currentElement.children.forEach((child) => {
              findParent(child, newPath);
            });
          }
        };
        elements.forEach((element) => {
          findParent(element);
        });
      },
      []
    );

    useEffect(() => {
      if (initialSelectedId) {
        expandSpecificTargetedElements(elements, initialSelectedId);
      }
    }, [initialSelectedId, elements]);

    const direction = dir === "rtl" ? "rtl" : "ltr";

    return (
      <TreeContext.Provider
        value={{
          selectedId,
          expendedItems,
          handleExpand,
          selectItem,
          setExpendedItems,
          indicator,
          openIcon,
          closeIcon,
          direction,
        }}
      >
        <div className={cn("size-full", className)}>
          <ScrollArea
            ref={ref}
            className="h-full relative px-2"
            dir={dir as Direction}
          >
            <AccordionPrimitive.Root
              {...props}
              type="multiple"
              defaultValue={expendedItems}
              value={expendedItems}
              className="flex flex-col gap-1"
              onValueChange={(value) =>
                setExpendedItems((prev) => [...(prev ?? []), value[0]])
              }
              dir={dir as Direction}
            >
              {children}
            </AccordionPrimitive.Root>
          </ScrollArea>
        </div>
      </TreeContext.Provider>
    );
  }
);

Tree.displayName = "Tree";

const TreeIndicator = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const { direction } = useTree();

  return (
    <div
      dir={direction}
      ref={ref}
      className={cn(
        "h-full w-px bg-gray-200 absolute left-1.5 rtl:right-1.5 py-3 rounded-md hover:bg-gray-500 duration-300 ease-in-out",
        className
      )}
      {...props}
    />
  );
});

TreeIndicator.displayName = "TreeIndicator";

interface FolderComponentProps
  extends React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item> { }

type FolderProps = {
  expendedItems?: string[];
  element: string;
  isSelectable?: boolean;
  isSelect?: boolean;
} & FolderComponentProps;

const Folder = forwardRef<
  HTMLDivElement,
  FolderProps & React.HTMLAttributes<HTMLDivElement>
>(
  (
    {
      className,
      element,
      value,
      isSelectable = true,
      isSelect,
      children,
      id,
      ...props
    },
    ref
  ) => {
    const {
      direction,
      handleExpand,
      expendedItems,
      indicator,
      setExpendedItems,
      openIcon,
      closeIcon
    } = useTree();

    return (
      <AccordionPrimitive.Item
        {...props}
        value={value}
        className="relative overflow-hidden h-full "
      >
        <div className="flex justify-between items-center">
          <Link to={`/settings/groups/${value}` || ''} className="flex items-center gap-1">
            {expendedItems?.includes(value)
              ? openIcon ?? <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6.08625 12.75C5.74125 12.75 5.45325 12.6345 5.22225 12.4035C4.99075 12.172 4.875 11.8837 4.875 11.5387V3.46125C4.875 3.11625 4.99075 2.82825 5.22225 2.59725C5.45325 2.36575 5.74125 2.25 6.08625 2.25H14.1637C14.5087 2.25 14.7968 2.36575 15.0278 2.59725C15.2593 2.82825 15.375 3.11625 15.375 3.46125V11.5387C15.375 11.8837 15.2595 12.172 15.0285 12.4035C14.797 12.6345 14.5087 12.75 14.1637 12.75H6.08625ZM3.83625 15C3.49125 15 3.20325 14.8845 2.97225 14.6535C2.74075 14.422 2.625 14.1337 2.625 13.7887V4.96125H3.375V13.7887C3.375 13.9038 3.423 14.0095 3.519 14.106C3.6155 14.202 3.72125 14.25 3.83625 14.25H12.6637V15H3.83625ZM9.9525 5.2785H14.625V3.46125C14.625 3.34625 14.577 3.2405 14.481 3.144C14.3845 3.048 14.2788 3 14.1637 3H9.95175L9.9525 5.2785Z" fill="#F97316" />
              </svg>
              : closeIcon ?? <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6.08625 12.75C5.74125 12.75 5.45325 12.6345 5.22225 12.4035C4.99075 12.172 4.875 11.8837 4.875 11.5387V3.46125C4.875 3.11625 4.99075 2.82825 5.22225 2.59725C5.45325 2.36575 5.74125 2.25 6.08625 2.25H14.1637C14.5087 2.25 14.7968 2.36575 15.0278 2.59725C15.2593 2.82825 15.375 3.11625 15.375 3.46125V11.5387C15.375 11.8837 15.2595 12.172 15.0285 12.4035C14.797 12.6345 14.5087 12.75 14.1637 12.75H6.08625ZM3.83625 15C3.49125 15 3.20325 14.8845 2.97225 14.6535C2.74075 14.422 2.625 14.1337 2.625 13.7887V4.96125H3.375V13.7887C3.375 13.9038 3.423 14.0095 3.519 14.106C3.6155 14.202 3.72125 14.25 3.83625 14.25H12.6637V15H3.83625ZM9.9525 5.2785H14.625V3.46125C14.625 3.34625 14.577 3.2405 14.481 3.144C14.3845 3.048 14.2788 3 14.1637 3H9.95175L9.9525 5.2785Z" fill="#F97316" />
              </svg>
            }
            <span className="text-sm font-medium text-gray">{element}</span>
          </Link>
          <AccordionPrimitive.Trigger
            className={cn(
              `flex items-center gap-1 text-sm rounded-md`,
              className,
              {
                "bg-muted rounded-md": isSelect && isSelectable,
                "cursor-pointer": isSelectable,
                "cursor-not-allowed opacity-50": !isSelectable,
              }
            )}
            disabled={!isSelectable}
            onClick={() => handleExpand(value)}
          >
            <ChevronDown
              className={cn(
                "w-4 h-4 text-gray-500 duration-200 ease-in-out transform",
                {
                  "rotate-0": expendedItems?.includes(value),
                  "rotate-180": !expendedItems?.includes(value),
                }
              )} />
          </AccordionPrimitive.Trigger>

        </div>

        <AccordionPrimitive.Content className="text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down relative overflow-hidden h-full">
          {element && indicator && <TreeIndicator aria-hidden="true" />}
          <AccordionPrimitive.Root
            dir={direction}
            type="multiple"
            className="flex flex-col gap-1 py-1 ml-5 rtl:mr-5 "
            defaultValue={expendedItems}
            value={expendedItems}
            onValueChange={(value) => {
              setExpendedItems?.((prev) => [...(prev ?? []), value[0]]);
            }}
          >
            {children}
          </AccordionPrimitive.Root>
        </AccordionPrimitive.Content>
      </AccordionPrimitive.Item>
    );
  }
);

Folder.displayName = "Folder";

const File = forwardRef<
  HTMLButtonElement,
  {
    value: string;
    handleSelect?: (id: string) => void;
    isSelectable?: boolean;
    isSelect?: boolean;
    fileIcon?: React.ReactNode;
  } & React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>
>(
  (
    {
      value,
      className,
      handleSelect,
      isSelectable = true,
      isSelect,
      fileIcon,
      children,
      ...props
    },
    ref
  ) => {
    const { direction, selectedId, selectItem } = useTree();
    const isSelected = isSelect ?? selectedId === value;
    return (
      <Link to={`/settings/groups/${value}` || ''} className="flex items-center gap-1">
        <AccordionPrimitive.Item value={value} className="relative">
          <AccordionPrimitive.Trigger
            ref={ref}
            {...props}
            dir={direction}
            disabled={!isSelectable}
            aria-label="File"
            className={cn(
              "flex items-center gap-1 cursor-pointer text-sm pr-1 rtl:pl-1 rtl:pr-0 rounded-md  duration-200 ease-in-out font-medium text-gray",
              {
                "bg-muted": isSelected && isSelectable,
              },
              isSelectable ? "cursor-pointer" : "opacity-50 cursor-not-allowed",
              className
            )}
            onClick={() => selectItem(value)}
          >

            {fileIcon ?? <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6.08625 12.75C5.74125 12.75 5.45325 12.6345 5.22225 12.4035C4.99075 12.172 4.875 11.8837 4.875 11.5387V3.46125C4.875 3.11625 4.99075 2.82825 5.22225 2.59725C5.45325 2.36575 5.74125 2.25 6.08625 2.25H14.1637C14.5087 2.25 14.7968 2.36575 15.0278 2.59725C15.2593 2.82825 15.375 3.11625 15.375 3.46125V11.5387C15.375 11.8837 15.2595 12.172 15.0285 12.4035C14.797 12.6345 14.5087 12.75 14.1637 12.75H6.08625ZM3.83625 15C3.49125 15 3.20325 14.8845 2.97225 14.6535C2.74075 14.422 2.625 14.1337 2.625 13.7887V4.96125H3.375V13.7887C3.375 13.9038 3.423 14.0095 3.519 14.106C3.6155 14.202 3.72125 14.25 3.83625 14.25H12.6637V15H3.83625ZM9.9525 5.2785H14.625V3.46125C14.625 3.34625 14.577 3.2405 14.481 3.144C14.3845 3.048 14.2788 3 14.1637 3H9.95175L9.9525 5.2785Z" fill="#737373" />
            </svg>
            }
            {children}
          </AccordionPrimitive.Trigger>
        </AccordionPrimitive.Item>
      </Link >

    );
  }
);

File.displayName = "File";

const CollapseButton = forwardRef<
  HTMLButtonElement,
  {
    elements: TreeViewElement[];
    expandAll?: boolean;
  } & React.HTMLAttributes<HTMLButtonElement>
>(({ className, elements, expandAll = false, children, ...props }, ref) => {
  const { expendedItems, setExpendedItems } = useTree();

  const expendAllTree = useCallback((elements: TreeViewElement[]) => {
    const expandTree = (element: TreeViewElement) => {
      const isSelectable = element.isSelectable ?? true;
      if (isSelectable && element.children && element.children.length > 0) {
        setExpendedItems?.((prev) => [...(prev ?? []), element.id]);
        element.children.forEach(expandTree);
      }
    };

    elements.forEach(expandTree);
  }, []);

  const closeAll = useCallback(() => {
    setExpendedItems?.([]);
  }, []);

  useEffect(() => {
    if (expandAll) {
      expendAllTree(elements);
    }
  }, [expandAll]);

  return (
    <Button
      variant={"ghost"}
      className="h-8 w-fit p-1 absolute bottom-1 right-2"
      onClick={
        expendedItems && expendedItems.length > 0
          ? closeAll
          : () => expendAllTree(elements)
      }
      ref={ref}
      {...props}
    >
      {children}
      <span className="sr-only">Toggle</span>
    </Button>
  );
});

CollapseButton.displayName = "CollapseButton";

export { CollapseButton, File, Folder, Tree, type TreeViewElement };
