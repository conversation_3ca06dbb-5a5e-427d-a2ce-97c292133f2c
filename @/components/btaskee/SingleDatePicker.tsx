import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { momentTz } from 'btaskee-utils';
import { CalendarIcon } from 'lucide-react';
import React from 'react';
import { RegisterOptions, UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

export interface DatePickerProps {
  /** The form object from react-hook-form */
  form: UseFormReturn<any>;
  /** The name of the form field */
  name: string;
  /** The label for the date picker */
  label?: string;
  /** Additional rules for form validation */
  rules?: RegisterOptions;
  /** Default time to set when a date is selected (format: "HH:mm:ss") */
  defaultTime?: string;
  /** If true, sets the time to the start of the day */
  isStartDate?: boolean;
  /** If true, sets the time to the end of the day */
  isEndDate?: boolean;
}

/**
 * A single date picker component with customizable time settings.
 *
 * This component provides a form field with a date picker, integrated with react-hook-form.
 * It allows users to select a date from a calendar interface and optionally set a specific time.
 *
 * @param props - The component props
 * @param props.form - The form object from react-hook-form (UseFormReturn<any>)
 * @param props.name - The name of the form field (string)
 * @param props.label - The label for the date picker (string, optional)
 * @param props.rules - Additional rules for form validation (RegisterOptions, optional)
 * @param props.defaultTime - Default time to set when a date is selected (string, format: "HH:mm:ss", optional)
 * @param props.isStartDate - If true, sets the time to the start of the day (boolean, optional)
 * @param props.isEndDate - If true, sets the time to the end of the day (boolean, optional)
 *
 * @returns A form field component with a date picker
 *
 * @example
 * <SingleDatePicker
 *   form={form}
 *   name="eventDate"
 *   label="Event Date"
 *   defaultTime="09:00:00"
 * />
 */
const SingleDatePicker = React.forwardRef<HTMLInputElement, DatePickerProps>(
  ({ form, name, label, rules, defaultTime, isStartDate, isEndDate }) => {
    const { t } = useTranslation('common');

    const formatDate = (date: Date) => {
      return momentTz(date).format('MMM DD YYYY, HH:mm:ss');
    };

    return (
      <FormField
        control={form.control}
        name={name}
        rules={rules}
        render={({ field }) => (
          <FormItem className="flex flex-col space-y-0">
            <FormLabel>{label}</FormLabel>
            <Popover>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant={'outline'}
                    className={cn(
                      'w-full pl-3 text-left font-normal',
                      !field.value && 'text-muted-foreground',
                    )}
                    ref={field.ref}>
                    {field.value ? (
                      formatDate(field.value)
                    ) : (
                      <span>{t('PICK_A_DATE')}</span>
                    )}
                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={field.value}
                  onSelect={date => {
                    if (date) {
                      let newDate: Date;
                      if (isStartDate) {
                        newDate = momentTz(date).startOf('day').toDate();
                      } else if (isEndDate) {
                        newDate = momentTz(date).endOf('day').toDate();
                      } else if (defaultTime) {
                        newDate = momentTz(date)
                          .set({
                            hour: parseInt(defaultTime.split(':')[0]),
                            minute: parseInt(defaultTime.split(':')[1]),
                            second: parseInt(defaultTime.split(':')[2]),
                          })
                          .toDate();
                      } else {
                        newDate = momentTz(date).toDate();
                      }
                      field.onChange(newDate);
                    }
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            <FormMessage className="mt-2 italic font-normal" />
          </FormItem>
        )}
      />
    );
  },
);

SingleDatePicker.displayName = 'SingleDatePicker';

export { SingleDatePicker };
