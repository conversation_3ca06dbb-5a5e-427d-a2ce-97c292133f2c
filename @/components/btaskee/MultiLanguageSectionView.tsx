import { Grid } from '@/components/btaskee/Grid';
import { Typography } from '@/components/btaskee/Typography';
import {
  IconENFlag,
  IconIDFlag,
  IconKOFlag,
  IconMYFlag,
  IconTHFlag,
  IconVNFlag,
} from '@/components/svg/flags';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Label } from '@/components/ui/label';
import { CheckCheck } from 'lucide-react';
import { ReactNode } from 'react';

type TLanguage = 'vi' | 'en' | 'ko' | 'th' | 'id' | 'ms';

type FlatData = {
  [key: string]: {
    [K in TLanguage]?: string;
  };
};

type NestedData = {
  [K in TLanguage]?: {
    [key: string]: string;
  };
};

type ParentNestedData = {
  [parent: string]: {
    [K in TLanguage]?: {
      [key: string]: string;
    };
  };
};

export const ITEMS_LANGUAGE: Array<{
  label: string;
  value: TLanguage;
  icon: ReactNode;
}> = [
  {
    value: 'vi',
    label: 'Vietnamese',
    icon: <IconVNFlag />,
  },
  {
    value: 'en',
    label: 'English',
    icon: <IconENFlag />,
  },
  {
    value: 'ko',
    label: 'Korean',
    icon: <IconKOFlag />,
  },
  {
    value: 'th',
    label: 'Thailand',
    icon: <IconTHFlag />,
  },
  {
    value: 'id',
    label: 'Indonesian',
    icon: <IconIDFlag />,
  },
  {
    value: 'ms',
    label: 'Malaysia',
    icon: <IconMYFlag />,
  },
];

interface MultiLanguageSectionProps {
  data: FlatData | NestedData | ParentNestedData;
  fields: {
    [key: string]: string;
  };
  parentField?: string;
  useNestedStructure?: boolean;
}

const MultiLanguageSectionView = ({
  data,
  fields,
  parentField,
  useNestedStructure = false,
}: MultiLanguageSectionProps) => {
  const getValue = (
    fieldName: string,
    languageValue: TLanguage,
  ): string | undefined => {
    if (useNestedStructure) {
      if (parentField) {
        return (data as ParentNestedData)[parentField]?.[languageValue]?.[
          fieldName
        ];
      }
      return (data as NestedData)[languageValue]?.[fieldName];
    }
    return (data as FlatData)[fieldName]?.[languageValue];
  };
  const isLanguageComplete = (languageValue: TLanguage): boolean => {
    return Object.keys(fields).every(fieldName => {
      const value = getValue(fieldName, languageValue);
      return !!value;
    });
  };
  return (
    <Accordion type="multiple" className="border">
      {ITEMS_LANGUAGE.map((language, languageIndex) => (
        <AccordionItem key={languageIndex} value={`item-${languageIndex}`}>
          <AccordionTrigger key={language.value} className="bg-gray-100 p-4">
            <Label className="flex items-center gap-2">
              {language.icon}
              {language.label}
              {isLanguageComplete(language.value) && (
                <CheckCheck className="text-green-500" />
              )}
            </Label>
          </AccordionTrigger>
          <AccordionContent className="flex flex-col justify-between p-6">
            {Object.keys(fields).map((fieldName, childIndex) => (
              <Grid key={childIndex} className="mb-4 gap-1">
                <Label className="text-xs font-normal text-gray-400">
                  {fields[fieldName]}
                </Label>
                <Typography
                  variant="p"
                  affects="removePMargin"
                  className="text-gray-600 break-all">
                  {getValue(fieldName, language.value)}
                </Typography>
              </Grid>
            ))}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};

export { MultiLanguageSectionView };
