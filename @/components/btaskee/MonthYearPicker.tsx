import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button, buttonVariants } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { momentTz } from 'btaskee-utils';
import { format } from 'date-fns';
import { CalendarIcon, ChevronLeft, ChevronRight } from 'lucide-react';
import type { HTMLAttributes } from 'react';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

type Month = {
  number: number;
  name: string;
};

const MONTHS: Month[][] = [
  [
    { number: 0, name: 'Jan' },
    { number: 1, name: 'Feb' },
    { number: 2, name: 'Mar' },
    { number: 3, name: 'Apr' },
  ],
  [
    { number: 4, name: 'May' },
    { number: 5, name: 'Jun' },
    { number: 6, name: 'Jul' },
    { number: 7, name: 'Aug' },
  ],
  [
    { number: 8, name: 'Sep' },
    { number: 9, name: 'Oct' },
    { number: 10, name: 'Nov' },
    { number: 11, name: 'Dec' },
  ],
];

type MonthCalProps = {
  currentSelectedDate?: Date;
  onMonthSelect?: (date: Date) => void | Promise<void>;
  onYearForward?: () => void;
  onYearBackward?: () => void;
  onWholeYearSelect?: (date: Date) => void | Promise<void>;
  callbacks?: {
    yearLabel?: (year: number) => string;
    monthLabel?: (month: Month) => string;
  };
  variant?: {
    calendar?: {
      main?: ButtonVariant;
      selected?: ButtonVariant;
    };
    chevrons?: ButtonVariant;
  };
  minDate?: Date;
  maxDate?: Date;
  disabledDates?: Date[];
};

type ButtonVariant =
  | 'default'
  | 'outline'
  | 'ghost'
  | 'link'
  | 'destructive'
  | 'secondary'
  | null
  | undefined;

function MonthYearPicker({
  onSelectMonth,
  minDate,
  maxDate,
  disabledDates,
  callbacks,
  onYearBackward,
  onYearForward,
  variant,
  className,
  initialDateFrom,
  initialDateTo,
  mode,
  onWholeYearSelect,
  ...props
}: HTMLAttributes<HTMLDivElement> &
  Omit<MonthCalProps, 'currentSelectedDate' | 'onMonthSelect'> & {
    initialDateFrom?: Date;
    initialDateTo?: Date;
    onSelectMonth: (selectedMonth: Date) => void | Promise<void>;
    mode?: 'month' | 'year';
  }) {
  const [selectedMonth, setSelectedMonth] = useState(() => {
    if (initialDateFrom && initialDateTo) {
      return momentTz(initialDateFrom).startOf('month').toDate();
    }
    return momentTz().startOf('month').toDate();
  });
  const [isWholeYearSelected, setIsWholeYearSelected] = useState(mode === 'year');
  const { t: tCommon } = useTranslation('common');

  const buttonContent = useMemo(() => {
    if (!selectedMonth) return <span>{tCommon('PICK_A_MONTH')}</span>;
    return isWholeYearSelected
      ? format(selectedMonth, 'yyyy')
      : format(selectedMonth, 'MMM yyyy');
  }, [selectedMonth, isWholeYearSelected, tCommon]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'max-h-8 justify-start px-6 text-left font-normal',
            !selectedMonth && 'text-muted-foreground',
          )}>
          <CalendarIcon className="mr-2 h-4 w-4" />
          {buttonContent}
        </Button>
      </PopoverTrigger>
      <PopoverContent>
        <div className={cn('p-3', className)} {...props}>
          <div className="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
            <div className="w-full space-y-4">
              <MonthCal
                onMonthSelect={selectedMonthValue => {
                  setSelectedMonth(selectedMonthValue);
                  setIsWholeYearSelected(false);
                  onSelectMonth(selectedMonthValue);
                }}
                callbacks={callbacks}
                currentSelectedDate={selectedMonth}
                onYearBackward={onYearBackward}
                onYearForward={onYearForward}
                variant={variant}
                minDate={minDate}
                maxDate={maxDate}
                disabledDates={disabledDates}
                isWholeYearSelected={isWholeYearSelected}
                setIsWholeYearSelected={setIsWholeYearSelected}
                onWholeYearSelect={onWholeYearSelect}
                setSelectedMonth={setSelectedMonth}
              />
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

function MonthCal({
  currentSelectedDate,
  onMonthSelect,
  callbacks,
  variant,
  minDate,
  maxDate,
  disabledDates,
  onYearBackward,
  onYearForward,
  onWholeYearSelect,
  isWholeYearSelected,
  setIsWholeYearSelected,
  setSelectedMonth,
}: MonthCalProps & {
  isWholeYearSelected: boolean;
  setIsWholeYearSelected: (isWholeYearSelected: boolean) => void;
  setSelectedMonth: (selectedMonth: Date) => void;
}) {
  const [year, setYear] = useState<number>(
    currentSelectedDate
      ? momentTz(currentSelectedDate).year()
      : momentTz().year(),
  );
  const [month, setMonth] = useState<number>(
    currentSelectedDate
      ? momentTz(currentSelectedDate).month()
      : momentTz().month(),
  );
  const [menuYear, setMenuYear] = useState<number>(year);

  const { t: tCommon } = useTranslation('common');

  const disabledDatesMapped = useMemo(
    () =>
      disabledDates?.map(d => {
        const momentDate = momentTz(d);
        return { year: momentDate.year(), month: momentDate.month() };
      }),
    [disabledDates],
  );

  const isDateDisabled = (year: number, month: number) => {
    if (
      maxDate &&
      (year > momentTz(maxDate).year() ||
        (year === momentTz(maxDate).year() &&
          month > momentTz(maxDate).month()))
    ) {
      return true;
    }
    if (
      minDate &&
      (year < momentTz(minDate).year() ||
        (year === momentTz(minDate).year() &&
          month < momentTz(minDate).month()))
    ) {
      return true;
    }
    return (
      disabledDatesMapped?.some(d => d.year === year && d.month === month) ??
      false
    );
  };

  const handleYearChange = (selectedYear: number) => {
    setMenuYear(selectedYear);
    setYear(selectedYear);
    setSelectedMonth(momentTz({ year: selectedYear, month }).toDate());
    setIsWholeYearSelected(true);
    if (onWholeYearSelect) {
      onWholeYearSelect(
        momentTz({ year: selectedYear }).startOf('year').toDate(),
      );
    }
  };

  const renderYearOptions = () => {
    const currentYear = momentTz().year();
    const startYear = 2015;
    const years = [];

    for (let year = currentYear; year >= startYear; year--) {
      years.push(
        <SelectItem key={year} value={year.toString()}>
          {year}
        </SelectItem>,
      );
    }

    return years;
  };

  return (
    <>
      <div className="relative flex items-center justify-center pt-1">
        <div className="text-sm font-medium">
          {callbacks?.yearLabel ? callbacks.yearLabel(menuYear) : menuYear}
        </div>
        <div className="flex items-center space-x-1">
          <Button
            type="button"
            onClick={() => {
              setMenuYear(menuYear - 1);
              if (onYearBackward) onYearBackward();
            }}
            disabled={minDate && menuYear <= momentTz(minDate).year()}
            className={cn(
              buttonVariants({ variant: variant?.chevrons ?? 'outline' }),
              'absolute left-1 inline-flex h-7 w-7 items-center justify-center p-0',
            )}>
            <ChevronLeft className="h-4 w-4 opacity-50 text-gray-800" />
          </Button>
          <Button
            type="button"
            onClick={() => {
              setMenuYear(menuYear + 1);
              if (onYearForward) onYearForward();
            }}
            disabled={maxDate && menuYear >= momentTz(maxDate).year()}
            className={cn(
              buttonVariants({ variant: variant?.chevrons ?? 'outline' }),
              'absolute right-1 inline-flex h-7 w-7 items-center justify-center p-0',
            )}>
            <ChevronRight className="h-4 w-4 opacity-50 text-gray-800" />
          </Button>
        </div>
      </div>
      <table className="w-full border-collapse space-y-1">
        <tbody>
          {MONTHS.map((monthRow, rowIndex) => (
            <tr key={`row-${rowIndex}`} className="mt-2 flex w-full gap-1">
              {monthRow.map(m => (
                <td
                  key={m.number}
                  className="[&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent relative h-10 w-1/4 p-0 text-center text-sm focus-within:relative focus-within:z-20 first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md [&:has([aria-selected].day-range-end)]:rounded-r-md">
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => {
                      setMonth(m.number);
                      setYear(menuYear);
                      if (
                        onMonthSelect &&
                        typeof onMonthSelect === 'function'
                      ) {
                        onMonthSelect(
                          momentTz({
                            year: menuYear,
                            month: m.number,
                          }).toDate(),
                        );
                      }
                    }}
                    disabled={isDateDisabled(menuYear, m.number)}
                    className={cn(
                      buttonVariants({
                        variant:
                          isWholeYearSelected &&
                          menuYear === year &&
                          momentTz({
                            year: menuYear,
                            month: m.number,
                          }).isSameOrBefore(momentTz())
                            ? 'default'
                            : isWholeYearSelected
                              ? 'ghost'
                              : month === m.number && menuYear === year
                                ? variant?.calendar?.selected ?? 'default'
                                : variant?.calendar?.main ?? 'ghost',
                      }),
                      'h-full w-full p-0 font-normal aria-selected:opacity-100',
                    )}>
                    {callbacks?.monthLabel ? callbacks.monthLabel(m) : m.name}
                  </Button>
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>

      <div className="mt-4 flex justify-center">
        <Select
          value={menuYear.toString()}
          onValueChange={value => handleYearChange(parseInt(value, 10))}>
          <SelectTrigger className="min-w-20">
            <SelectValue>{menuYear}</SelectValue>
          </SelectTrigger>
          <SelectContent>{renderYearOptions()}</SelectContent>
        </Select>
        <Button
          variant="outline"
          className="ml-2"
          onClick={() => {
            const selectedYear = momentTz({ year: menuYear })
              .startOf('year')
              .toDate();
            if (onWholeYearSelect) {
              setYear(menuYear);
              onWholeYearSelect(selectedYear);
              setIsWholeYearSelected(true);
              setSelectedMonth(selectedYear);
            }
          }}>
          {tCommon('SELECT_YEAR')}
        </Button>
      </div>
    </>
  );
}

MonthYearPicker.displayName = 'MonthYearPicker';

export { MonthYearPicker };
