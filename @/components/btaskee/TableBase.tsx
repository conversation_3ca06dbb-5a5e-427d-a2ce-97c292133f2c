import {
  DateRangePicker,
  type DateRangePickerOptions,
} from '@/components/btaskee/DateRangePicker';
import { DebouncedInput } from '@/components/btaskee/DebounceInput';
import { DataTableFacetedFilter } from '@/components/btaskee/table-data/data-table-faceted-filter';
import { DataTablePagination } from '@/components/btaskee/table-data/data-table-pagination';
import { DataTableToolbar } from '@/components/btaskee/table-data/data-table-toolbar';
import { VectorEmptyDataTable } from '@/components/svg/empty-data-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useSearchParams } from '@remix-run/react';
import type {
  Column,
  ColumnDef,
  ColumnFiltersState,
  ColumnPinningState,
  PaginationState,
  Row,
  RowSelectionState,
  SortingState,
  VisibilityState,
} from '@tanstack/react-table';
import {
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { RowData } from '@tanstack/table-core';
import { momentTz } from 'btaskee-utils';
import type { CSSProperties, InputHTMLAttributes, ReactElement } from 'react';
import { Fragment, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { NavigateOptions } from 'react-router';
import { URLSearchParamsInit } from 'react-router-dom';

const MAX_WIDTH_SCREEN = 1392;

export interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  // onDoubleClickRow, onClickRow is an optional function that is triggered when user double click or click a row in the table.
  onDoubleClickRow?: (record: TData) => void | Promise<void>;
  onClickRow?: (record: TData) => void | Promise<void>;
  total: number;
  pagination?: PaginationState;
  defaultSearchParams?: URLSearchParamsInit;
  filterDate?: {
    name: string;
    mode?: 'month' | 'range-month' | 'range-date' | 'month-year';
    variant?: {
      chevrons?: 'outline' | 'default';
    };
    selectMode?: 'month' | 'year';
    minDate?: Date;
    maxDate?: Date;
    defaultValue?: {
      from: Date;
      to: Date;
    };
    defaultRangeDateOptions?: DateRangePickerOptions;
  };
  columnVisibilityFromOutSide?: VisibilityState;
  search?: {
    name: string;
    defaultValue: string;
    placeholder?: string;
    setValue?: (newValue: string) => void;
    value?: string;
    searchByEnter?: boolean;
    className?: InputHTMLAttributes<HTMLInputElement>['className'];
  };
  filters?: Array<{
    placeholder: string;
    options: OptionType[];
    name: string;
    value: string;
    className?: InputHTMLAttributes<HTMLInputElement>['className'];
  }>;
  columnPinningFromOutSide?: ColumnPinningState;
  extraContent?: ReactElement;
  renderSubComponent?: (props: { row: Row<TData> }) => ReactElement;
  getRowCanExpand?: (row: Row<TData>) => boolean;
  localeAddress?: string;
  componentAfterFilter?: React.ReactNode;
  isShowClearButton?: boolean;
  toolbarAction?: ReactElement;
  disableViewOptions?: boolean;
  isShowRecords?: boolean;
}

declare module '@tanstack/react-table' {
  //allows us to define custom properties for our columns
  interface ColumnMeta<TData extends RowData, TValue> {
    filterVariant?: 'text' | 'range' | 'select' | 'date';
    filterOptions?: OptionType[];
  }
}

const FilterTableColumn = <TData, TValue>({
  column,
  setSearchParams,
}: {
  column: Column<TData, TValue>;
  setSearchParams: (
    params: (params: URLSearchParams) => URLSearchParams,
    navigateOpts?: NavigateOptions,
  ) => void;
}) => {
  const { t } = useTranslation('common');
  const columnFilterValue = column.getFilterValue();
  const { filterVariant, filterOptions } = column.columnDef.meta ?? {};
  const [isMounted, setIsMounted] = useState(false);

  const updateSearchParams = (
    key: Column<TData, TValue>['id'],
    value: InputHTMLAttributes<HTMLInputElement>['value'],
  ) => {
    if (isMounted) {
      setSearchParams(
        params => {
          if (params.get(key) !== value?.toString()) {
            params.set(key, value?.toString() ?? '');
          }
          return params;
        },
        {
          replace: true,
          preventScrollReset: true,
        },
      );
    }
  };

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (filterVariant === 'range') {
    return (
      <div className="flex space-x-1`">
        <DebouncedInput
          type="number"
          min={0}
          value={(columnFilterValue as [number, number])?.[0] ?? ''}
          defaultValue={(columnFilterValue as [number, number])?.[0] ?? ''}
          onChange={value => updateSearchParams(`${column.id}_min`, value)}
          placeholder={t('MIN')}
          className="w-[72px] h-8 focus-visible:ring-0 focus-visible:ring-offset-0"
        />
        <DebouncedInput
          type="number"
          min={0}
          value={(columnFilterValue as [number, number])?.[1] ?? ''}
          defaultValue={(columnFilterValue as [number, number])?.[1] ?? ''}
          onChange={value => updateSearchParams(`${column.id}_max`, value)}
          placeholder={t('MAX')}
          className="w-[72px] h-8 focus-visible:ring-0 focus-visible:ring-offset-0"
        />
      </div>
    );
  }

  if (filterVariant === 'select') {
    return (
      <DataTableFacetedFilter
        title={t('SELECT')}
        options={filterOptions || []}
        keyFilter={column.id}
        defaultValues={columnFilterValue?.toString() || ''}
      />
    );
  }

  if (filterVariant === 'date') {
    return (
      <DateRangePicker
        className={'ml-0'}
        initialRangeDate={{
          from: momentTz().toDate(),
          to: momentTz().toDate(),
        }}
        onUpdate={value => updateSearchParams(column.id, JSON.stringify(value))}
      />
    );
  }

  return (
    <DebouncedInput
      type="search"
      className={'h-8'}
      onChange={value => updateSearchParams(column.id, value)}
      placeholder={t('SEARCH')}
      value={(columnFilterValue ?? '') as string}
    />
  );
};

const BTaskeeTable = <TData, TValue>({
  data,
  total,
  pagination,
  columns,
  search,
  filters,
  filterDate,
  columnVisibilityFromOutSide,
  componentAfterFilter,
  columnPinningFromOutSide = { left: undefined, right: undefined },
  extraContent,
  defaultSearchParams,
  onClickRow,
  onDoubleClickRow,
  renderSubComponent,
  getRowCanExpand = () => false,
  localeAddress,
  isShowClearButton = false,
  toolbarAction,
  disableViewOptions = false,
  isShowRecords = true,
}: DataTableProps<TData, TValue>) => {
  const [searchParams, setSearchParams] = useSearchParams();

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const sortParams = searchParams.get('sort');

  const [sorting, setSorting] = useState<SortingState>([
    ...(sortParams
      ? sortParams.split(',').map(sort => {
          const [id, desc] = sort.split(':');
          return {
            id,
            desc: desc === 'desc',
          };
        })
      : []),
  ]);

  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnPinning, setColumnPinning] = useState<ColumnPinningState>({
    left: [],
    right: [],
  });

  const getCommonPinningStyles = (column: Column<TData>): CSSProperties => {
    const isPinned = column.getIsPinned();
    const isLastLeftPinnedColumn =
      isPinned === 'left' && column.getIsLastColumn('left');
    const isFirstRightPinnedColumn =
      isPinned === 'right' && column.getIsFirstColumn('right');

    return {
      boxShadow: isLastLeftPinnedColumn
        ? '-4px 0 4px -4px gray inset'
        : isFirstRightPinnedColumn
          ? '4px 0 4px -4px gray inset'
          : undefined,
      left: isPinned === 'left' ? `${column.getStart('left')}px` : undefined,
      right: isPinned === 'right' ? `${column.getAfter('right')}px` : undefined,
      opacity: isPinned ? 0.95 : 1,
      position: isPinned ? 'sticky' : 'relative',
      width: isPinned ? column.columnDef.size : column.getSize(),
      zIndex: isPinned ? 1 : 0,
      backgroundColor: isPinned ? 'white' : 'inherit',
      minWidth: isPinned ? column.columnDef.size : undefined,
      maxWidth: isPinned ? column.columnDef.size : undefined,
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap',
    };
  };

  const table = useReactTable({
    data,
    columns,
    getRowCanExpand,
    state: {
      sorting,
      columnPinning: { ...columnPinning, ...columnPinningFromOutSide },
      columnVisibility: { ...columnVisibility, ...columnVisibilityFromOutSide },
      rowSelection,
      columnFilters,
      ...(pagination ? { pagination } : {}),
    },
    rowCount: total,
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnPinningChange: setColumnPinning,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getExpandedRowModel: getExpandedRowModel(),
  });

  useEffect(() => {
    if (sorting.length) {
      const sortValue = sorting
        .map(sort => {
          return `${sort.id}:${sort.desc ? 'desc' : 'asc'}`;
        })
        .join(','); // e.g. "name:asc,age:desc"

      setSearchParams(
        params => {
          params.set('sort', sortValue);
          return params;
        },
        {
          replace: true,
          preventScrollReset: true,
        },
      );
    }
  }, [sorting, setSearchParams]);

  return (
    <div className="space-y-4">
      <DataTableToolbar<TData, TValue>
        search={search}
        filterDate={filterDate}
        table={table}
        filters={filters}
        total={total}
        componentAfterFilter={componentAfterFilter}
        defaultSearchParams={defaultSearchParams}
        localeAddress={localeAddress}
        isShowClearButton={isShowClearButton}
        setSorting={setSorting}
        toolbarAction={toolbarAction}
        disableViewOptions={disableViewOptions}
        isShowRecords={isShowRecords}
      />
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups()?.map(headerGroup => (
              <TableRow className="bg-gray-100" key={headerGroup.id}>
                {headerGroup.headers?.map(header => (
                  <TableHead
                    style={getCommonPinningStyles(header.column)}
                    key={header.id}
                    colSpan={header.colSpan}
                    className={
                      header.column.columnDef.enableColumnFilter ? 'h-16' : ''
                    }>
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                    {header.column.columnDef.enableColumnFilter ? (
                      <FilterTableColumn
                        column={header.column}
                        setSearchParams={setSearchParams}
                      />
                    ) : null}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getCoreRowModel().rows?.length ? (
              table.getCoreRowModel().rows?.map(row => (
                <Fragment key={row.id}>
                  <TableRow
                    className={onDoubleClickRow ? 'cursor-pointer' : ''}
                    onDoubleClick={() => onDoubleClickRow?.(row.original)}
                    onClick={() => onClickRow?.(row.original)}
                    data-state={
                      row.getIsSelected() ? 'selected' : 'unselected'
                    }>
                    {row.getVisibleCells()?.map(cell => (
                      <TableCell
                        key={cell.id}
                        style={getCommonPinningStyles(cell.column)}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                  {row.getIsExpanded() && renderSubComponent && (
                    <TableRow>
                      <TableCell colSpan={row.getVisibleCells().length}>
                        {renderSubComponent({ row })}
                      </TableCell>
                    </TableRow>
                  )}
                </Fragment>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="text-center">
                  <VectorEmptyDataTable className="inline py-12" />
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {extraContent}
      {pagination ? <DataTablePagination table={table} /> : null}
    </div>
  );
};

export { BTaskeeTable };
