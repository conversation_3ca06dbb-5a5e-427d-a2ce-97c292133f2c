import { DateInput } from '@/components/btaskee/DateInput';
import { SelectBase } from '@/components/btaskee/SelectBase';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { CalendarIcon, CheckIcon } from '@radix-ui/react-icons';
import { momentTz } from 'btaskee-utils';
import { type FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

export enum DATE_RANGE_PICKER_OPTIONS {
  NONE = 'NONE',
  TODAY = 'TODAY',
  YESTERDAY = 'YESTERDAY',
  LAST_7_DAYS = 'LAST_7_DAYS',
  LAST_14_DAYS = 'LAST_14_DAYS',
  LAST_30_DAYS = 'LAST_30_DAYS',
  THIS_MONTH = 'THIS_MONTH',
  LAST_MONTH = 'LAST_MONTH',
  THIS_YEAR = 'THIS_YEAR',
  LAST_YEAR = 'LAST_YEAR',
}

export type DateRangePickerOptions = keyof typeof DATE_RANGE_PICKER_OPTIONS;

export type DateRangeProps = {
  from: Date;
  to: Date;
} | undefined;

const MEDIUM_SCREEN_WIDTH = 768; //768 is 'md' breakpoint in

export interface DateRangePickerProps {
  onUpdate?: (value: DateRangeProps) => void;
  initialRangeDate?: DateRangeProps;
  align?: 'start' | 'center' | 'end';
  className?: string;
  defaultRangeDateOptions?: DateRangePickerOptions;
  formatDateTriggerButtonText?: string;
}

export const DateRangePicker: FC<DateRangePickerProps> = ({
  onUpdate,
  align = 'end',
  initialRangeDate,
  className,
  defaultRangeDateOptions = DATE_RANGE_PICKER_OPTIONS.LAST_30_DAYS,
  formatDateTriggerButtonText = 'DD/MM/YYYY',
}): JSX.Element => {
  const { t } = useTranslation('common');
  const defaultRangeDate = useMemo(
    () => (initialRangeDate ? {
      from: momentTz(initialRangeDate.from).startOf('day').toDate(),
      to: initialRangeDate.to
        ? momentTz(initialRangeDate.to).endOf('day').toDate()
        : momentTz(initialRangeDate.from).endOf('day').toDate(),
    } : undefined),
    [initialRangeDate],
  );

  const [isOpen, setIsOpen] = useState(false);
  const [range, setRange] = useState<DateRangeProps | undefined>(defaultRangeDate);
  const [tempRange, setTempRange] = useState<DateRangeProps | undefined>(defaultRangeDate);
  const [selectedPreset, setSelectedPreset] = useState<
    DateRangePickerOptions | string
  >(defaultRangeDateOptions === DATE_RANGE_PICKER_OPTIONS.NONE ? '' : defaultRangeDateOptions);

  const [tempSelectedPreset, setTempSelectedPreset] = useState<
    DateRangePickerOptions | string
  >(defaultRangeDateOptions === DATE_RANGE_PICKER_OPTIONS.NONE ? '' : defaultRangeDateOptions);

  const [isSmallScreen, setIsSmallScreen] = useState(
    typeof window !== 'undefined'
      ? window.innerWidth < MEDIUM_SCREEN_WIDTH
      : false,
  );
  useEffect(() => {
    const handleResize = (): void =>
      setIsSmallScreen(window.innerWidth < MEDIUM_SCREEN_WIDTH);

    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const getPresetRange = (
    presetRange: DateRangePickerOptions,
  ): DateRangeProps => {
    const preset = Object.values(DATE_RANGE_PICKER_OPTIONS).find(
      option => option === presetRange,
    );
    if (!preset) throw new Error(`Unknown date range preset: ${presetRange}`);

    let from = momentTz().startOf('day');
    let to = momentTz().endOf('day');

    switch (preset) {
      case DATE_RANGE_PICKER_OPTIONS.TODAY:
        break;
      case DATE_RANGE_PICKER_OPTIONS.YESTERDAY:
        from = from.subtract(1, 'day').startOf('day');
        to = to.subtract(1, 'day').endOf('day');
        break;
      case DATE_RANGE_PICKER_OPTIONS.LAST_7_DAYS:
        from = from.subtract(7, 'day').startOf('day');
        break;
      case DATE_RANGE_PICKER_OPTIONS.LAST_14_DAYS:
        from = from.subtract(14, 'day').startOf('day');
        break;
      case DATE_RANGE_PICKER_OPTIONS.LAST_30_DAYS:
        from = from.subtract(30, 'day').startOf('day');
        break;
      case DATE_RANGE_PICKER_OPTIONS.THIS_MONTH:
        from = from.startOf('month');
        to = to.endOf('month');
        break;
      case DATE_RANGE_PICKER_OPTIONS.LAST_MONTH:
        from = from.subtract(1, 'month').startOf('month');
        to = to.subtract(1, 'month').endOf('month');
        break;
      case DATE_RANGE_PICKER_OPTIONS.THIS_YEAR:
        from = from.startOf('year');
        to = to.endOf('year');
        break;
      case DATE_RANGE_PICKER_OPTIONS.LAST_YEAR:
        from = from.subtract(1, 'year').startOf('year');
        to = to.subtract(1, 'year').endOf('year');
        break;
      default:
        break;
    }

    return { from: from.toDate(), to: to.toDate() };
  };

  const PresetButton = useCallback(
    ({
      isSelected,
      preset,
    }: {
      isSelected: boolean;
      preset: DateRangePickerOptions;
    }): JSX.Element => (
      <Button
        className={cn(isSelected && 'pointer-events-none')}
        variant="ghost"
        onClick={() => {
          const range = getPresetRange(preset);
          setTempRange(range);
          setTempSelectedPreset(preset);
        }}>
        <>
          <span className={cn('pr-2 opacity-0', isSelected && 'opacity-70')}>
            <CheckIcon width={18} height={18} />
          </span>
          {t(preset)}
        </>
      </Button>
    ),
    [getPresetRange, setTempRange, setTempSelectedPreset, t],
  );

  const onCancel = useCallback(() => {
    setIsOpen(false);
    setTempRange(range);
    setTempSelectedPreset(selectedPreset);
  }, [range, selectedPreset]);

  const onApply = useCallback(() => {
    setIsOpen(false);
    setRange(tempRange);
    setSelectedPreset(tempSelectedPreset);
    onUpdate?.(tempRange);
  }, [tempRange, tempSelectedPreset, onUpdate]);

  return (
    <Popover modal={true} open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          className={cn('h-8 px-3', className)}
          size="lg"
          variant="outline">
          <div className={`flex items-center gap-4 font-normal ${range ? 'text-foreground' : 'text-muted-foreground'}`}>
            <CalendarIcon />
            <span className="py-1">
              {range ? `${momentTz(range?.from).format(formatDateTriggerButtonText)}${range?.to != null ? ' - ' + momentTz(range?.to).format(formatDateTriggerButtonText) : ''}` : t('SELECT_DATE_RANGE_ON_BTASKEE_TABLE')}
            </span>
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align={align}
        className="w-auto"
        onInteractOutside={onCancel}>
        <div className="flex items-stretch">
          <div className="flex flex-col gap-2">
            <div className="flex justify-center gap-2">
              <DateInput
                value={tempRange?.from}
                onChange={date => {
                  const toDate =
                    !tempRange?.to || momentTz(date).isAfter(tempRange?.to)
                      ? momentTz(date).endOf('day').toDate()
                      : momentTz(tempRange?.to).endOf('day').toDate();

                  setTempRange(prevRange => ({
                    ...prevRange,
                    from: momentTz(date).startOf('day').toDate(),
                    to: toDate,
                  }));
                }}
              />
              <span className="py-1">-</span>
              <DateInput
                value={tempRange?.to}
                onChange={date => {
                  const fromDate = momentTz(date).isBefore(tempRange?.from)
                    ? momentTz(date).startOf('day').toDate()
                    : momentTz(tempRange?.from).startOf('day').toDate();
                  setTempRange(prevRange => ({
                    ...prevRange,
                    from: fromDate,
                    to: momentTz(date).endOf('day').toDate(),
                  }));
                }}
              />
            </div>

            {isSmallScreen && (
              <SelectBase
                backgroundColor="h-[unset]"
                options={Object.values(DATE_RANGE_PICKER_OPTIONS).filter(option => option !== DATE_RANGE_PICKER_OPTIONS.NONE).map(
                  option => ({ label: t(option), value: option }),
                )}
                onValueChange={value => {
                  const range = getPresetRange(value as DateRangePickerOptions);
                  setTempRange(range);
                  setTempSelectedPreset(value as DateRangePickerOptions);
                }}
                defaultValue={defaultRangeDateOptions === DATE_RANGE_PICKER_OPTIONS.NONE ? '' : defaultRangeDateOptions}
                value={tempSelectedPreset}
                placeholder="Select a preset date"
              />
            )}

            <Calendar
              mode="range"
              selected={tempRange}
              onSelect={(value: { from?: Date; to?: Date } | undefined) => {
                setTempRange(value ? {
                  from: momentTz(value.from).startOf('day').toDate(),
                  to: value.to
                    ? momentTz(value.to).endOf('day').toDate()
                    : momentTz(value.from).endOf('day').toDate(),
                } : undefined);
                setTempSelectedPreset('');
              }}
              numberOfMonths={isSmallScreen ? 1 : 2}
              defaultMonth={
                isSmallScreen
                  ? momentTz().startOf('month').toDate()
                  : momentTz().subtract(1, 'month').startOf('month').toDate()
              }
            />
          </div>

          {!isSmallScreen && (
            <div className="flex w-full flex-col items-start">
              {Object.values(DATE_RANGE_PICKER_OPTIONS).map(preset =>{
              if(preset === DATE_RANGE_PICKER_OPTIONS.NONE) return;
              return <PresetButton
                isSelected={tempSelectedPreset === preset}
                key={preset}
                preset={preset}
              />
              })}
            </div>
          )}
        </div>
        <div className="mt-2 flex justify-end gap-2">
          <Button onClick={onCancel} variant="ghost">
            {t('CANCEL')}
          </Button>
          <Button onClick={onApply}>{t('APPLY')}</Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};

DateRangePicker.displayName = 'DateRangePicker';
