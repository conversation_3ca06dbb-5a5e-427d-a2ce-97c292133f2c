import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import * as SelectPrimitive from '@radix-ui/react-select';
import { createUID } from 'btaskee-utils';
import { Plus, XIcon } from 'lucide-react';
import { useState } from 'react';
import { RefCallBack } from 'react-hook-form';

import { Separator } from '../ui/separator';

interface SelectBaseProps
  extends Omit<SelectPrimitive.SelectProps, 'onValueChange'> {
  onValueChange: (value: string) => void;
  defaultValue?: string;
  options: Array<OptionType>;
  placeholder?: string;
  isAddItem?: boolean;
  addItem?: (item: OptionType) => void;
  newItemPlaceholder?: string;
  backgroundColor?: string;
  allowClear?: boolean;
  selectTriggerRef?: RefCallBack;
}

const SelectBase = ({
  onValueChange,
  defaultValue,
  options,
  placeholder,
  isAddItem,
  addItem,
  newItemPlaceholder,
  allowClear,
  backgroundColor: selectTriggerBtnClassName,
  selectTriggerRef,
  ...props
}: SelectBaseProps) => {
  const [newItem, setNewItem] = useState('');
  const [key, setKey] = useState(createUID());

  const handleAddItem = () => {
    if (newItem.trim() !== '' && addItem) {
      addItem({ value: newItem, label: newItem });
      setNewItem(''); // Reset the input field
    }
  };

  // Issue from radix
  // https://github.com/radix-ui/primitives/pull/2174
  const handleClear = () => {
    onValueChange('');
  };

  return (
    <Select
      onValueChange={onValueChange}
      defaultValue={defaultValue}
      key={key}
      {...props}>
      <SelectTrigger
        ref={selectTriggerRef}
        className={cn(
          'justify-between font-normal flex',
          selectTriggerBtnClassName,
        )}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {allowClear ? (
          <div className="grid gap-1">
            <Button
              className="w-full px-2"
              variant="secondary"
              size="sm"
              onClick={e => {
                e.stopPropagation();
                handleClear();
                setKey(createUID());
              }}>
              <XIcon size={16} />
            </Button>
            <Separator />
          </div>
        ) : null}
        {options.map(option => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
        {isAddItem && addItem && (
          <div className="mt-4 flex px-6 py-2">
            <Input
              type="text"
              value={newItem}
              onChange={e => setNewItem(e.target.value)}
              placeholder={newItemPlaceholder}
              className="border p-2"
            />
            <Button
              onClick={handleAddItem}
              className="ml-2 flex gap-2 p-2 text-white">
              <Plus />
              Add Item
            </Button>
          </div>
        )}
      </SelectContent>
    </Select>
  );
};

export { SelectBase };
