import { Button } from '@/components/ui/button';
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>er,
  Card<PERSON>eader,
  CardTitle,
} from '@/components/ui/card';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { momentTz } from 'btaskee-utils';
import { Calendar, ChevronLeft, ChevronRight } from 'lucide-react';
import type { HTMLAttributes } from 'react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

type DateRange = { from: Date; to: Date };

export interface SingleMonthYearPickerProps {
  mode: 'month-year' | 'month';
  minDate?: Date;
  maxDate?: Date;
  initialValue?: DateRange;
  onChange: (range: DateRange) => void;
  resetSignal?: number;
  className?: HTMLAttributes<HTMLButtonElement>['className'];
}

export default function SingleMonthYearPicker({
  mode,
  minDate = momentTz().year(2000).startOf('year').toDate(),
  maxDate = momentTz().year(2050).endOf('year').toDate(),
  initialValue,
  onChange,
  resetSignal,
  className,
}: SingleMonthYearPickerProps) {
  const { t: tCommon } = useTranslation('common');

  const [currentDate, setCurrentDate] = useState(
    initialValue?.from || momentTz().toDate(),
  );
  const [selectedRange, setSelectedRange] = useState<DateRange | null>(
    initialValue || null,
  );
  const [tempSelectedRange, setTempSelectedRange] = useState<DateRange | null>(
    initialValue || null,
  );
  const [isOpen, setIsOpen] = useState(false);
  const [isYearSelected, setIsYearSelected] = useState(() => {
    if (!initialValue) return false;

    return (
      initialValue.from.getFullYear() === initialValue.to.getFullYear() &&
      momentTz(initialValue.from).isSame(
        momentTz(initialValue.from).startOf('year'),
      ) &&
      (momentTz(initialValue.to).isSame(
        momentTz(initialValue.to).endOf('year'),
      ) ||
        initialValue.to.getTime() === maxDate.getTime())
    );
  });

  const months = useMemo(
    () => [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ],
    [],
  );

  useEffect(() => {
    if (resetSignal && resetSignal > 0) {
      setIsOpen(false);
      setSelectedRange(initialValue || null);
      setTempSelectedRange(initialValue || null);
      setIsYearSelected(false);
    }
  }, [resetSignal]);

  const changeYear = useCallback(
    (offset: number) => {
      const newDate = momentTz(currentDate).add(offset, 'years').toDate();
      setCurrentDate(newDate);

      if (tempSelectedRange) {
        const currentMonth = momentTz(tempSelectedRange.from).month();
        const newFrom = momentTz(newDate)
          .month(currentMonth)
          .startOf('month')
          .toDate();
        const newTo = momentTz(newDate)
          .month(currentMonth)
          .endOf('month')
          .toDate();
        setTempSelectedRange({ from: newFrom, to: newTo });
      }

      setIsYearSelected(false);
    },
    [currentDate, tempSelectedRange],
  );

  const selectMonth = useCallback(
    (month: number) => {
      const newDate = momentTz()
        .year(momentTz(currentDate).year())
        .month(month)
        .toDate();
      if (newDate >= minDate && newDate <= maxDate) {
        const from = momentTz()
          .year(momentTz(currentDate).year())
          .month(month)
          .startOf('month')
          .toDate();
        const to = momentTz()
          .year(momentTz(currentDate).year())
          .month(month)
          .endOf('month')
          .toDate();
        setTempSelectedRange({ from, to });
        setIsYearSelected(false);
      }
    },
    [currentDate, maxDate, minDate],
  );

  const selectYear = useCallback(() => {
    const year = momentTz(currentDate).year();
    const from = momentTz(currentDate).startOf('year').toDate();
    const to = momentTz(currentDate).endOf('year').toDate();
    if (year === momentTz().year()) {
      if (maxDate && momentTz(maxDate).year() === year) {
        to.setTime(maxDate.getTime());
      }
    }
    setTempSelectedRange({ from, to });
    setIsYearSelected(true);
  }, [currentDate, maxDate]);

  const handleApply = useCallback(() => {
    if (tempSelectedRange) {
      setSelectedRange(tempSelectedRange);
      setIsOpen(false);
      onChange(tempSelectedRange);
    }
  }, [tempSelectedRange]);

  const handleCancel = useCallback(() => {
    setTempSelectedRange(selectedRange);
    setIsYearSelected(
      selectedRange?.from.getFullYear() === selectedRange?.to.getFullYear() &&
        selectedRange?.from.getMonth() === 0 &&
        (selectedRange?.to.getMonth() === 11 ||
          selectedRange?.to.getTime() === momentTz().endOf('day').valueOf()),
    );
    setIsOpen(false);
  }, [selectedRange]);

  const isMonthSelected = useCallback(
    (month: number) => {
      if (!tempSelectedRange) return false;
      if (isYearSelected) return true;
      return momentTz(tempSelectedRange.from).month() === month;
    },
    [isYearSelected, tempSelectedRange],
  );

  const isMonthDisabled = useCallback(
    (month: number) => {
      const date = momentTz()
        .year(momentTz(currentDate).year())
        .month(month)
        .toDate();
      return date < minDate || date > maxDate;
    },
    [currentDate, maxDate, minDate],
  );

  const formatDateRange = useCallback(
    (range: DateRange) => {
      if (
        isYearSelected ||
        (momentTz(range.from).year() === momentTz(range.to).year() &&
          momentTz(range.from).month() === 0 &&
          momentTz(range.to).month() === 11)
      ) {
        return `${momentTz(range.from).year()}`;
      }
      return `${momentTz(range.from).format('MMMM YYYY')}`;
    },
    [selectedRange],
  );

  const yearSelectOptions = useMemo(() => {
    return Array.from(
      {
        length: momentTz(maxDate).year() - momentTz(minDate).year() + 1,
      },
      (_, i) => ({
        value: (momentTz(minDate).year() + i).toString(),
        label: momentTz(minDate).year() + i,
      }),
    );
  }, [maxDate, minDate]);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          type="button"
          size="lg"
          variant="outline"
          className={cn('h-8 px-3', className)}>
          <Calendar className="mr-2 h-4 w-4" />
          {selectedRange ? formatDateRange(selectedRange) : 'Select date'}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Card className="border-none shadow-none">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {momentTz(currentDate).format('MMMM YYYY')}
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={() => changeYear(-1)}
                disabled={
                  momentTz(currentDate)
                    .subtract(1, 'year')
                    .endOf('year')
                    .toDate() < minDate
                }>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={() => changeYear(1)}
                disabled={
                  momentTz(currentDate)
                    .add(1, 'year')
                    .startOf('year')
                    .toDate() > maxDate
                }>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-4 gap-2">
              {months.map((month, index) => (
                <Button
                  type="button"
                  key={month}
                  variant={'outline'}
                  onClick={() => selectMonth(index)}
                  disabled={isMonthDisabled(index)}
                  className={cn(
                    'w-full hover:bg-primary hover:text-primary-foreground',
                    isMonthSelected(index)
                      ? 'bg-primary text-primary-foreground'
                      : '',
                  )}>
                  {month}
                </Button>
              ))}
            </div>
            {mode === 'month-year' && (
              <div className="mt-4 flex items-center justify-between">
                <Select
                  value={momentTz(currentDate).year().toString()}
                  onValueChange={value => {
                    const newDate = momentTz(currentDate)
                      .year(parseInt(value))
                      .toDate();
                    setCurrentDate(newDate);

                    if (tempSelectedRange) {
                      const currentMonth = momentTz(
                        tempSelectedRange.from,
                      ).month();
                      const newFrom = momentTz(newDate)
                        .month(currentMonth)
                        .startOf('month')
                        .toDate();
                      const newTo = momentTz(newDate)
                        .month(currentMonth)
                        .endOf('month')
                        .toDate();
                      setTempSelectedRange({ from: newFrom, to: newTo });
                    }

                    setIsYearSelected(false);
                  }}>
                  <SelectTrigger className="w-[120px]">
                    <SelectValue placeholder={tCommon('SELECT_YEAR')} />
                  </SelectTrigger>
                  <SelectContent>
                    {yearSelectOptions.map(({ value, label }) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  type="button"
                  onClick={selectYear}
                  variant={'outline'}
                  className={
                    isYearSelected ? 'bg-primary text-primary-foreground' : ''
                  }>
                  {tCommon('SELECT_YEAR')}
                </Button>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button type="button" variant="outline" onClick={handleCancel}>
              {tCommon('CANCEL')}
            </Button>
            <Button type="button" onClick={handleApply}>
              {tCommon('APPLY')}
            </Button>
          </CardFooter>
        </Card>
      </PopoverContent>
    </Popover>
  );
}
