/**
 * @module CardInformation
 * @description This module exports a CardInformation component and a BlockDescription component for displaying structured information in a card format.
 */

import { Typography } from '@/components/btaskee/Typography';
import { cn } from '@/lib/utils';
import { ReactNode } from 'react';

/**
 * @component BlockDescription
 * @description Renders a block of information with a label, optional value, tags, and custom node.
 * @param {Object} props - The component props
 * @param {Object} props.desc - The description object
 * @param {string} props.desc.label - The label for the description
 * @param {string} [props.desc.value] - The value for the description
 * @param {string[]} [props.desc.tags] - An array of tags
 * @param {ReactNode} [props.desc.customValueNode] - A custom React node to render as value
 * @param {'sm' | 'md'} [props.variant='md'] - The size variant of the component
 * @param {string} [props.className] - Additional CSS classes
 * @returns {JSX.Element} The rendered BlockDescription component
 */
const BlockDescription = ({
  desc,
  variant = 'md',
  className,
}: {
  desc: {
    label: string;
    value?: string;
    tags?: string[];
    customValueNode?: ReactNode;
  };
  variant?: 'sm' | 'md';
  className?: string;
}): JSX.Element => {
  return (
    <div className={cn('flex flex-col gap-1', className)}>
      <Typography
        variant="p"
        affects="removePMargin"
        className={`${variant === 'sm' ? 'text-xs font-medium' : 'text-sm font-normal'} text-gray-400`}>
        {desc.label}
      </Typography>
      {desc.tags ? (
        <div className="flex gap-2 flex-wrap">
          {desc.tags.map(tag => (
            <Typography
              key={tag}
              affects="removePMargin"
              variant="p"
              className="text-sm text-blue-500 bg-blue-50 py-1.5 px-3 inline rounded-md leading-tight">
              {tag}
            </Typography>
          ))}
        </div>
      ) : null}
      {desc.value ? (
        <Typography
          variant="p"
          affects="removePMargin"
          className={`${variant === 'sm' ? 'text-base leading-tight font-medium' : 'text-lg font-semibold'} text-gray-600 break-all`}>
          {desc.value}
        </Typography>
      ) : null}
      {desc.customValueNode ? desc.customValueNode : null}
    </div>
  );
};

/**
 * @component CardInformation
 * @description Renders a grid of BlockDescription components.
 * @param {Object} props - The component props
 * @param {Array<Object>} props.descriptions - An array of description objects
 * @param {string} [props.className] - Additional CSS classes for the container
 * @param {string} [props.classNameBlockDescription] - Additional CSS classes for each BlockDescription
 * @param {'sm' | 'md'} [props.variant='md'] - The size variant of the component
 * @returns {JSX.Element} The rendered CardInformation component
 */
const CardInformation = ({
  descriptions,
  className,
  classNameBlockDescription,
  variant = 'md',
}: {
  descriptions: Array<{
    label: string;
    value?: string;
    tags?: string[];
    customValueNode?: ReactNode;
  }>;
  className?: string;
  variant?: 'sm' | 'md';
  classNameBlockDescription?: string;
}): JSX.Element => (
  <div className={cn('grid grid-cols-3 gap-x-24 gap-y-5', className)}>
    {descriptions?.map(desc => (
      <BlockDescription
        key={desc.label}
        desc={desc}
        variant={variant}
        className={classNameBlockDescription}
      />
    ))}
  </div>
);

export { BlockDescription, CardInformation };
