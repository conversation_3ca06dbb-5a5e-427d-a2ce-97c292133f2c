import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  <PERSON><PERSON><PERSON>Provider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import React from 'react';

interface TooltipBaseProps {
  children: React.ReactNode;
  content: string;
}

const TooltipUIBase: React.FC<TooltipBaseProps> = ({ children, content }) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger>{children}</TooltipTrigger>
      <TooltipContent>{content}</TooltipContent>
    </Tooltip>
  </TooltipProvider>
);

export { TooltipUIBase };
