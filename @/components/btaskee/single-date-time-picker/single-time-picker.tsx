import { Button, ButtonProps } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { format as formatDateFns } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';

import { TimePicker } from './time-picker';

export interface SingleDateTimePickerProps {
  form: UseFormReturn<MustBeAny>;
  name: string;
  label?: string;
  format?: string; //See https://github.com/date-fns/date-fns/blob/main/docs/unicodeTokens.md for the list of Unicode tokens.
  fieldWrapperClassName?: string;
  triggerProps?: ButtonProps;
  triggerClassName?: string;
  disableTimePickers?: Array<'hours' | 'minutes' | 'seconds'>;
}

/**
 * SingleDateTimePicker component
 * 
 * This component renders a form field for selecting both a date and time.
 * It uses a popover to display a calendar for date selection and a time picker for time selection.
 * 
 * @param {Object} props - The component props
 * @param {UseFormReturn<MustBeAny>} props.form - The form object from react-hook-form
 * @param {string} props.name - The name of the form field
 * @param {string} [props.label] - Optional label for the form field
 * @param {string} [props.format='PPP HH:mm:ss'] - Optional date-time format string
 *   The format string should use Unicode tokens as defined in date-fns.
 *   See https://github.com/date-fns/date-fns/blob/main/docs/unicodeTokens.md for the list of Unicode tokens.
 *   Default is 'PPP HH:mm:ss' which represents a long localized date format with time (e.g., "April 29th, 2023 14:30:00")
 * @param {string} [props.fieldWrapperClassName] - Optional class name for the form field wrapper
 * @param {ButtonProps} [props.triggerProps] - Optional props for the trigger button
 * @param {string} [props.triggerClassName] - Optional class name for the trigger button
 * @param {Array<'hours' | 'minutes' | 'seconds'>} [props.disableTimePickers] - Optional array of time picker types to disable
 * 
 * @returns {JSX.Element} A form field component for date and time selection
 */
export function SingleDateTimePicker({
  form,
  name,
  label,
  format = 'PPP HH:mm:ss',
  fieldWrapperClassName,
  triggerProps,
  triggerClassName,
  disableTimePickers,
}: SingleDateTimePickerProps) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn('flex flex-col', fieldWrapperClassName)}>
          <FormLabel className="text-left">{label}</FormLabel>
          <Popover>
            <FormControl>
              <PopoverTrigger asChild>
                <Button
                  {...triggerProps}
                  variant="outline"
                  className={cn(
                    'w-[280px] justify-start text-left font-normal',
                    !field.value && 'text-muted-foreground',
                    triggerClassName,
                  )}>
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {field.value ? (
                    formatDateFns(field.value, format)
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>
            </FormControl>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={field.value}
                onSelect={field.onChange}
                initialFocus
              />
              <div className="p-3 border-t border-border">
                <TimePicker setDate={field.onChange} date={field.value} disablePickers={disableTimePickers} />
              </div>
            </PopoverContent>
            <FormMessage />
          </Popover>
        </FormItem>
      )}
    />
  );
}
