import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { UploadCloud } from 'lucide-react';
import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

export type MediaType = 'image' | 'video';

export interface MediaFile {
  file: string | File;
  preview: string | Blob;
  type: MediaType;
}

interface MediaValue {
  images: MediaFile[];
  videos: MediaFile[];
}

interface MediaUploadProps {
  title?: string;
  maxFiles?: number;
  maxImageSize?: number;
  maxVideoSize?: number;
  value?: MediaValue | null;
  onFileChange?: (files: MediaValue | null) => void;
  accept?: string;
}

export function useMediaUpload({
  maxFiles = 10,
  maxImageSize = 1000,
  maxVideoSize = 10240,
  value,
  onFileChange,
}: MediaUploadProps) {
  const { t } = useTranslation('common');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  const hasMedia = value?.images?.length || value?.videos?.length;

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const validateFile = (file: File): { isValid: boolean; error?: string } => {
    const isImage = file.type.startsWith('image/');
    const isVideo = file.type.startsWith('video/');
    const maxSize = isImage ? maxImageSize : maxVideoSize;

    if (!isImage && !isVideo) {
      return { isValid: false, error: t('INVALID_FILE_TYPE') };
    }

    if (file.size > maxSize * 1024) {
      return {
        isValid: false,
        error: t('FILE_TOO_LARGE', {
          maxSize: isImage ? maxImageSize : maxVideoSize,
        }),
      };
    }

    return { isValid: true };
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = Array.from(event.target.files || []);
    if (!files.length) return;

    const currentImages = value?.images || [];
    const currentVideos = value?.videos || [];
    const totalFiles = currentImages.length + currentVideos.length;

    if (totalFiles + files.length > maxFiles) {
      toast({
        variant: 'error',
        title: t('MAX_FILES_EXCEEDED'),
        description: t('MAX_FILES_EXCEEDED_DESCRIPTION', { maxFiles }),
      });
      return;
    }

    const newImages: MediaFile[] = [];
    const newVideos: MediaFile[] = [];
    const errors: string[] = [];

    for (const file of files) {
      const { isValid, error } = validateFile(file);
      if (!isValid) {
        errors.push(error!);
        continue;
      }

      const isImage = file.type.startsWith('image/');
      const preview = URL.createObjectURL(file);
      const mediaFile = {
        file,
        preview,
        type: isImage ? 'image' : ('video' as const),
      } as MediaFile;

      if (isImage) {
        newImages.push(mediaFile);
      } else {
        newVideos.push(mediaFile);
      }
    }

    if (errors.length) {
      toast({
        variant: 'error',
        title: t('FILE_VALIDATION_ERROR'),
        description: errors.join('\n'),
      });
    }

    if (newImages.length || newVideos.length) {
      onFileChange?.({
        images: [...currentImages, ...newImages],
        videos: [...currentVideos, ...newVideos],
      });

      toast({
        variant: 'success',
        title: t('FILES_UPLOADED'),
        description: t('FILES_UPLOADED_DESCRIPTION', {
          count: newImages.length + newVideos.length,
        }),
      });
    }

    event.target.value = '';
  };

  return {
    fileInputRef,
    isEditMode,
    setIsEditMode,
    hasMedia,
    handleButtonClick,
    handleFileChange,
  };
}

export function MediaUploader({
  title = '',
  maxFiles = 10,
  value,
  accept = 'image/*,video/*',
  ...props
}: MediaUploadProps) {
  const { fileInputRef, handleButtonClick, handleFileChange } = useMediaUpload({
    maxFiles,
    value,
    accept,
    ...props,
  });
  const { t } = useTranslation('common');

  return (
    <div className="flex gap-4 justify-center items-center">
      <input
        type="file"
        accept={accept}
        ref={fileInputRef}
        onChange={handleFileChange}
        className="hidden"
        multiple={maxFiles !== 1}
      />
      <Button
        type="button"
        color="primary"
        className="flex items-center gap-2 rounded-lg"
        variant="outline"
        onClick={handleButtonClick}>
        <UploadCloud className="h-4 w-4" />
        {t('UPLOAD')}
      </Button>
    </div>
  );
}

export function MediaUploaderPreview({
  value,
  maxFiles = 10,
  ...props
}: MediaUploadProps) {
  const { t } = useTranslation('common');
  const hasMedia = value?.images?.length || value?.videos?.length;
  const totalFiles = (value?.images?.length || 0) + (value?.videos?.length || 0);

  const renderMediaPreview = (file: MediaFile, index: number) => {
    const previewUrl = file.preview instanceof Blob 
      ? URL.createObjectURL(file.preview)
      : file.preview;

    if (file.type === 'image') {
      return (
        <img
          src={previewUrl}
          alt={`Preview ${index + 1}`}
          className="h-full w-full rounded-md object-cover"
        />
      );
    }
    return (
      <video
        src={previewUrl}
        className="h-full w-full rounded-md object-cover"
        controls
      />
    );
  };

  const renderGalleryView = () => {
    const allMedia = [...(value?.images || []), ...(value?.videos || [])];

    if (allMedia.length === 0) return null;

    const getGridClassName = (count: number) => {
      switch (count) {
        case 1:
          return 'w-full aspect-[4/3]';
        case 2:
          return 'grid grid-cols-2 gap-2 aspect-[4/3]';
        case 3:
          return 'grid grid-rows-[1fr_1fr] gap-2 aspect-[4/3]';
        case 4:
          return 'grid grid-cols-2 grid-rows-2 gap-2 aspect-[4/3]';
        default:
          return 'grid grid-rows-[1fr_1fr] gap-2 aspect-[4/3]';
      }
    };

    return (
      <div className={getGridClassName(allMedia.length)}>
        {allMedia.length === 3 ? (
          <>
            <div className="w-full">{renderMediaPreview(allMedia[0], 0)}</div>
            <div className="grid grid-cols-2 gap-2">
              {allMedia.slice(1, 3).map((file, index) => (
                <div key={index} className="relative">
                  {renderMediaPreview(file, index + 1)}
                </div>
              ))}
            </div>
          </>
        ) : allMedia.length >= 5 ? (
          <>
            <div className="grid grid-cols-2 gap-2">
              {allMedia.slice(0, 2).map((file, index) => (
                <div key={index} className="relative">
                  {renderMediaPreview(file, index)}
                </div>
              ))}
            </div>
            <div className="grid grid-cols-3 gap-2">
              {allMedia.slice(2, 5).map((file, index) => (
                <div key={index} className="relative">
                  {renderMediaPreview(file, index + 2)}
                  {allMedia.length > 5 && index === 2 && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-md">
                      <span className="text-white text-xl font-semibold">
                        +{allMedia.length - 5}
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </>
        ) : (
          allMedia.slice(0, 4).map((file, index) => (
            <div key={index} className="relative">
              {renderMediaPreview(file, index)}
            </div>
          ))
        )}
      </div>
    );
  };

  if (!hasMedia) return null;

  return (
    <div className="w-full space-y-4 mb-8">
      {renderGalleryView()}
    </div>
  );
}

// Original component that uses both parts
export function MediaUpload(props: MediaUploadProps) {
  return (
    <div className="space-y-4 relative">
      <MediaUploader {...props} />
      <MediaUploaderPreview {...props} />
    </div>
  );
}
