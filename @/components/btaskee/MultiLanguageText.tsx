import { IconENFlag, IconID<PERSON>lag, IconKOFlag, IconTH<PERSON>lag, IconVNFlag } from "@/components/svg/flags";

type LanguageTextDisplayLayout = 'row' | 'column' | 'grid';


/**
 * LanguageTextDisplay Component
 * 
 * NOTE: Wrapper of this component should not set fixed width and height
 * 
 * Renders text in multiple languages with corresponding country flags.
 * 
 * @component
 * @param {Object} props - The component props
 * @param {Record<string, string>} props.text - An object containing text for different languages.
 *                                              Keys are language codes, values are the translated text.
 * @param {('row'|'column'|'grid')} [props.layout='grid'] - The layout style for displaying languages.
 * 
 * @returns {JSX.Element} A React component displaying multilingual text with flags
 * @example
 * <LanguageTextDisplay 
 *   text={{
 *     vi: "",
 *     ko: "",
 *     th: "",
 *     id: "",
 *     en: ""
 *   }}
 *   layout="row"
 * />
 */
export const MultiLanguageText = ({
    text,
    layout,
  }: {
    text: Record<string, string>;
    layout?: LanguageTextDisplayLayout;
  }): JSX.Element => {
    const languageFlags = [
      { lang: 'vi', Icon: IconVNFlag },
      { lang: 'ko', Icon: IconKOFlag },
      { lang: 'th', Icon: IconTHFlag },
      { lang: 'id', Icon: IconIDFlag },
      { lang: 'en', Icon: IconENFlag },
    ];
  
    const renderLanguageItem = ({
      lang,
      Icon,
    }: {
      lang: string;
      Icon: React.ComponentType<{ props: { className: string } }>;
    }) => (
      <div key={lang} className="flex items-center gap-1 px-2 py-1 rounded">
        <Icon props={{ className: 'min-h-4 min-w-4' }} />
        <span className="text-sm break-all">{text?.[lang]}</span>
      </div>
    );
  
    const gridLayout = (
      <div className="flex flex-col gap-2">
        <div className="flex items-center gap-1">
          {renderLanguageItem(languageFlags[0])}
          {renderLanguageItem(languageFlags[1])}
        </div>
        <div className="flex items-center gap-1">
          {renderLanguageItem(languageFlags[2])}
          {renderLanguageItem(languageFlags[3])}
        </div>
        {renderLanguageItem(languageFlags[4])}
      </div>
    );
  
    const rowLayout = (
      <div className="flex items-center gap-2 flex-wrap">
        {languageFlags.map(renderLanguageItem)}
      </div>
    );
  
    const columnLayout = (
      <div className="flex flex-col gap-2">
        {languageFlags.map(renderLanguageItem)}
      </div>
    );
  
    switch (layout) {
      case 'row':
        return rowLayout;
      case 'column':
        return columnLayout;
      case 'grid':
      default:
        return gridLayout;
    }
  };
  