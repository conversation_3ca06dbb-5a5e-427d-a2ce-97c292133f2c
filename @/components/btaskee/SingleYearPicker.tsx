import { Button, buttonVariants } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { momentTz } from 'btaskee-utils';
import {
  CalendarIcon,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import * as React from 'react';
import { useTranslation } from 'react-i18next';

type ButtonVariant =
  | 'default'
  | 'outline'
  | 'ghost'
  | 'link'
  | 'destructive'
  | 'secondary'
  | null
  | undefined;

type SingleYearCalProps = {
  selectedYear?: Date;
  onYearSelect?: (date: Date) => void;
  variant?: {
    calendar?: {
      main?: ButtonVariant;
      selected?: ButtonVariant;
    };
    chevrons?: ButtonVariant;
  };
  minDate?: Date;
  maxDate?: Date;
};

function SingleYearPicker({
  onYearSelect,
  variant,
  minDate,
  maxDate,
  defaultYear,
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement> &
  Omit<SingleYearCalProps, 'selectedYear'> & {
    defaultYear?: Date;
  }) {
  const [selectedYear, setSelectedYear] = React.useState<Date | undefined>(
    defaultYear,
  );
  const { t } = useTranslation('common');

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'max-h-8 justify-start px-6 text-left font-normal',
            !selectedYear ? 'text-muted-foreground' : '',
          )}>
          <CalendarIcon className="mr-2 h-4 w-4" />
          {selectedYear ? (
            momentTz(selectedYear).format('YYYY')
          ) : (
            <span>{t('PICK_YEAR')}</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px]">
        <div className={cn('p-3', className)} {...props}>
          <SingleYearCal
            selectedYear={selectedYear as MustBeAny}
            onYearSelect={value => {
              setSelectedYear(value);
              return onYearSelect?.(value);
            }}
            variant={variant as MustBeAny}
            minDate={minDate as MustBeAny}
            maxDate={maxDate as MustBeAny}
          />
        </div>
      </PopoverContent>
    </Popover>
  );
}

function SingleYearCal({
  selectedYear,
  onYearSelect,
  variant,
  minDate,
  maxDate,
}: SingleYearCalProps) {
  const [startYear, setStartYear] = React.useState<number>(
    selectedYear ? momentTz(selectedYear).year() - 10 : momentTz().year() - 10,
  );

  const years = React.useMemo(() => {
    const yearsArray = [];
    for (let i = 0; i < 20; i++) {
      yearsArray.push(startYear + i);
    }
    return yearsArray;
  }, [startYear]);

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <Button
          variant={variant?.chevrons ?? 'outline'}
          size="icon"
          onClick={() => setStartYear(prev => prev - 20)}>
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <span className="font-medium">
          {years[0]} - {years[years.length - 1]}
        </span>
        <Button
          variant={variant?.chevrons ?? 'outline'}
          size="icon"
          onClick={() => setStartYear(prev => prev + 20)}>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
      <div className="grid grid-cols-4 gap-2">
        {years.map(year => {
          const isSelected =
            selectedYear && momentTz(selectedYear).year() === year;

          return (
            <Button
              key={year}
              variant={
                isSelected
                  ? (variant?.calendar?.selected ?? 'default')
                  : (variant?.calendar?.main ?? 'outline')
              }
              onClick={() => {
                const selectedDate = momentTz({ year })
                  .startOf('year')
                  .toDate();
                onYearSelect?.(selectedDate);
              }}
              disabled={
                (maxDate && year > momentTz(maxDate).year()) ||
                (minDate && year < momentTz(minDate).year())
              }>
              {year}
            </Button>
          );
        })}
      </div>
    </div>
  );
}

SingleYearPicker.displayName = 'SingleYearPicker';

export { SingleYearPicker };
