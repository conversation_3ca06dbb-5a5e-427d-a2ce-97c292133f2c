import { ClientOnly } from '@/components/btaskee/rich-text-editor/client-only';
import { ContentProseStyleClassNames } from '@/components/btaskee/rich-text-editor/read-markdown-only';
import { cn } from '@/lib/utils';
import type { MDXEditorMethods } from '@mdxeditor/editor';
import {
  BlockTypeSelect,
  BoldItalicUnderlineToggles,
  CodeToggle,
  CreateLink,
  InsertThematicBreak,
  ListsToggle,
  MDXEditor,
  StrikeThroughSupSubToggles,
  UndoRedo,
  headingsPlugin,
  linkDialogPlugin,
  linkPlugin,
  listsPlugin,
  quotePlugin,
  thematicBreakPlugin,
  toolbarPlugin,
} from '@mdxeditor/editor';
import { useCallback, useEffect, useRef } from 'react';
import type { RefCallBack } from 'react-hook-form';

interface RichTextComponentProps {
  markdown: string;
  placeholder?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  formFieldRefCallback?: RefCallBack;
  readOnly?: boolean;
}

/* Implement [&[data-lexical-editor="true"]]
 * because when the editor is empty,
 * there are 2 div with 'contentEditableClassName' that don't have the same height,
 * so the border we added will become 2 border
 */
const LexicalEditableClassNames =
  '[&[data-lexical-editor="true"]]:border [&[data-lexical-editor="true"]]:border-t-0 [&[data-lexical-editor="true"]]:border-gray-200 [&[data-lexical-editor="true"]]:rounded-bl-md [&[data-lexical-editor="true"]]:-mt-2 [&[data-lexical-editor="true"]]:rounded-br-md';

const RichTextComponent = ({
  markdown,
  onChange,
  onBlur,
  placeholder = 'Write your wonderful content here...',
  formFieldRefCallback,
  readOnly = false,
}: {
  markdown: string;
  placeholder?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  formFieldRefCallback?: RefCallBack;
  readOnly?: boolean;
}) => {
  const editorRef = useRef<MDXEditorMethods>(null);

  //By default, even when readOnly, the editor still add new line as <p> tag,
  //So we need to set the markdown value when the component is mounted
  useEffect(() => {
    if (readOnly && editorRef.current) {
      editorRef.current.setMarkdown(markdown);
    }
  }, [readOnly, markdown]);

  const toolbarContents = useCallback(
    () => (
      <div className="flex items-center gap-2">
        <UndoRedo />
        <BlockTypeSelect />
        <div className="flex items-center">
          <BoldItalicUnderlineToggles options={['Bold', 'Italic']} />
          <span className="-ml-1.5">
            <StrikeThroughSupSubToggles options={['Strikethrough']} />
          </span>
        </div>
        <div className="h-[24px] w-[1px] border-r border-gray-400"></div>
        <ListsToggle options={['bullet', 'number']} />
        <div className="h-[24px] w-[1px] border-r border-gray-400"></div>
        <div className="flex items-center">
          <CodeToggle />
          <InsertThematicBreak />
          <CreateLink />
        </div>
      </div>
    ),
    [],
  );

  return (
    <>
      {readOnly && !markdown ? (
        <></>
      ) : (
        <ClientOnly fallback={<div>Loading...</div>}>
          {() => (
            <div ref={formFieldRefCallback} className="w-full">
              <MDXEditor
                readOnly={readOnly}
                suppressHtmlProcessing
                contentEditableClassName={cn(
                  'max-w-[unset]',
                  ContentProseStyleClassNames,
                  !readOnly
                    ? [
                        LexicalEditableClassNames,
                        'min-h-[200px]',
                        'pt-6 px-4 pb-4',
                      ]
                    : 'p-0',
                )}
                ref={editorRef}
                markdown={readOnly ? '' : markdown}
                onChange={onChange}
                onBlur={onBlur}
                plugins={[
                  ...(readOnly ? [] : [toolbarPlugin({ toolbarContents })]),
                  headingsPlugin({ allowedHeadingLevels: [1, 2, 3, 4] }),
                  ...(readOnly ? [] : [linkDialogPlugin()]),
                  linkPlugin(),
                  listsPlugin(),
                  quotePlugin(),
                  thematicBreakPlugin(),
                ]}
                placeholder={placeholder}
              />
            </div>
          )}
        </ClientOnly>
      )}
    </>
  );
};

export { RichTextComponent };
