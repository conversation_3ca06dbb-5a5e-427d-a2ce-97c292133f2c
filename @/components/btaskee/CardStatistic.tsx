import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';

type CardStatisticProps = {
  title: string;
  icon?: React.ReactNode;
  content: string;
  subContent?: string;
};

const CardStatistic = ({
  title,
  icon,
  content,
  subContent,
}: CardStatisticProps) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{content}</div>
        <p className="text-xs text-muted-foreground">{subContent}</p>
      </CardContent>
    </Card>
  );
};

export { CardStatistic };
