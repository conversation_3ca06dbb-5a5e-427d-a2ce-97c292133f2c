import { DateInput } from '@/components/btaskee/DateInput';
import { Button } from '@/components/ui/button';
import { Calendar, CalendarProps } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { CalendarIcon, CheckIcon } from '@radix-ui/react-icons';
import { DATE_RANGE_OPTIONS } from 'btaskee-constants';
import { getRangeByPresetOptions, momentTz } from 'btaskee-utils';
import { type FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { HourRangePicker } from './HourRangePicker';

export type DateRangePickerOptions =
  | keyof typeof DATE_RANGE_OPTIONS
  | undefined;

export type DateRangeProps =
  | {
      from: Date;
      to: Date;
    }
  | undefined;

export interface DateRangePickerProps {
  onUpdate?: (value: DateRangeProps) => void;
  initialRangeDate?: DateRangeProps;
  align?: 'start' | 'center' | 'end';
  className?: string;
  formatDateTriggerButtonText?: string;
  resetSignal?: number;
  calendarProps?: Omit<CalendarProps, 'mode' | 'selected' | 'onSelect' | 'numberOfMonths' | 'defaultMonth'>;
  showHourRangePicker?: boolean;
  onHourRangeUpdate?: (timeRange: { from: Date; to: Date }) => void;
  initialHourRange?: { from: Date; to: Date };
}

export const DateRangePickerV2: FC<DateRangePickerProps> = ({
  onUpdate,
  align = 'end',
  initialRangeDate,
  className,
  formatDateTriggerButtonText = 'DD/MM/YYYY',
  resetSignal,
  calendarProps,
  showHourRangePicker = false,
  onHourRangeUpdate,
  initialHourRange,
}): JSX.Element => {
  const { t } = useTranslation('common');

  const getPresetByRange = useCallback(
    (dateRange: DateRangeProps): DATE_RANGE_OPTIONS | undefined => {
      if (!dateRange) return undefined;

      const { from, to } = dateRange;
      const now = momentTz();
      const ranges = {
        [DATE_RANGE_OPTIONS.TODAY]: {
          from: now.clone().startOf('day').toDate(),
          to: now.clone().endOf('day').toDate(),
        },
        [DATE_RANGE_OPTIONS.YESTERDAY]: {
          from: now.clone().subtract(1, 'day').startOf('day').toDate(),
          to: now.clone().subtract(1, 'day').endOf('day').toDate(),
        },
        [DATE_RANGE_OPTIONS.LAST_7_DAYS]: {
          from: now.clone().subtract(6, 'days').startOf('day').toDate(),
          to: now.clone().endOf('day').toDate(),
        },
        [DATE_RANGE_OPTIONS.LAST_14_DAYS]: {
          from: now.clone().subtract(13, 'days').startOf('day').toDate(),
          to: now.clone().endOf('day').toDate(),
        },
        [DATE_RANGE_OPTIONS.LAST_30_DAYS]: {
          from: now.clone().subtract(29, 'days').startOf('day').toDate(),
          to: now.clone().endOf('day').toDate(),
        },
        [DATE_RANGE_OPTIONS.THIS_MONTH]: {
          from: now.clone().startOf('month').toDate(),
          to: now.clone().endOf('month').toDate(),
        },
        [DATE_RANGE_OPTIONS.LAST_MONTH]: {
          from: now.clone().subtract(1, 'month').startOf('month').toDate(),
          to: now.clone().subtract(1, 'month').endOf('month').toDate(),
        },
        [DATE_RANGE_OPTIONS.THIS_YEAR]: {
          from: now.clone().startOf('year').toDate(),
          to: now.clone().endOf('year').toDate(),
        },
        [DATE_RANGE_OPTIONS.LAST_YEAR]: {
          from: now.clone().subtract(1, 'year').startOf('year').toDate(),
          to: now.clone().subtract(1, 'year').endOf('year').toDate(),
        },
      };

      for (const [preset, range] of Object.entries(ranges)) {
        if (
          from.getTime() === range.from.getTime() &&
          to.getTime() === range.to.getTime()
        ) {
          return preset as DATE_RANGE_OPTIONS;
        }
      }

      return undefined;
    },
    [],
  );

  const initialDateRangeMemo = useMemo(() => {
    return initialRangeDate
      ? {
          from: momentTz(initialRangeDate.from).startOf('day').toDate(),
          to: initialRangeDate.to
            ? momentTz(initialRangeDate.to).endOf('day').toDate()
            : momentTz(initialRangeDate.from).endOf('day').toDate(),
        }
      : undefined;
  }, [initialRangeDate]);

  const [isOpen, setIsOpen] = useState(false);
  const [range, setRange] = useState<DateRangeProps | undefined>(
    initialDateRangeMemo,
  );
  const [tempRange, setTempRange] = useState<DateRangeProps | undefined>(
    initialDateRangeMemo,
  );
  const [selectedPreset, setSelectedPreset] = useState<
    DateRangePickerOptions | undefined
  >(getPresetByRange(initialRangeDate));
  const [hourRange, setHourRange] = useState<{ from: Date; to: Date } | undefined>(
    initialHourRange,
  );

  const PresetButton = useCallback(
    ({
      isSelected,
      preset,
    }: {
      isSelected: boolean;
      preset: DateRangePickerOptions;
    }): JSX.Element => (
      <Button
        className={isSelected ? 'pointer-events-none' : ''}
        variant="ghost"
        onClick={() => {
          const range = getRangeByPresetOptions(preset);

          if (range) {
            setTempRange(range);
            setSelectedPreset(preset);
          }
        }}>
        <>
          <span className={cn('pr-2 opacity-0', isSelected && 'opacity-70')}>
            <CheckIcon width={18} height={18} />
          </span>
          {t(preset as DATE_RANGE_OPTIONS)}
        </>
      </Button>
    ),
    [t],
  );

  const onCancel = useCallback(() => {
    setIsOpen(false);
    setTempRange(range);
    setSelectedPreset(getPresetByRange(range));
  }, [range, getPresetByRange]);

  const onApply = useCallback(() => {
    setIsOpen(false);
    setRange(tempRange);
    setSelectedPreset(getPresetByRange(tempRange));
    onUpdate?.(tempRange);
    if (showHourRangePicker && hourRange) {
      onHourRangeUpdate?.(hourRange);
    }
  }, [tempRange, onUpdate, getPresetByRange, showHourRangePicker, hourRange, onHourRangeUpdate]);

  useEffect(() => {
    if (resetSignal && resetSignal > 0) {
      const preset = getPresetByRange(initialDateRangeMemo);
      setSelectedPreset(preset);
      setRange(initialDateRangeMemo);
      setTempRange(initialDateRangeMemo);
      if (showHourRangePicker) {
        setHourRange(initialHourRange);
      }
    }
  }, [resetSignal, initialDateRangeMemo, getPresetByRange, showHourRangePicker, initialHourRange]);

  return (
    <Popover modal={true} open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          className={cn('h-8 px-3', className)}
          size="lg"
          variant="outline">
          <div
            className={`flex items-center gap-4 font-normal ${range ? 'text-foreground' : 'text-muted-foreground'}`}>
            <CalendarIcon />
            <span className="py-1">
              {range
                ? `${momentTz(range.from).format(formatDateTriggerButtonText)}${range.to ? ' - ' + momentTz(range.to).format(formatDateTriggerButtonText) : ''}`
                : t('SELECT_DATE_RANGE_ON_BTASKEE_TABLE')}
            </span>
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        align={align}
        className="w-auto"
        onInteractOutside={onCancel}>
        <div className="flex items-stretch">
          <div className="flex flex-col gap-2">
            <div className="flex justify-center gap-2">
              <DateInput
                value={tempRange?.from}
                onChange={date => {
                  const toDate =
                    !tempRange?.to || momentTz(date).isAfter(tempRange?.to)
                      ? momentTz(date).endOf('day').toDate()
                      : momentTz(tempRange?.to).endOf('day').toDate();

                  setTempRange(prevRange => ({
                    ...prevRange,
                    from: momentTz(date).startOf('day').toDate(),
                    to: toDate,
                  }));
                }}
              />
              <span className="py-1">-</span>
              <DateInput
                value={tempRange?.to}
                onChange={date => {
                  const fromDate = momentTz(date).isBefore(tempRange?.from)
                    ? momentTz(date).startOf('day').toDate()
                    : momentTz(tempRange?.from).startOf('day').toDate();
                  setTempRange(prevRange => ({
                    ...prevRange,
                    from: fromDate,
                    to: momentTz(date).endOf('day').toDate(),
                  }));
                }}
              />
            </div>

            <Calendar
              mode="range"
              selected={tempRange}
              onSelect={(value: { from?: Date; to?: Date } | undefined) => {
                setSelectedPreset(undefined);
                setTempRange(
                  value
                    ? {
                        from: momentTz(value.from).startOf('day').toDate(),
                        to: value.to
                          ? momentTz(value.to).endOf('day').toDate()
                          : momentTz(value.from).endOf('day').toDate(),
                      }
                    : undefined,
                );
              }}
              numberOfMonths={2}
              defaultMonth={momentTz()
                .subtract(1, 'month')
                .startOf('month')
                .toDate()}
              {...calendarProps}
            />
          </div>

          <div className="flex w-full flex-col items-start">
            {Object.values(DATE_RANGE_OPTIONS).map(preset => (
              <PresetButton
                isSelected={selectedPreset === preset}
                key={preset}
                preset={preset}
              />
            ))}
          </div>
        </div>

        {showHourRangePicker && (
          <div className="mt-4 border-t pt-4">
            <div className="mb-2">
              <span className="text-sm font-medium">{t('SELECT_TIME_RANGE')}</span>
            </div>
            <HourRangePicker
              initialValue={hourRange}
              onValueChange={setHourRange}
              startTimePlaceholder={t('START_TIME')}
              endTimePlaceholder={t('END_TIME')}
            />
          </div>
        )}

        <div className="mt-2 flex justify-end gap-2">
          <Button onClick={onCancel} variant="ghost">
            {t('CANCEL')}
          </Button>
          <Button onClick={onApply}>{t('APPLY')}</Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};

DateRangePickerV2.displayName = 'DateRangePickerV2';
