import { Input } from '@/components/ui/input';
import { type InputHTMLAttributes, useEffect, useState } from 'react';

const DebouncedInput = ({
  value: initialValue,
  onChange,
  debounce = 500,
  ...props
}: {
  value?: InputHTMLAttributes<HTMLInputElement>['value'];
  onChange: (value: InputHTMLAttributes<HTMLInputElement>['value']) => void;
  debounce?: number;
} & Omit<InputHTMLAttributes<HTMLInputElement>, 'onChange'>) => {
  const [value, setValue] = useState(props.defaultValue);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (isMounted) {
      const timeout = setTimeout(() => onChange(value), debounce);
      return () => clearTimeout(timeout);
    }
  }, [value]);

  return (
    <Input
      {...props}
      value={value}
      onChange={e => setValue(e.target.value)}
    />
  );
};

DebouncedInput.displayName = 'DebouncedInput';

export { DebouncedInput };
