import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import React from 'react';

export interface AlertBaseProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  variant?: 'default' | 'destructive';
}

export function AlertBase({
  icon,
  title,
  description,
  variant = 'default',
}: AlertBaseProps) {
  return (
    <Alert variant={variant}>
      {icon}
      <AlertTitle>{title}</AlertTitle>
      <AlertDescription>{description}</AlertDescription>
    </Alert>
  );
}
