import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useEffect, useReducer } from 'react';
import { RegisterOptions, UseFormReturn } from 'react-hook-form';

type TextInputProps = {
  form: UseFormReturn<any>;
  name: string;
  label: string;
  placeholder: string;
  currency: string;
  rules?: RegisterOptions;
  disabled?: boolean;
  backgroundColor?: string;
};

function numberWithCommas(value: number) {
  return value.toLocaleString();
}

export function MoneyInput(props: TextInputProps) {
  const [value, setValue] = useReducer((_: any, next: string | number) => {
    const stringValue = next.toString();
    const digits = stringValue.replace(/\D/g, '');
    return numberWithCommas(Number(digits));
  }, '');

  useEffect(() => {
    const formValue = props.form.getValues(props.name);
    if (formValue !== undefined && formValue !== null) {
      setValue(formValue);
    }
  }, [props.form.getValues(props.name)]);

  function handleChange(realChangeFn: Function, formattedValue: string) {
    const digits = formattedValue.replace(/\D/g, '');
    const realValue = Number(digits);
    realChangeFn(realValue);
  }

  return (
    <FormField
      control={props.form.control}
      name={props.name}
      rules={props.rules}
      render={({ field }) => {
        field.value = value;
        const _change = field.onChange;

        return (
          <FormItem>
            <FormLabel>{props.label}</FormLabel>
            <FormControl>
              <Input
                backgroundColor={props.backgroundColor}
                placeholder={props.placeholder}
                type="text"
                {...field}
                disabled={props.disabled}
                onChange={ev => {
                  setValue(ev.target.value);
                  handleChange(_change, ev.target.value);
                }}
                value={value}
                endAdornment={<span>{props.currency}</span>}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
