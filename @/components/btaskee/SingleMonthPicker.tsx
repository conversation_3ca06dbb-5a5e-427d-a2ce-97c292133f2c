import { Button, buttonVariants } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { momentTz } from 'btaskee-utils';
import {
  CalendarIcon,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import * as React from 'react';
import { useTranslation } from 'react-i18next';

function SingleMonthCal({
  selectedMonth,
  onMonthSelect,
  callbacks,
  variant,
  minDate,
  maxDate,
}: SingleMonthCalProps) {
  const [menuYear, setMenuYear] = React.useState<number>(
    selectedMonth ? momentTz(selectedMonth).year() : momentTz().year(),
  );

  if (minDate && maxDate && minDate > maxDate) minDate = maxDate;

  return (
    <div className="min-w-[400px] space-y-4">
      <div className="relative flex items-center justify-evenly pt-1">
        <div className="text-sm font-medium">
          {callbacks?.yearLabel ? callbacks?.yearLabel(menuYear) : menuYear}
        </div>
        <div className="flex items-center space-x-1">
          <button
            onClick={() => {
              setMenuYear(menuYear - 1);
            }}
            className={cn(
              buttonVariants({ variant: variant?.chevrons ?? 'outline' }),
              'absolute left-1 inline-flex h-7 w-7 items-center justify-center p-0',
            )}>
            <ChevronLeft className="h-4 w-4 opacity-50" />
          </button>
          <button
            onClick={() => {
              setMenuYear(menuYear + 1);
            }}
            className={cn(
              buttonVariants({ variant: variant?.chevrons ?? 'outline' }),
              'absolute right-1 inline-flex h-7 w-7 items-center justify-center p-0',
            )}>
            <ChevronRight className="h-4 w-4 opacity-50" />
          </button>
        </div>
        <div className="text-sm font-medium">
          {callbacks?.yearLabel
            ? callbacks?.yearLabel(menuYear + 1)
            : menuYear + 1}
        </div>
      </div>
      <table className="w-full border-collapse space-y-1">
        <tbody>
          {MONTHS.map((monthRow, a) => {
            return (
              <tr key={'row-' + a} className="mt-2 flex w-full">
                {monthRow.map((m, i) => {
                  const currentYear = menuYear + m.yearOffset;
                  const isSelected =
                    selectedMonth &&
                    momentTz(selectedMonth).year() === currentYear &&
                    momentTz(selectedMonth).month() === m.number;

                  return (
                    <td
                      key={m.number + '-' + m.yearOffset}
                      className={cn(
                        'relative h-10 w-1/4 p-0 text-center text-sm focus-within:relative focus-within:z-20',
                        i == 3 ? 'mr-2' : i == 4 ? 'ml-2' : '',
                      )}>
                      <button
                        onClick={() => {
                          const selectedDate = momentTz({
                            year: currentYear,
                            month: m.number,
                          }).toDate();
                          onMonthSelect?.(selectedDate);
                        }}
                        disabled={
                          (maxDate
                            ? currentYear > momentTz(maxDate).year() ||
                              (currentYear === momentTz(maxDate).year() &&
                                m.number > momentTz(maxDate).month())
                            : false) ||
                          (minDate
                            ? currentYear < momentTz(minDate).year() ||
                              (currentYear === momentTz(minDate).year() &&
                                m.number < momentTz(minDate).month())
                            : false)
                        }
                        className={cn(
                          buttonVariants({
                            variant: isSelected
                              ? (variant?.calendar?.selected ?? 'default')
                              : (variant?.calendar?.main ?? 'ghost'),
                          }),
                          'h-full w-full p-0 font-normal aria-selected:opacity-100',
                        )}>
                        {callbacks?.monthLabel
                          ? callbacks.monthLabel(m)
                          : m.name}
                      </button>
                    </td>
                  );
                })}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}

type Month = {
  number: number;
  name: string;
  yearOffset: number;
};

const MONTHS: Month[][] = [
  [
    { number: 0, name: 'Jan', yearOffset: 0 },
    { number: 1, name: 'Feb', yearOffset: 0 },
    { number: 2, name: 'Mar', yearOffset: 0 },
    { number: 3, name: 'Apr', yearOffset: 0 },
    { number: 0, name: 'Jan', yearOffset: 1 },
    { number: 1, name: 'Feb', yearOffset: 1 },
    { number: 2, name: 'Mar', yearOffset: 1 },
    { number: 3, name: 'Apr', yearOffset: 1 },
  ],
  [
    { number: 4, name: 'May', yearOffset: 0 },
    { number: 5, name: 'Jun', yearOffset: 0 },
    { number: 6, name: 'Jul', yearOffset: 0 },
    { number: 7, name: 'Aug', yearOffset: 0 },
    { number: 4, name: 'May', yearOffset: 1 },
    { number: 5, name: 'Jun', yearOffset: 1 },
    { number: 6, name: 'Jul', yearOffset: 1 },
    { number: 7, name: 'Aug', yearOffset: 1 },
  ],
  [
    { number: 8, name: 'Sep', yearOffset: 0 },
    { number: 9, name: 'Oct', yearOffset: 0 },
    { number: 10, name: 'Nov', yearOffset: 0 },
    { number: 11, name: 'Dec', yearOffset: 0 },
    { number: 8, name: 'Sep', yearOffset: 1 },
    { number: 9, name: 'Oct', yearOffset: 1 },
    { number: 10, name: 'Nov', yearOffset: 1 },
    { number: 11, name: 'Dec', yearOffset: 1 },
  ],
];

type SingleMonthCalProps = {
  selectedMonth?: Date;
  onMonthSelect?: (date: Date) => void;
  callbacks?: {
    yearLabel?: (year: number) => string;
    monthLabel?: (month: Month) => string;
  };
  variant?: {
    calendar?: {
      main?: ButtonVariant;
      selected?: ButtonVariant;
    };
    chevrons?: ButtonVariant;
  };
  minDate?: Date;
  maxDate?: Date;
};

type ButtonVariant =
  | 'default'
  | 'outline'
  | 'ghost'
  | 'link'
  | 'destructive'
  | 'secondary'
  | null
  | undefined;

function SingleMonthPicker({
  onMonthSelect,
  callbacks,
  variant,
  minDate,
  maxDate,
  defaultMonth,
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement> &
  Omit<SingleMonthCalProps, 'selectedMonth'> & {
    defaultMonth?: Date;
  }) {
  const [selectedMonth, setSelectedMonth] =
    React.useState<NonNullable<SingleMonthCalProps['selectedMonth']>>(defaultMonth as Date);
  const { t } = useTranslation('common');

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'max-h-8 justify-start px-6 text-left font-normal',
            !selectedMonth ? 'text-muted-foreground' : '',
          )}>
          <CalendarIcon className="mr-2 h-4 w-4" />
          {selectedMonth ? (
            momentTz(selectedMonth).format('MMM YYYY')
          ) : (
            <span>{t('PICK_MONTH')}</span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full">
        <div className={cn('p-3', className)} {...props}>
          <div className="flex flex-col space-y-4">
            <div className="w-full">
              <SingleMonthCal
                onMonthSelect={value => {
                  setSelectedMonth(value);
                  return onMonthSelect?.(value);
                }}
                callbacks={callbacks as MustBeAny}
                selectedMonth={selectedMonth}
                variant={variant as MustBeAny}
                minDate={minDate as MustBeAny}
                maxDate={maxDate as MustBeAny}
              />
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

SingleMonthPicker.displayName = 'SingleMonthPicker';

export { SingleMonthPicker };
