import { ErrorResponse } from '@remix-run/react';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { Error403, Error404, Error500 } from '../svg/ResponseError';
import { Grid } from './Grid';
import { Typography } from './Typography';

interface BtaskeeResponseErrorProps {
  t: ReturnType<typeof useTranslation>['t'];
  errorStatus: ErrorResponse['status'];
}

/**
 * Renders an error message with an appropriate image based on the HTTP error status.
 *
 * @param {Object} props - The component props.
 * @param {Function} props.t - Translation function from i18next.
 * @param {number} props.errorStatus - HTTP error status code.
 *
 * @returns {JSX.Element} The rendered error component.
 *
 * @description
 * This component handles the following error codes:
 * - 403: Access Denied
 *   - title: 'ACCESS_DENIED'
 *   - description: 'NOT_PERMISSION'
 * - 404: Page Not Found
 *   - title: 'PAGE_NOT_FOUND'
 *   - description: 'PAGE_NOT_EXIST'
 * - 500 (default for unhandled errors): Server Error
 *   - title: 'SERVER_ERROR'
 *   - description: 'TRY_AGAIN_LATER'
 *
 * The component will display the appropriate error image and message based on the errorStatus.
 * If an unhandled error status is provided, it will default to the 500 error message.
 */
export function BtaskeeResponseError({
  t,
  errorStatus,
}: BtaskeeResponseErrorProps) {
  const ImageByStatus = useCallback(() => {
    switch (errorStatus) {
      case 403:
        return <Error403 />;
      case 404:
        return <Error404 />;
      default:
        return <Error500 />;
    }
  }, [errorStatus]);

  const errorMessages = {
    403: { title: 'ACCESS_DENIED', description: 'NOT_PERMISSION' },
    404: { title: 'PAGE_NOT_FOUND', description: 'PAGE_NOT_EXIST' },
    500: { title: 'SERVER_ERROR', description: 'TRY_AGAIN_LATER' },
  };

  const { title, description } =
    errorMessages[errorStatus as keyof typeof errorMessages] ||
    errorMessages[500];

  return (
    <Grid className="text-center">
      <div className="lg:text-7xl text-4xl w-2/3 2xl:w-full text-center m-auto">
        <ImageByStatus />
      </div>
      <Typography className="mt-3" variant="h3">
        {t(title)}
      </Typography>
      <Typography variant="p" className="text-gray">
        {t(description)}
      </Typography>
    </Grid>
  );
}
