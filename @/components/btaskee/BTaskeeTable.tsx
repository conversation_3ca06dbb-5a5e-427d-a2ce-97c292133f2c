import { DateRangeProps } from '@/components/btaskee/DateRangePicker';
import TableFilterOnColumn from '@/components/btaskee/TableFilterOnColumn';
import { DataTablePagination } from '@/components/btaskee/table-data/data-table-pagination';
import { DataTableToolbarV2 } from '@/components/btaskee/table-data/data-table-toolbar-v2';
import { VectorEmptyDataTable } from '@/components/svg/empty-data-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { useSearchParams } from '@remix-run/react';
import type {
  Cell,
  Column,
  ColumnDef,
  ColumnFiltersState,
  ColumnPinningState,
  HeaderGroup,
  PaginationState,
  Row,
  RowSelectionState,
  SortingState,
  VisibilityState,
} from '@tanstack/react-table';
import {
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { RowData } from '@tanstack/table-core';
import { FlatNamespace } from 'i18next';
import type {
  CSSProperties,
  HTMLAttributes,
  InputHTMLAttributes,
  ReactElement,
} from 'react';
import { Fragment, useCallback, useEffect, useState } from 'react';
import { URLSearchParamsInit } from 'react-router-dom';
import { AlertBase, type AlertBaseProps } from './AlertBase';

type THideElements =
  | 'view-options'
  | 'search'
  | 'pagination'
  | 'toolbar-filter'; // If your UI need to hide some elements, update this instead of create new props
type TSearchInputTriggerMode =
  | 'onEnter'
  | 'onChange'
  | 'onChangeDebounce' //Default
  | 'onChangeThrottle'
  | 'custom' // Custom is onChange but handle searchParams outside of table
  | undefined;

export interface DataTablePropsV2<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  total: number;
  pagination: PaginationState;
  overrideColumnVisibility?: VisibilityState;
  pinColumns?: ColumnPinningState; //Default column def is 150px, using pinning with modified size(column implementation) for better UI
  isResizeColumn?: boolean;
  getRowCanExpand?: (row: Row<TData>) => boolean;
  renderExpandComponent?: (props: { row: Row<TData> }) => ReactElement;
  renderExtraComponent?: Array<{
    /** Implement this is Array cause we might have to put multi extra things in the middle of component */
    position: 'top' | 'bottom';
    component: ReactElement;
  }>;
  renderExtraToolbarComponent?: Array<{
    // This is the extra component that will be rendered in the toolbar block
    position: 'left' | 'right';
    component: ReactElement;
  }>;
  alertInformation?: AlertBaseProps | null;
  emptyDataComponent?: ReactElement;
  onDoubleClickRow?: (record: TData) => void | Promise<void>;
  onClickRow?: (record: TData) => void | Promise<void>;
  translationKey?: FlatNamespace;
  searchInput?: {
    name: InputHTMLAttributes<HTMLInputElement>['name'];
    defaultValue?: InputHTMLAttributes<HTMLInputElement>['defaultValue'];
    placeholder?: InputHTMLAttributes<HTMLInputElement>['placeholder'];
    setSearchInputValue?: (
      newValue: InputHTMLAttributes<HTMLInputElement>['value'],
    ) => void;
    triggerMode?: TSearchInputTriggerMode;
    delayTime?: number;
    customTrigger?: (
      value: InputHTMLAttributes<HTMLInputElement>['value'],
    ) => void;
    inputClassName?: InputHTMLAttributes<HTMLInputElement>['className'];
    onChange?: InputHTMLAttributes<HTMLInputElement>['onChange'];
  };
  initialFilterDate?: {
    name: string;
    mode?: 'range-date' | 'month-year' | 'month'; // Default is range-date
    defaultValue?: {
      from: Date;
      to: Date;
    };
    showHourRangePicker?: boolean;
    initialHourRange?: {
      from: Date;
      to: Date;
    };
    minDate?: Date;
    maxDate?: Date;
  };
  initialFilters?: Array<{
    placeholder: string;
    options: OptionType[];
    name: string;
    value: string;
    className?: InputHTMLAttributes<HTMLInputElement>['className'];
  }>;
  initialSearchParams?: URLSearchParamsInit; // This helpful when we want to control UI with search params, implement this will make the reset button cleanup search params with keep the initial search params
  hideElements?: THideElements[];
}

//auto align center for column action
const ACTION_HEADER_ID = 'action';

declare module '@tanstack/react-table' {
  //allows us to define custom properties for our columns
  interface ColumnMeta<TData extends RowData, TValue> {
    filterVariant?: 'text' | 'range' | 'select' | 'date';
    filterOptions?: OptionType[];
    headerCellClassName?: HTMLAttributes<HTMLTableCellElement>['className'];
    dataCellClassName?: HTMLAttributes<HTMLTableCellElement>['className'];
    dateFilterOptions?: {
      defaultDateRange?: DateRangeProps;
      minDate?: Date;
      maxDate?: Date;
      formatTriggerText?: string;
    };
  }
}

/**
 * Columns definition for the table.
 * @type {ColumnDef<TData, TValue>[]}
 * @property {ColumnDef<TData, TValue>} action - The action column.
 * This column will be aligned to the center if its key or id is 'action'.
 */

/**
 * BTaskeeTableV2 is a generic table component that supports various features such as sorting, filtering, pagination, and column pinning.
 *
 * @template TData - The type of data for each row in the table.
 * @template TValue - The type of value for each column in the table.
 *
 * @param {DataTablePropsV2<TData, TValue>} props - The properties for configuring the table.
 * @param {ColumnDef<TData, TValue>[]} props.columns - The column definitions for the table.
 * @param {TData[]} props.data - The data to be displayed in the table.
 * @param {number} props.total - The total number of data entries.
 * @param {PaginationState} props.pagination - The pagination state for the table.
 * @param {VisibilityState} [props.overrideColumnVisibility] - Optional override for column visibility.
 * @param {ColumnPinningState} [props.pinColumns] - Optional column pinning configuration. Default column def is 150px, using pinning with modified size(column implementation) for better UI
 * @param {boolean} [props.isResizeColumn=true] - Whether columns can be resized.
 * @param {(row: Row<TData>) => boolean} [props.getRowCanExpand] - Function to determine if a row can be expanded.
 * @param {(props: { row: Row<TData> }) => ReactElement} [props.renderExpandComponent] - Component to render when a row is expanded.
 * @param {Array<{ position: 'top' | 'bottom'; component: ReactElement }>} [props.renderExtraComponent] - Extra components to render at the top or bottom of the table.
 * @param {Array<{ position: 'left' | 'right'; component: ReactElement }>} [props.renderExtraToolbarComponent] - Extra components to render in the toolbar.
 * @param {(record: TData) => void | Promise<void>} [props.onDoubleClickRow] - Callback for double-clicking a row.
 * @param {(record: TData) => void | Promise<void>} [props.onClickRow] - Callback for clicking a row.
 * @param {FlatNamespace} [props.translationKey='common'] - Translation key for i18n.
 * @param {object} [props.searchInput] - Configuration for the search input.
 * @param {object} [props.initialFilterDate] - Initial date filter configuration.
 * @param {Array<{ placeholder: string; options: OptionType[]; name: string; value: string; className?: string }>} [props.initialFilters] - Initial filters for the table.
 * @param {URLSearchParamsInit} [props.initialSearchParams] - Initial search parameters for the table.
 * @param {THideElements[]} [props.hideElements] - Elements to hide in the table UI.
 *
 * @returns {ReactElement} The rendered table component.
 */
const BTaskeeTableV2 = <TData, TValue>({
  data = [],
  total = 0,
  pagination = { pageIndex: 0, pageSize: 10 },
  columns = [],
  searchInput = undefined,
  initialFilterDate = undefined,
  initialFilters = undefined,
  overrideColumnVisibility = undefined,
  pinColumns = { left: [], right: [] },
  renderExtraComponent = undefined,
  alertInformation,
  onClickRow = undefined,
  onDoubleClickRow = undefined,
  renderExpandComponent = undefined,
  translationKey = 'common',
  initialSearchParams = {},
  isResizeColumn = true,
  getRowCanExpand = undefined,
  hideElements = [],
  renderExtraToolbarComponent = undefined,
  emptyDataComponent = undefined,
}: DataTablePropsV2<TData, TValue>) => {
  const [searchParams, setSearchParams] = useSearchParams();

  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const sortParams = searchParams.get('sort');

  const [sorting, setSorting] = useState<SortingState>([
    ...(sortParams
      ? sortParams.split(',').map(sort => {
          const [id, desc] = sort.split(':');
          return {
            id,
            desc: desc === 'desc',
          };
        })
      : []),
  ]);

  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  const getCommonPinningStyles = useCallback(
    (column: Column<TData>): CSSProperties => {
      const isPinned = column.getIsPinned();
      const isLastLeftPinnedColumn =
        isPinned === 'left' && column.getIsLastColumn('left');
      const isFirstRightPinnedColumn =
        isPinned === 'right' && column.getIsFirstColumn('right');

      return {
        boxShadow: isLastLeftPinnedColumn
          ? '-4px 0 4px -4px gray inset'
          : isFirstRightPinnedColumn
            ? '4px 0 4px -4px gray inset'
            : undefined,
        left: isPinned === 'left' ? `${column.getStart('left')}px` : undefined,
        right:
          isPinned === 'right' ? `${column.getAfter('right')}px` : undefined,
        opacity: isPinned ? 0.95 : 1,
        position: isPinned ? 'sticky' : 'relative',
        width: isPinned ? column.columnDef.size : column.getSize(),
        zIndex: isPinned ? 1 : 0,
        backgroundColor: isPinned ? 'white' : 'inherit',
        minWidth: isPinned ? column.columnDef.size : undefined,
        maxWidth: isPinned ? column.columnDef.size : undefined,
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
      };
    },
    [pinColumns],
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      ...(pinColumns
        ? {
            columnPinning: {
              left: pinColumns.left ?? [],
              right: pinColumns.right ?? [],
            },
          }
        : {}),
      columnVisibility: {
        ...columnVisibility,
        ...overrideColumnVisibility,
      },
      rowSelection,
      columnFilters,
      pagination,
    },
    rowCount: total,
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getRowCanExpand,
    getExpandedRowModel: getExpandedRowModel(),
    columnResizeMode: 'onEnd',
    columnResizeDirection: 'ltr',
  });

  useEffect(() => {
    if (sorting.length) {
      // e.g. "name:asc,age:desc" is the table behavior, but our table is only support one column sorting
      const sortValue = sorting
        .map(sort => {
          return `${sort.id}:${sort.desc ? 'desc' : 'asc'}`;
        })
        .join(',');

      const currentSortValue = searchParams.get('sort');
      if (currentSortValue !== sortValue) {
        setSearchParams(
          params => {
            params.set('sort', sortValue);
            return params;
          },
          {
            replace: true,
            preventScrollReset: true,
          },
        );
      }
    }
  }, [sorting, setSearchParams, searchParams]);

  return (
    <div
      className="space-y-4"
      style={{
        ...(isResizeColumn
          ? { direction: table.options.columnResizeDirection }
          : {}),
      }}>
      {!hideElements.includes('toolbar-filter') ? (
        <DataTableToolbarV2
          searchInput={searchInput}
          initialFilterDate={initialFilterDate}
          initialFilters={initialFilters}
          table={table}
          total={total}
          translationKey={translationKey}
          setSorting={setSorting}
          extraComponent={
            renderExtraComponent?.find(item => item.position === 'top')
              ?.component
          }
          initialSearchParams={initialSearchParams}
          renderExtraToolbarComponent={renderExtraToolbarComponent}
        />
      ) : null}
      {alertInformation ? (
        <AlertBase
          icon={alertInformation?.icon ?? ''}
          title={alertInformation?.title ?? ''}
          description={alertInformation?.description ?? ''}
          variant={alertInformation?.variant ?? 'default'}
        />
      ) : null}

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup: HeaderGroup<TData>) => (
              <TableRow className="bg-gray-100" key={headerGroup.id}>
                {headerGroup.headers.map((header, headerIndex) => {
                  const isChildOfGroupHeader = header.depth >= 2;
                  const isIncludeSubHeader = header.subHeaders.length > 0;
                  const isLastHeader =
                    headerIndex === headerGroup.headers.length - 1;
                  const isActionHeader = header.id === ACTION_HEADER_ID;
                  const isEnableColumnFilter =
                    header.column.columnDef.enableColumnFilter;

                  return (
                    <TableHead
                      style={{
                        ...(pinColumns
                          ? getCommonPinningStyles(header.column)
                          : {}),
                        ...(isResizeColumn ? { width: header.getSize() } : {}),
                      }}
                      key={header.id}
                      colSpan={header.colSpan}
                      className={cn(
                        isEnableColumnFilter ? 'py-2' : '',
                        !isLastHeader &&
                          (isChildOfGroupHeader || isIncludeSubHeader)
                          ? 'border-r border-gray-300'
                          : '',
                        isActionHeader ? 'text-center' : '',
                        isResizeColumn ? 'relative group' : '',
                        header.column.columnDef.meta?.headerCellClassName,
                      )}>
                      <div
                        className={
                          isEnableColumnFilter
                            ? 'flex items-center gap-0.5'
                            : ''
                        }>
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                        {isEnableColumnFilter ? (
                          <TableFilterOnColumn
                            column={header.column}
                            setSearchParams={setSearchParams}
                          />
                        ) : null}
                      </div>
                      {isResizeColumn &&
                        header.column.getCanResize() &&
                        !header.column.getIsPinned() && (
                          <div
                            title="Resize column (double click to reset size)"
                            onDoubleClick={() => header.column.resetSize()}
                            onMouseDown={header.getResizeHandler()}
                            onTouchStart={header.getResizeHandler()}
                            className={`absolute top-0 h-full w-1 bg-black/50 cursor-col-resize select-none touch-none right-0 opacity-0 hover:opacity-100 group-hover:opacity-100 ${
                              header.column.getIsResizing()
                                ? 'bg-blue-500 opacity-100'
                                : ''
                            }`}
                            style={{
                              transform: header.column.getIsResizing()
                                ? `translateX(${
                                    1 *
                                    (table.getState().columnSizingInfo
                                      .deltaOffset ?? 0)
                                  }px)`
                                : '',
                            }}
                          />
                        )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getCoreRowModel().rows.length ? (
              table.getCoreRowModel().rows.map(row => (
                <Fragment key={row.id}>
                  <TableRow
                    className={onDoubleClickRow ? 'cursor-pointer' : ''}
                    onDoubleClick={() => onDoubleClickRow?.(row.original)}
                    onClick={() => onClickRow?.(row.original)}
                    data-state={
                      row.getIsSelected() ? 'selected' : 'unselected'
                    }>
                    {row.getVisibleCells()?.map((cell: Cell<TData, TValue>) => {
                      const isActionCell = cell.column.id === ACTION_HEADER_ID;
                      return (
                        <TableCell
                          key={cell.id}
                          style={{
                            ...(pinColumns
                              ? getCommonPinningStyles(cell.column)
                              : {}),
                            ...(isResizeColumn
                              ? { width: cell.column.getSize() }
                              : {}),
                          }}
                          className={cn(
                            isActionCell ? 'text-center' : 'text-left',
                            cell.column.columnDef.meta?.dataCellClassName,
                          )}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext(),
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                  {row.getIsExpanded() && renderExpandComponent ? (
                    <TableRow>
                      <TableCell colSpan={row.getVisibleCells().length}>
                        {renderExpandComponent({ row })}
                      </TableCell>
                    </TableRow>
                  ) : null}
                </Fragment>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="text-center">
                  {emptyDataComponent ?? (
                    <VectorEmptyDataTable className="inline py-12" />
                  )}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {
        renderExtraComponent?.find(item => item.position === 'bottom')
          ?.component
      }

      <DataTablePagination table={table} />
    </div>
  );
};

export { BTaskeeTableV2 };

// Usage example for date range filtering on a column:
//
// const columns = [
//   {
//     id: 'created_at',
//     header: 'Created At',
//     accessorKey: 'created_at',
//     cell: ({ row }) => {
//       return momentTz(row.getValue('created_at')).format('DD/MM/YYYY');
//     },
//     enableColumnFilter: true,
//     meta: {
//       filterVariant: 'date',
//       dateFilterOptions: {
//         // Optional: Set a default date range. If not provided, no dates will be pre-selected.
//         // defaultDateRange: {
//         //   from: momentTz().subtract(7, 'days').toDate(),
//         //   to: momentTz().toDate(),
//         // },
//         formatTriggerText: 'DD/MM/YYYY',
//       },
//     },
//   },
//   // ... other columns
// ];
//
// UI Features:
// - Displays a small calendar icon next to the column header
// - Icon appears in blue background when a filter is active
// - Icon has hover states and subtle scale animation when clicked
// - Clicking the icon opens a polished popover with the date range picker
// - When a filter is active, the selected date range is shown in a blue header with a clear button
// - Hovering over the icon shows the selected date range as a tooltip
