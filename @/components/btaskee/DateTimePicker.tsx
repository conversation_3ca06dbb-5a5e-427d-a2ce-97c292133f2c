import { Grid } from '@/components/btaskee/Grid';
import { TimePicker } from '@/components/btaskee/TimePicker';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import {
  endOfDay,
  format,
  setHours,
  setMinutes,
  setSeconds,
  startOfDay,
} from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import React from 'react';
import { RegisterOptions, UseFormReturn } from 'react-hook-form';

/**
 * DatePickerProps interface for the DateTimePicker component.
 * @typedef {Object} DatePickerProps
 * @property {UseFormReturn<any>} form - The form object from react-hook-form.
 * @property {string} name - The name of the form field.
 * @property {boolean} [showTime] - Whether to show time picker or not.
 * @property {RegisterOptions} [rules] - Validation rules for the form field.
 * @property {string} [label] - Label for the date picker.
 * @property {string} [formatString] - Format string for date display.
 * @property {'default' | 'dayMonth'} [mode] - Mode of the date picker.
 * @property {boolean} [disabled] - Whether the date picker is disabled or not.
 */
export interface DatePickerProps {
  form: UseFormReturn<any>;
  name: string;
  showTime?: boolean;
  rules?: RegisterOptions;
  label?: string;
  formatString?: string;
  mode?: 'default' | 'dayMonth';
  disabled?: boolean;
}

/**
 * DateTimePicker component for selecting date and time range.
 *
 * @example
 * <DateTimePicker
 *   form={form}
 *   name="dateRange"
 *   showTime={true}
 *   label="Select Date Range"
 *   mode="default"
 *   disabled={false}
 * />
 *
 * @param {DatePickerProps} props - The props for the DateTimePicker component.
 * @returns {JSX.Element} The DateTimePicker component.
 */
const DateTimePicker = React.forwardRef<HTMLInputElement, DatePickerProps>(
  ({
    form,
    name,
    showTime,
    rules,
    label,
    formatString = 'LLL dd, y HH:mm:ss',
    mode = 'default',
    disabled = false,
  }) => {
    return (
      <FormField
        control={form.control}
        name={name}
        rules={rules}
        render={({ field }) => {
          const fromDate = field.value?.from;
          const endDate = field.value?.to;

          /**
           * Handles the selection of date range.
           * @param {Object} dateRange - The selected date range.
           */
          const handleSelect = (dateRange: any) => {
            const { from, to } = field.value || {};

            if (!dateRange && !showTime) {
              /**
               * @desc: dateRange will be undefined when the user clicks on the start date at second click, so we assume that the user wants to select the whole day
               */
              return field.onChange({
                from: startOfDay(from),
                to: endOfDay(from),
              });
            }

            if (dateRange.from) {
              // Set the time of the `from` date to start of the day (00:00:00)
              dateRange.from = startOfDay(dateRange.from);
              if (from) {
                dateRange.from = setHours(
                  setMinutes(dateRange.from, from.getMinutes()),
                  from.getHours(),
                );
              }
            }

            if (dateRange.to) {
              // Set the time of the `to` date to end of the day (23:59:59)
              dateRange.to = endOfDay(dateRange.to);
              if (to) {
                dateRange.to = setHours(
                  setMinutes(dateRange.to, to.getMinutes()),
                  to.getHours(),
                );
                dateRange.to = setSeconds(dateRange.to, 59);
              }
            } else {
              if (mode === 'dayMonth' || !showTime) {
                dateRange.to = endOfDay(dateRange.from);
              }
            }

            field.onChange(dateRange);
          };

          /**
           * Formats the date based on the mode.
           * @param {Date} date - The date to format.
           * @returns {string} The formatted date string.
           */
          const formatDate = (date: Date) => {
            if (mode === 'dayMonth') {
              return format(date, 'MMM dd');
            }
            return format(date, formatString);
          };

          return (
            <FormItem>
              <FormLabel className="text-gray-700">{label}</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="date"
                    variant={'outline'}
                    className={cn(
                      'w-full justify-start text-left font-normal disabled:cursor-not-allowed',
                      !fromDate && 'text-muted-foreground',
                    )}
                    disabled={disabled}>
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {fromDate ? (
                      endDate ? (
                        `${formatDate(fromDate)} - ${formatDate(endDate)}`
                      ) : (
                        format(fromDate, 'LLL dd, y') //Unknown previous reason set this, so I don't break it
                      )
                    ) : (
                      //TODO: Localize this text
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent align="end" className="w-auto p-0">
                  <Calendar
                    formatters={{
                      formatCaption: (date: Date) => {
                        if (mode === 'dayMonth') {
                          return format(date, 'MMM');
                        }
                        return format(date, 'LLLL y'); // Default format
                      },
                    }}
                    initialFocus
                    mode="range"
                    defaultMonth={fromDate}
                    selected={{
                      from: fromDate,
                      to: endDate,
                    }}
                    onSelect={handleSelect}
                    numberOfMonths={2}
                    ISOWeek={mode === 'dayMonth'}
                    fixedWeeks={mode === 'dayMonth'}
                  />
                  {showTime ? (
                    <Grid className="grid-cols-2 p-3 border-t border-border space-x-3">
                      <TimePicker
                        setDate={e => {
                          const timeUpdated = { from: e, to: endDate };
                          field.onChange(timeUpdated);
                        }}
                        date={fromDate}
                      />
                      <TimePicker
                        setDate={e => {
                          const timeUpdated = {
                            to: e,
                            from: fromDate,
                          };
                          field.onChange(timeUpdated);
                        }}
                        date={endDate}
                      />
                    </Grid>
                  ) : null}
                </PopoverContent>
              </Popover>
              <FormMessage />
            </FormItem>
          );
        }}
      />
    );
  },
);

DateTimePicker.displayName = 'DateTimePicker';

export { DateTimePicker };
