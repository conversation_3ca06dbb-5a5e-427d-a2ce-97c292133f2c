import { cn } from '@/lib/utils';
import { Link, useLocation } from '@remix-run/react';
import { createUID } from 'btaskee-utils';
import { ChevronDown } from 'lucide-react';
import { useTranslation } from 'react-i18next';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import {
  dropdownMenuTriggerStyle,
  navigationMenuItemTriggerStyle,
} from '../ui/navigation-menu';
import { Typography } from './Typography';

export interface NavigatorDropdownCommonProps {
  navigation: Array<NavigatorDropdownItem>;
  userPermissions: Array<BtaskeePermissions['key']>;
  visibleItems?: number;
}

const Translation = ({
  languageLabel,
  dotSign,
}: {
  languageLabel: string;
  dotSign?: boolean;
}) => {
  const { t } = useTranslation('common');
  return (
    <Typography variant="p" className="relative w-fit">
      {t(languageLabel)}
      {dotSign && (
        <span className="absolute -right-[6px] top-0 inline-block h-2 w-2 rounded-full bg-red-500" />
      )}
    </Typography>
  );
};

const NavigationLink = ({
  to,
  isActive,
  title,
  dot,
}: {
  to: string;
  isActive: boolean;
  title: string;
  dot?: boolean;
}) => (
  <Link
    to={to}
    className={cn(
      'relative inline-block',
      navigationMenuItemTriggerStyle(),
      isActive ? 'font-medium text-primary' : '',
    )}>
    <Translation languageLabel={title} dotSign={dot} />
  </Link>
);

const DropdownMenuItemLink = ({
  to,
  isActive,
  title,
  dot,
}: {
  to: string;
  isActive: boolean;
  title: string;
  dot?: boolean;
}) => (
  <DropdownMenuItem
    className={cn(
      dropdownMenuTriggerStyle(),
      'relative block p-0',
      isActive ? 'font-medium text-primary' : '',
    )}>
    <Link to={to} className="inline-block w-full p-2">
      <Translation languageLabel={title} dotSign={dot} />
    </Link>
  </DropdownMenuItem>
);

const NavigatorDropdownCommon = ({
  navigation,
  userPermissions,
  visibleItems = 5,
}: NavigatorDropdownCommonProps) => {
  const location = useLocation();
  const { t } = useTranslation('common');

  const isActiveMenuItem = (route?: string) => {
    if (!route) return false;
    return (
      location.pathname === route || location.pathname.startsWith(`${route}/`)
    );
  };

  const isActiveSection = (section: NavigatorDropdownItem): boolean => {
    if (section.route) return isActiveMenuItem(section.route);
    return !!section.routes?.some(route => isActiveSection(route));
  };

  const hasPermission = (permissions?: Array<BtaskeePermissions['key']>) => {
    if (!permissions) return true;
    return userPermissions.some(permission => permissions.includes(permission));
  };

  const isRouteHasDot = (route: NavigatorDropdownItem): boolean => {
    if (route.dot) return true;
    return !!route.routes?.some(subRoute => isRouteHasDot(subRoute));
  };

  const hasValidChildren = (routes: NavigatorDropdownItem[]): boolean => {
    return routes.some(route => {
      if (route.routes) {
        return hasValidChildren(route.routes);
      }
      return hasPermission(route.permissions);
    });
  };

  const renderSubRoutes = (
    routes: NavigatorDropdownItem[],
    parentIdx?: number,
  ) => {
    return routes.map((route, idx) => {
      if (route.routes) {
        if (!hasValidChildren(route.routes)) return null;

        return (
          <DropdownMenuSub key={createUID()}>
            <DropdownMenuSubTrigger
              className={cn(
                'p-2',
                dropdownMenuTriggerStyle(),
                isActiveSection(route) ? 'font-medium text-primary' : '',
              )}>
              <Translation languageLabel={route.title} dotSign={route.dot} />
            </DropdownMenuSubTrigger>
            <DropdownMenuPortal>
              <DropdownMenuSubContent className="ml-7 w-[300px] rounded-md px-6 py-4">
                <DropdownMenuGroup>
                  {renderSubRoutes(route.routes, idx)}
                </DropdownMenuGroup>
              </DropdownMenuSubContent>
            </DropdownMenuPortal>
          </DropdownMenuSub>
        );
      }

      if (!hasPermission(route.permissions)) return null;

      return (
        <DropdownMenuItemLink
          key={createUID()}
          to={route.route || '/'}
          isActive={isActiveMenuItem(route.route)}
          title={route.title}
          dot={route.dot}
        />
      );
    });
  };

  const mainNavigation = navigation.slice(0, visibleItems);
  const moreNavigation = navigation.slice(visibleItems);

  return (
    <ul className="group flex flex-1 items-center justify-center space-x-4">
      {mainNavigation.map(nav => {
        if (nav.routes) {
          if (!hasValidChildren(nav.routes)) return null;

          return (
            <DropdownMenu key={createUID()}>
              <DropdownMenuTrigger asChild>
                <div
                  className={cn(
                    navigationMenuItemTriggerStyle(),
                    'relative flex h-5 w-max cursor-pointer items-center justify-center',
                    isActiveSection(nav) ? 'font-medium text-primary' : '',
                  )}>
                  <Translation languageLabel={nav.title} dotSign={nav.dot} />
                  <ChevronDown className="chevron ml-1 h-3 w-3 transition-transform duration-300" />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[300px] rounded-md px-6 py-4"
                align="start">
                <DropdownMenuGroup>
                  {renderSubRoutes(nav.routes)}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          );
        }

        if (!hasPermission(nav.permissions)) return null;

        return (
          <NavigationLink
            key={createUID()}
            to={nav.route}
            isActive={isActiveMenuItem(nav.route)}
            title={nav.title}
            dot={nav.dot}
          />
        );
      })}

      {moreNavigation.length > 0 && hasValidChildren(moreNavigation) && (
        <DropdownMenu key={createUID()}>
          <DropdownMenuTrigger asChild>
            <div
              className={cn(
                navigationMenuItemTriggerStyle(),
                'relative flex h-5 w-max cursor-pointer items-center justify-center',
                moreNavigation.some(nav => isActiveSection(nav))
                  ? 'font-medium text-primary'
                  : '',
              )}>
              <Typography variant="p" className="relative w-fit">
                {t('MORE')}
                {moreNavigation.some(isRouteHasDot) && (
                  <span className="absolute -right-[6px] top-0 inline-block h-2 w-2 rounded-full bg-red-500" />
                )}
              </Typography>
              <ChevronDown className="chevron ml-1 h-3 w-3 transition-transform duration-300" />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[300px] rounded-md px-6 py-4"
            align="start">
            <DropdownMenuGroup>
              {renderSubRoutes(moreNavigation)}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </ul>
  );
};

export { NavigatorDropdownCommon };
