import ReactMarkdown from 'react-markdown';

interface ReadMarkdownOnlyProps {
  content: string;
  className?: string;
  isDetail?: boolean;
}

/**
 * Editor content is styled using https://github.com/tailwindlabs/tailwindcss-typography
 */
const ContentProseStyleClassNames =
  'prose prose-a:text-blue-600 prose-code:before:content-none prose-code:after:content-none';

//In case we want to present as detail, put prose class via props
const ReadMarkdownOnly = ({ content, className, isDetail = false }: ReadMarkdownOnlyProps) => {
  return <ReactMarkdown className={`${className ? className : ''} ${isDetail ? ContentProseStyleClassNames : ''}`}>{content}</ReactMarkdown>;
};

export { ReadMarkdownOnly, ContentProseStyleClassNames };