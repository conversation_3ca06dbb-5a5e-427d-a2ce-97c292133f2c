import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { CheckCheck, X } from 'lucide-react';
import React, { useState } from 'react';
import { type UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { ITEMS_LANGUAGE } from './MultiLanguageSectionView';

// This function retrieves a nested error from an error object based on a given path
function getNestedError(errors: any, path: string): any {
  // Split the path string into an array of keys
  // e.g., "name.en" becomes ["name", "en"]
  return path.split('.').reduce((acc, part) => {
    // For each part of the path, drill down into the error object
    // If at any point the path doesn't exist, it will return undefined
    return acc && acc[part];
  }, errors);
}

type MultiLanguageSectionProps = {
  form: UseFormReturn<any>;
  children: React.ReactElement[];
  childrenProps: { name: string; label: string; required?: string }[];
  parentField?: string;
  useNestedStructure?: boolean;
};

export function MultiLanguageSection({
  children,
  form,
  childrenProps,
  parentField,
  useNestedStructure = false,
}: MultiLanguageSectionProps) {
  const { t } = useTranslation('common');
  const { control, getValues, setValue, watch, formState, clearErrors } = form;
  const [visibility, setVisibility] = useState<{ [key: string]: boolean }>(() =>
    ITEMS_LANGUAGE.reduce((acc, lang) => ({ ...acc, [lang.value]: false }), {}),
  );

  const getFieldName = (childName: string, languageValue: string | number) => {
    if (useNestedStructure) {
      return parentField
        ? `${parentField}.${languageValue}.${childName}`
        : `${languageValue}.${childName}`;
    }
    return `${childName}.${languageValue}`;
  };

  const toggleVisibility = (languageValue: TLanguage) => {
    setVisibility(prevVisibility => ({
      ...prevVisibility,
      [languageValue]: !prevVisibility[languageValue],
    }));
  };

  watch(); // To trigger re-render when watch is called

  return (
    <Accordion type="multiple" className="border">
      {ITEMS_LANGUAGE.map((language, languageIndex) => {
        const isCompleted = React.Children.toArray(children).every(
          (child, childIndex) => {
            if (React.isValidElement(child)) {
              const fieldName = getFieldName(
                childrenProps[childIndex].name,
                language.value,
              );
              const fieldNameValues = getValues(fieldName);
              return fieldNameValues;
            }
            return false;
          },
        );

        const hasValue = React.Children.toArray(children).some(
          (child, childIndex) => {
            if (React.isValidElement(child)) {
              const fieldName = getFieldName(
                childrenProps[childIndex].name,
                language.value,
              );
              const fieldNameValues = getValues(fieldName);
              return !!fieldNameValues;
            }
            return false;
          },
        );

        const isRequired =
          !isCompleted &&
          React.Children.toArray(children).some((_, childIndex) => {
            const errorPath = getFieldName(
              childrenProps[childIndex].name,
              language.value,
            );
            const error = getNestedError(formState.errors, errorPath);
            return !!error?.message;
          });

        return (
          <AccordionItem
            key={`item-${languageIndex}`}
            value={`item-${languageIndex}`}>
            <AccordionTrigger
              key={language.value}
              className="bg-gray-100 p-4"
              onClick={() => toggleVisibility(language.value)}>
              <Label className="flex items-center gap-2">
                {language.icon}
                {language.label}
                {isCompleted && <CheckCheck className="text-green-500" />}
                {isRequired && <X className="text-red-500" />}
              </Label>
            </AccordionTrigger>
            <AccordionContent
              className="data-[state=open]: flex flex-col justify-between"
              forceMount
              hidden={!visibility[language.value]}>
              <div className="mb-6 ">
                {React.Children.map(children, (child, childIndex) => (
                  <div
                    className={cn('flex flex-col gap-4', {
                      'mb-4': childIndex < children.length - 1,
                    })}>
                    <FormField
                      name={getFieldName(
                        childrenProps[childIndex].name,
                        language.value,
                      )}
                      rules={{
                        ...(childrenProps[childIndex].required && {
                          required: childrenProps[childIndex].required,
                        }),
                      }}
                      control={control}
                      render={({ field: { onChange, value } }) => {
                        return (
                          <FormItem>
                            <FormLabel>
                              {childrenProps[childIndex].label}
                            </FormLabel>
                            <FormControl>
                              {React.cloneElement(child, { onChange, value })}
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        );
                      }}
                    />
                  </div>
                ))}
              </div>
              <div className="self-end">
                <Button
                  variant="outline"
                  type="button"
                  className={cn('font-medium', {
                    'bg-primary text-white': hasValue,
                  })}
                  onClick={() => {
                    childrenProps.forEach(childProp => {
                      const currentValues = getValues(
                        getFieldName(childProp.name, language.value),
                      );
                      ITEMS_LANGUAGE.forEach(lang => {
                        const langValue = getFieldName(
                          childProp.name,
                          lang.value,
                        );
                        if (lang.value !== language.value) {
                          setValue(langValue, currentValues);
                        }
                        if (currentValues !== '') {
                          clearErrors(langValue);
                        }
                      });
                    });
                  }}>
                  {t('APPLY_FOR_ALL')}
                </Button>
              </div>
            </AccordionContent>
          </AccordionItem>
        );
      })}
    </Accordion>
  );
}
