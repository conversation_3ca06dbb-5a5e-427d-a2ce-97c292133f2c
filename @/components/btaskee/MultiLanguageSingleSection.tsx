import { cn } from '@/lib/utils';
import type { AccordionSingleProps } from '@radix-ui/react-accordion';
import { <PERSON><PERSON><PERSON><PERSON>, Loader2, X } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { type UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '../ui/accordion';
import { Button } from '../ui/button';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/form';
import { ITEMS_LANGUAGE } from './MultiLanguageSectionView';
import { RichTextComponent } from './RichTextComponent';

// Global registry to track components with errors
const errorRegistry = {
  components: new Map<
    number,
    { hasError: boolean; ref: React.RefObject<HTMLDivElement> }
  >(),
  registerComponent: (order: number, ref: React.RefObject<HTMLDivElement>) => {
    errorRegistry.components.set(order, { hasError: false, ref });
  },
  unregisterComponent: (order: number) => {
    errorRegistry.components.delete(order);
  },
  setHasError: (order: number, hasError: boolean) => {
    const component = errorRegistry.components.get(order);
    if (component) {
      component.hasError = hasError;
    }
  },
  scrollToFirstError: () => {
    // Sort by order (ascending) and find the first with error
    const sortedComponents = Array.from(
      errorRegistry.components.entries(),
    ).sort(([orderA], [orderB]) => orderA - orderB);

    const firstWithError = sortedComponents.find(
      ([_, { hasError }]) => hasError,
    );

    if (firstWithError) {
      const [_, { ref }] = firstWithError;
      if (ref.current) {
        setTimeout(() => {
          ref.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }, 100);
        return true;
      }
    }
    return false;
  },
};

// This function retrieves a nested error from an error object based on a given path
function getNestedError(errors: any, path: string): any {
  if (!errors) return undefined;

  // Split the path string into an array of keys
  // e.g., "name.en" becomes ["name", "en"]
  return path.split('.').reduce((acc, part) => {
    // For each part of the path, drill down into the error object
    // If at any point the path doesn't exist, it will return undefined
    return acc && acc[part];
  }, errors);
}

type MultiLanguageSingleSectionProps = {
  form?: UseFormReturn<any>;
  data?: Record<string, Record<string, any>>;
  children: React.ReactElement | React.ReactElement[];
  childrenProps: {
    name: string;
    label: string;
    required?: string;
    layout?: 'full' | 'half';
    type?: 'richtext' | 'input';
  }[];
  parentField?: string;
  useNestedStructure?: boolean;
  order?: number;
} & Omit<AccordionSingleProps, 'collapsible' | 'type'> &
  React.RefAttributes<HTMLDivElement>;

/**
 * A component that provides multi-language form sections with accordion functionality.
 * Supports both form input mode and read-only display mode.
 *
 * @param {Object} props - Component props
 * @param {UseFormReturn<any>} [props.form] - React Hook Form instance for form handling
 * @param {Record<string, Record<string, any>>} [props.data] - Data for read-only mode
 * @param {React.ReactElement | React.ReactElement[]} props.children - Form input elements
 * @param {Object[]} props.childrenProps - Configuration for each child element
 * @param {string} props.childrenProps[].name - Field name
 * @param {string} props.childrenProps[].label - Field label
 * @param {string} [props.childrenProps[].required] - Required field validation message
 * @param {('full' | 'half')} [props.childrenProps[].layout] - Layout width configuration
 * @param {('richtext' | 'input')} [props.childrenProps[].type] - Input type
 * @param {string} [props.parentField] - Parent field name for nested structures
 * @param {boolean} [props.useNestedStructure=false] - Whether to use nested field naming
 * @param {string} [props.className] - Additional CSS classes for the accordion
 * @param {number} [props.order] - Scroll priority
 *
 * @returns {JSX.Element} A multi-language form section component
 */
export function MultiLanguageSingleSection({
  children: inputChildren,
  form,
  data,
  childrenProps,
  parentField,
  useNestedStructure = false,
  className: AccordionExpandClassName,
  order = Infinity,
  ...restAccordionProps
}: MultiLanguageSingleSectionProps) {
  const { t } = useTranslation('common');
  const isReadOnlyMode = !form && !!data;
  const [selectedLanguage, setSelectedLanguage] = useState(
    ITEMS_LANGUAGE[0].value,
  );
  const [isApplyingToAll, setIsApplyingToAll] = useState(false);
  const children: React.ReactElement[] = Array.isArray(inputChildren)
    ? inputChildren
    : [inputChildren];
  const accordionRef = useRef<HTMLDivElement>(null);
  const sectionRef = useRef<HTMLDivElement>(null);
  const formFieldRefs = useRef<Record<string, HTMLElement | null>>({});
  const [hasErrorState, setHasErrorState] = useState(false);
  const isHandlingLanguageChange = useRef(false);

  // Register this component with the global error registry
  useEffect(() => {
    errorRegistry.registerComponent(order, sectionRef);

    return () => {
      errorRegistry.unregisterComponent(order);
    };
  }, [order]);

  // Update error state in the registry when it changes
  useEffect(() => {
    errorRegistry.setHasError(order, hasErrorState);
  }, [hasErrorState, order]);

  const getFieldName = (childName: string, languageValue: string | number) => {
    if (useNestedStructure) {
      return parentField
        ? `${parentField}.${languageValue}.${childName}`
        : `${languageValue}.${childName}`;
    }
    return `${childName}.${languageValue}`;
  };

  // Get value based on mode
  const getValue = (fieldName: string, language: string) => {
    if (isReadOnlyMode) {
      return data?.[fieldName]?.[language] || '';
    }
    return form?.getValues(getFieldName(fieldName, language)) || '';
  };

  form?.watch(); // To trigger re-render when watch is called

  // Function to register form field refs for scrolling
  const registerFieldRef = (name: string, element: HTMLElement | null) => {
    formFieldRefs.current[name] = element;
  };

  // Scroll to field function - component specific scrolling
  const scrollToField = (fieldName: string, useGlobalScroll = false) => {
    // First try React Hook Form's setFocus
    if (form?.setFocus) {
      form.setFocus(fieldName);
    }

    // Skip global scrolling if we're handling a language change in this component
    if (isHandlingLanguageChange.current && !useGlobalScroll) {
      // Make sure this component's accordion is open
      if (accordionRef.current) {
        const accordionState = accordionRef.current.getAttribute('data-state');
        if (accordionState === 'closed') {
          const triggerElement = accordionRef.current.querySelector(
            '[data-state="closed"]',
          );
          if (triggerElement && triggerElement instanceof HTMLElement) {
            triggerElement.click();
          }
        }
      }

      // Then scroll to the specific field in this component
      setTimeout(() => {
        const fieldElement = formFieldRefs.current[fieldName];
        if (fieldElement) {
          fieldElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        } else if (sectionRef.current) {
          sectionRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      }, 150);

      return;
    }

    // Then manually scroll to the field element for better visibility
    setTimeout(() => {
      const fieldElement = formFieldRefs.current[fieldName];
      if (fieldElement) {
        // Make sure the accordion is open
        if (accordionRef.current) {
          // Force the accordion to open if it's closed
          const accordionState =
            accordionRef.current.getAttribute('data-state');
          if (accordionState === 'closed') {
            const triggerElement = accordionRef.current.querySelector(
              '[data-state="closed"]',
            );
            if (triggerElement && triggerElement instanceof HTMLElement) {
              triggerElement.click();
            }
          }

          // Wait for accordion animation to complete
          setTimeout(() => {
            // Scroll the element into view
            fieldElement.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
            });
          }, 100);
        } else {
          // If we can't find the accordion, just scroll to the element
          fieldElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        }
      } else if (sectionRef.current) {
        // If we can't find the specific field element, scroll to the section
        sectionRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
    }, 150);
  };

  const completionStatus = ITEMS_LANGUAGE.map(language => {
    if (isReadOnlyMode) {
      const isCompleted = React.Children.toArray(children).every(
        (_, childIndex) => {
          const fieldName = childrenProps[childIndex].name;
          return !!data?.[fieldName]?.[language.value];
        },
      );
      return { language, isCompleted, hasError: false };
    }

    const hasError = React.Children.toArray(children).some((_, childIndex) => {
      if (!childrenProps[childIndex].required) return false;

      const fieldName = getFieldName(
        childrenProps[childIndex].name,
        language.value,
      );
      const fieldError = getNestedError(form?.formState.errors, fieldName);
      const fieldValue = form?.getValues(fieldName);

      return (form?.formState.isSubmitted && !fieldValue) || !!fieldError;
    });

    const isCompleted = React.Children.toArray(children).every(
      (child, childIndex) => {
        if (React.isValidElement(child)) {
          const fieldName = getFieldName(
            childrenProps[childIndex].name,
            language.value,
          );
          const fieldValue = form?.getValues(fieldName);
          return !!fieldValue;
        }
        return false;
      },
    );

    return {
      language,
      isCompleted,
      hasError,
    };
  });

  // Update the component's error status based on all language statuses
  useEffect(() => {
    const hasAnyError = completionStatus.some(status => status.hasError);
    setHasErrorState(hasAnyError);
  }, [completionStatus]);

  const hasValue = React.Children.toArray(children).some(
    (child, childIndex) => {
      if (!React.isValidElement(child)) return false;

      const fieldName = childrenProps[childIndex].name;
      if (isReadOnlyMode) {
        return !!data?.[fieldName]?.[selectedLanguage];
      }
      return !!form?.getValues(getFieldName(fieldName, selectedLanguage));
    },
  );

  const validateAllLanguages = () => {
    if (isReadOnlyMode) return true;

    let hasErrors = false;
    let firstErrorField = '';

    for (const language of ITEMS_LANGUAGE) {
      for (const childProp of childrenProps) {
        if (childProp.required) {
          const fieldName = getFieldName(childProp.name, language.value);
          const value = form?.getValues(fieldName);

          if (!value) {
            form?.setError(fieldName, {
              type: 'required',
              message: childProp.required,
            });

            // Save first error field for focus
            if (!firstErrorField) {
              firstErrorField = fieldName;

              // If this is not the selected language, switch to it
              if (language.value !== selectedLanguage) {
                setSelectedLanguage(language.value);
              }
            }

            hasErrors = true;
          }
        }
      }
    }

    setHasErrorState(hasErrors);

    // Focus and scroll to the first error field in this component if needed
    // but only if we're the first component with errors in the global registry
    if (hasErrors && firstErrorField) {
      // Use setTimeout to ensure language state has updated before focusing
      setTimeout(() => {
        // Only scroll if we're the first component with an error
        const scrolledToFirstError = errorRegistry.scrollToFirstError();

        // If we've actually scrolled to a component with errors (not necessarily this one)
        // and if it's this component, then focus the field
        if (
          scrolledToFirstError &&
          errorRegistry.components.get(order)?.hasError
        ) {
          // Wait a bit more to ensure the component is visible
          setTimeout(() => {
            // Set focus to the first error field in this component
            if (form?.setFocus) {
              form.setFocus(firstErrorField);
            }

            // Open the accordion if needed
            if (accordionRef.current) {
              const accordionState =
                accordionRef.current.getAttribute('data-state');
              if (accordionState === 'closed') {
                const triggerElement = accordionRef.current.querySelector(
                  '[data-state="closed"]',
                );
                if (triggerElement && triggerElement instanceof HTMLElement) {
                  triggerElement.click();
                }
              }
            }
          }, 200);
        }
      }, 100);
    }

    return !hasErrors;
  };

  useEffect(() => {
    if (!isReadOnlyMode && form) {
      const handleBeforeSubmit = (e: Event) => {
        isHandlingLanguageChange.current = false; // Reset flag on submit
        const isValid = validateAllLanguages();
        if (!isValid) {
          e.preventDefault();
        }
      };

      // Use the capture phase to intercept the event before it reaches the form
      window.addEventListener('submit', handleBeforeSubmit, true);

      return () => {
        window.removeEventListener('submit', handleBeforeSubmit, true);
      };
    }
  }, [form, isReadOnlyMode]);

  const handleLanguageChange = async (language: any) => {
    if (selectedLanguage === language.value) {
      return;
    }

    // Mark that we're handling a language change in this component
    isHandlingLanguageChange.current = true;

    setSelectedLanguage(language.value);

    if (!isReadOnlyMode && form) {
      // Store current errors before validation to preserve them
      const currentErrors = form.formState.errors;

      const fieldsToValidate = childrenProps
        .filter(prop => prop.required)
        .map(prop => getFieldName(prop.name, language.value));

      // Don't clear errors on tab change, only if the field actually has a value
      fieldsToValidate.forEach(fieldName => {
        const fieldValue = form.getValues(fieldName);
        if (fieldValue) {
          form.clearErrors(fieldName);
        }
      });

      if (fieldsToValidate.length > 0) {
        // Validate new language fields without clearing other errors
        await form.trigger(fieldsToValidate);
      }

      if (form.formState.isSubmitted) {
        // Find first error field in the new language
        let firstErrorField = null;
        for (const childProp of childrenProps) {
          const fieldName = getFieldName(childProp.name, language.value);
          const fieldError = getNestedError(form?.formState.errors, fieldName);
          const fieldValue = form?.getValues(fieldName);

          if ((form?.formState.isSubmitted && !fieldValue) || !!fieldError) {
            firstErrorField = fieldName;
            break;
          }
        }

        if (firstErrorField) {
          // Scroll to the error field in THIS component only
          setTimeout(() => {
            scrollToField(firstErrorField, false);
          }, 100);
        }

        // Re-validate all language tabs to ensure errors are preserved
        // This makes sure we keep the error state for all tabs even when switching
        setTimeout(() => {
          childrenProps.forEach(childProp => {
            if (childProp.required) {
              ITEMS_LANGUAGE.forEach(lang => {
                const fieldName = getFieldName(childProp.name, lang.value);
                const fieldValue = form?.getValues(fieldName);

                if (!fieldValue && form?.formState.isSubmitted) {
                  form.setError(fieldName, {
                    type: 'required',
                    message: childProp.required,
                  });
                }
              });
            }
          });
        }, 50);
      }

      // Reset the flag after a delay
      setTimeout(() => {
        isHandlingLanguageChange.current = false;
      }, 500);
    }
  };

  const applyForAllLanguages = async () => {
    if (isReadOnlyMode) return;

    setIsApplyingToAll(true);
    try {
      await Promise.all(
        childrenProps.map(async childProp => {
          const currentValues =
            form?.getValues(getFieldName(childProp.name, selectedLanguage)) ||
            '';

          await Promise.all(
            ITEMS_LANGUAGE.map(async lang => {
              if (lang.value !== selectedLanguage) {
                const fieldName = getFieldName(childProp.name, lang.value);
                form?.setValue(fieldName, currentValues);
                if (currentValues !== '') {
                  form?.clearErrors(fieldName);
                  await form?.trigger(fieldName);
                }
              }
            }),
          );
        }),
      );
    } finally {
      setIsApplyingToAll(false);
    }
  };

  return (
    <div
      ref={sectionRef}
      data-multilanguage-section
      data-order={order}
      data-error={hasErrorState ? 'true' : 'false'}
      data-fields={childrenProps
        .map(prop =>
          useNestedStructure && parentField
            ? `${parentField}.${prop.name}`
            : prop.name,
        )
        .join(',')}>
      <Accordion
        type="single"
        ref={accordionRef}
        collapsible
        tabIndex={-1}
        defaultValue="content"
        {...restAccordionProps}
        className={cn(
          AccordionExpandClassName,
          'accordion-multilanguage rounded-lg border bg-white shadow-sm',
          hasErrorState && 'border-red-200',
        )}>
        <AccordionItem value="content" className="border-none">
          <AccordionTrigger
            className={cn(
              'bg-gray-100 px-4 py-3 hover:no-underline data-[state=closed]:rounded-lg data-[state=open]:rounded-t-lg',
              hasErrorState && 'bg-red-50',
            )}>
            <div className="flex w-full items-center justify-between">
              {completionStatus.map(({ language, isCompleted, hasError }) => (
                <Button
                  key={language.value}
                  type="button"
                  variant="ghost"
                  className={cn(
                    'flex h-auto items-center gap-2 p-1 px-2 transition-all duration-200 rounded-md',
                    selectedLanguage === language.value
                      ? 'bg-transparent underline decoration-primary decoration-2 underline-offset-4'
                      : 'hover:bg-gray-50',
                    hasError && 'text-red-500',
                  )}
                  onMouseDown={e => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onClick={e => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleLanguageChange(language);
                  }}>
                  <div className="flex-shrink-0 w-5 h-4">{language.icon}</div>
                  <span
                    className={cn(
                      'text-sm flex items-center',
                      selectedLanguage === language.value
                        ? 'font-medium text-primary'
                        : '',
                      hasError && 'text-red-500',
                      selectedLanguage === language.value &&
                        hasError &&
                        'text-red-600 font-medium',
                    )}>
                    {language.label}
                    {isCompleted && (
                      <CheckCheck className="ml-1 h-4 w-4 text-green-500 flex-shrink-0" />
                    )}
                    {hasError && (
                      <X
                        className={cn(
                          'ml-1 h-4 w-4 text-red-500 flex-shrink-0',
                          selectedLanguage === language.value &&
                            'animate-pulse',
                        )}
                      />
                    )}
                  </span>
                </Button>
              ))}
            </div>
          </AccordionTrigger>

          <AccordionContent className="p-4">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              {React.Children.map(children, (child, childIndex) => {
                const isFullWidth = childrenProps[childIndex].layout === 'full';
                const isRichText =
                  childrenProps[childIndex].type === 'richtext';
                const fieldName = childrenProps[childIndex].name;
                const currentFieldName = getFieldName(
                  fieldName,
                  selectedLanguage,
                );

                if (isReadOnlyMode) {
                  const value = getValue(fieldName, selectedLanguage);
                  return (
                    <div className={cn(isFullWidth && 'md:col-span-2')}>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          {childrenProps[childIndex].label}
                        </label>
                        {isRichText ? (
                          <RichTextComponent markdown={value} readOnly={true} />
                        ) : (
                          React.cloneElement(child, {
                            value,
                            disabled: true,
                            className: 'w-full',
                          })
                        )}
                      </div>
                    </div>
                  );
                }

                return (
                  <div
                    className={cn(isFullWidth && 'md:col-span-2')}
                    ref={el => registerFieldRef(currentFieldName, el)}>
                    <FormField
                      key={`${selectedLanguage}-${fieldName}`}
                      name={currentFieldName}
                      rules={{
                        ...(childrenProps[childIndex].required && {
                          required: childrenProps[childIndex].required,
                        }),
                      }}
                      control={form?.control}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {childrenProps[childIndex].label}
                          </FormLabel>
                          <FormControl>
                            {isRichText ? (
                              <RichTextComponent
                                markdown={field.value || ''}
                                onChange={field.onChange}
                              />
                            ) : (
                              React.cloneElement(child, {
                                onChange: field.onChange,
                                value: field.value ?? '',
                                onBlur: field.onBlur,
                              })
                            )}
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                );
              })}
            </div>

            {!isReadOnlyMode && (
              <div className="mt-6 flex justify-end">
                <Button
                  variant="outline"
                  type="button"
                  size="sm"
                  disabled={isApplyingToAll || !hasValue}
                  className={cn('font-medium transition-all duration-200', {
                    'bg-primary text-white hover:bg-primary/90': hasValue,
                  })}
                  onClick={applyForAllLanguages}>
                  {isApplyingToAll ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t('APPLYING')}
                    </>
                  ) : (
                    t('APPLY_FOR_ALL')
                  )}
                </Button>
              </div>
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
}
