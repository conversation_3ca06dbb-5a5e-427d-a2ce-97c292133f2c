// TODO: Must clean code
import { Typography } from '@/components/btaskee/Typography';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { momentTz } from 'btaskee-utils';
import { useEffect, useState } from 'react';

type HourOption = {
  label: string;
  hour: number;
  minute: number;
};

export const getRangeHourText = ({ hour, minute }: Omit<HourOption, 'label'>) =>
  `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

const generateTimeOptions = () => {
  const options: HourOption[] = [];
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      options.push({
        label: getRangeHourText({ hour, minute }),
        hour,
        minute,
      });
    }
  }
  return options;
};

export interface HourRangePickerProps {
  initialValue?: {
    from: Date;
    to: Date;
  };
  onValueChange: (timeRange: { from: Date; to: Date }) => void;
  startTimePlaceholder?: string;
  endTimePlaceholder?: string;
}

export const HourRangePicker: React.FC<HourRangePickerProps> = ({
  initialValue,
  onValueChange,
  startTimePlaceholder = 'Select start time',
  endTimePlaceholder = 'Select end time',
}: HourRangePickerProps) => {
  const getStringifyRangeHourInfo = (date: Date) =>
    JSON.stringify({
      label: getRangeHourText({
        hour: momentTz(date).get('hours'),
        minute: momentTz(date).get('minutes'),
      }),
      hour: momentTz(date).get('hours'),
      minute: momentTz(date).get('minutes'),
    });

  const [startTime, setStartTime] = useState<string | undefined>(
    initialValue?.from
      ? getStringifyRangeHourInfo(initialValue.from)
      : undefined,
  );

  const [endTime, setEndTime] = useState<string | undefined>(
    initialValue?.to ? getStringifyRangeHourInfo(initialValue.to) : undefined,
  );

  const hocChangeValueFromOutSide = ({
    start,
    end,
  }: {
    start?: string;
    end?: string;
  }) => {
    if (!start || !end) return;

    const parsedStartTime: HourOption = JSON.parse(start);
    const parsedEndTime: HourOption = JSON.parse(end);

    onValueChange?.({
      from: momentTz()
        .startOf('date')
        .add(parsedStartTime.hour, 'hours')
        .add(parsedStartTime.minute, 'minutes')
        .startOf('minutes')
        .toDate(),
      to: momentTz()
        .startOf('date')
        .add(parsedEndTime.hour, 'hours')
        .add(parsedEndTime.minute, 'minutes')
        .toDate(),
    });
  };

  useEffect(() => {
    if (initialValue) {
      const newStartTime = getStringifyRangeHourInfo(initialValue.from);
      const newEndTime = getStringifyRangeHourInfo(initialValue.to);
      setStartTime(newStartTime);
      setEndTime(newEndTime);
      hocChangeValueFromOutSide({ start: newStartTime, end: newEndTime });
    }
  }, []);

  return (
    <div className="flex flex-col sm:flex-row sm:space-x-4 space-y-4 sm:space-y-0">
      <div className="flex flex-col space-y-2 items-center justify-center">
        <Select
          value={startTime}
          onValueChange={start => {
            setStartTime(start);
            if (endTime) {
              hocChangeValueFromOutSide({
                start: start,
                end: endTime,
              });
            }
          }}>
          <SelectTrigger className="text-base" id="start-time">
            <SelectValue placeholder={startTimePlaceholder} />
          </SelectTrigger>
          <SelectContent className="max-h-[200px] overflow-y-auto">
            {generateTimeOptions().map(time => (
              <SelectItem
                disabled={
                  endTime
                    ? time.hour > JSON.parse(endTime).hour ||
                      (time.hour === JSON.parse(endTime).hour &&
                        time.minute >= JSON.parse(endTime).minute)
                    : false
                }
                key={`start-${time.label}`}
                value={JSON.stringify(time)}>
                {time.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <div className="flex items-center">
        <Typography variant="p" affects="removePMargin">
          -
        </Typography>
      </div>
      <div className="flex flex-col space-y-2 items-center justify-center">
        <Select
          value={endTime}
          onValueChange={end => {
            setEndTime(end);
            if (startTime) {
              hocChangeValueFromOutSide({
                start: startTime,
                end,
              });
            }
          }}>
          <SelectTrigger className="text-base" id="end-time">
            <SelectValue placeholder={endTimePlaceholder} />
          </SelectTrigger>
          <SelectContent className="max-h-[200px] overflow-y-auto">
            {generateTimeOptions().map(time => (
              <SelectItem
                disabled={
                  startTime
                    ? time.hour < JSON.parse(startTime).hour ||
                      (time.hour === JSON.parse(startTime).hour &&
                        time.minute <= JSON.parse(startTime).minute)
                    : false
                }
                key={`end-${time.label}`}
                value={JSON.stringify(time)}>
                {time.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};
