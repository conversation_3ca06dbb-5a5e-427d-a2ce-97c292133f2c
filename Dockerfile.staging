FROM node:18

WORKDIR /app

# Add SSH key to access private repositories
ARG SSH_PRIVATE_KEY
RUN mkdir -p /root/.ssh/ && \
    echo "$SSH_PRIVATE_KEY" > /root/.ssh/id_rsa && \
    chmod 600 /root/.ssh/id_rsa && \
    ssh-keyscan -t rsa gitlab.com >> /root/.ssh/known_hosts

COPY package*.json ./

# Install xdg-utils for xdg-open
RUN apt-get update && apt-get install -y xdg-utils

COPY . .

RUN npm install

EXPOSE 6006

CMD ["npm", "run", "storybook"]
