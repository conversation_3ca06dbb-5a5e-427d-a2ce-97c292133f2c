{"name": "btaskee-ui", "version": "1.3.77", "description": "bTaskee UI for backend operations", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.mts", "default": "./dist/index.js"}}}, "source": "./src/index.ts", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "files": ["dist", "README.md"], "scripts": {"build": "node build.mjs", "test": "echo \"Error: no test specified\" && exit 1", "prettier": "prettier --write .", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "type:check": "tsc", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "repository": {"type": "git", "url": "**************:btaskee/btaskee-ops/ui-components.git"}, "keywords": [], "author": "<PERSON><PERSON>ee", "license": "ISC", "peerDependencies": {"@hookform/error-message": "^2.0.1", "@mdxeditor/editor": "3.20.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-context-menu": "^2.1.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@remix-run/node": "^2.12.1", "@remix-run/react": "^2.7.2", "@storybook/addon-actions": "^8.3.2", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-table": "^8.13.2", "@tanstack/react-virtual": "^3.8.6", "@tanstack/table-core": "^8.20.5", "btaskee-constants": "git+ssh://**************:btaskee/btaskee-ops/constants.git#v0.0.89", "btaskee-hooks": "git+ssh://**************:btaskee/btaskee-ops/hooks.git#v1.0.5", "btaskee-types": "git+ssh://**************:btaskee/btaskee-ops/types.git#v1.8.82", "btaskee-utils": "git+ssh://**************:btaskee/btaskee-ops/utils.git#v1.0.26", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.0.0", "i18next": "^23.15.1", "input-otp": "^1.2.3", "lucide-react": "^0.418.0", "next-themes": "^0.3.0", "npm": "^10.8.3", "react": "^18.2.0", "react-day-picker": "^8.10.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.2", "react-i18next": "^14.0.5", "react-markdown": "^9.0.1", "react-resizable-panels": "^2.0.16", "react-router": "^6.26.2", "react-router-dom": "^6.26.2", "recharts": "^2.12.4", "sonner": "^1.4.41", "styled-components": "^6.1.8", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.6", "tailwindcss-animate": "^1.0.7", "tsconfig-paths-webpack-plugin": "^4.1.0", "use-resize-observer": "^9.1.0", "vaul": "^0.9.0", "zod": "^3.23.8"}, "devDependencies": {"@chromatic-com/storybook": "^1.6.1", "@hookform/resolvers": "^3.9.0", "@storybook/addon-essentials": "^8.2.4", "@storybook/addon-interactions": "^8.2.4", "@storybook/addon-links": "^8.2.4", "@storybook/addon-onboarding": "^8.2.4", "@storybook/addon-postcss": "^2.0.0", "@storybook/addon-webpack5-compiler-swc": "^1.0.4", "@storybook/blocks": "^8.2.4", "@storybook/react": "^8.2.4", "@storybook/react-webpack5": "^8.2.4", "@storybook/test": "^8.2.4", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/lodash": "^4.17.4", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@types/recharts": "^1.8.29", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^8.0.0", "autoprefixer": "^10.4.20", "cspell": "^8.6.0", "css-loader": "^7.1.2", "esbuild": "^0.21.4", "eslint": "^8.47.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-storybook": "^0.8.0", "husky": "^9.0.11", "postcss": "^8.0.0", "postcss-loader": "^7.0.0", "prettier": "3.2.5", "storybook": "^8.2.4", "storybook-addon-remix-react-router": "^3.0.0", "style-loader": "^4.0.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsup": "^8.1.0", "typescript": "^5.1.6"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "dependencies": {"@react-google-maps/api": "^2.20.6", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dropzone": "14.3.8", "react-image-crop": "11.0.10"}}